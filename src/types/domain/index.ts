type CallRecord = {
  datetime: Date;
  durationSeconds: number;
  from?: string;
  to?: string;
  callId: string;
}

type PhoneNumber = {
  number: string;
  label: string;
  type: string;
  active: boolean;
  inboundAssistant?: AssistantID;
  outboundAssistant?: AssistantID;
}

type AssistantID = {
  id: string;
  name: string;
}

type Assistant = {
  id: string;
  name?: string;
  type?: string;
  timezone?: string;
  voiceId?: string;
  patienceLevel?: string;
  stability?: number;
  voiceSpeed?: number;
  backchannelWords?: string[];
}

type KnowledgeBaseSource = {
  type: 'document' | 'url' | 'text';
  source_id?: string;
  name?: string;
  url?: string;
  text?: string;
}

type KnowledgeBase = {
  id: string;
  name: string;
  status: 'in_progress' | 'ready' | 'error';
  sources: KnowledgeBaseSource[];
  type: 'inventory' | 'process' | 'faq' | 'areas' | 'policies' | 'custom';
  custom_trigger?: string;
}

// Service-related types
type ServiceError = {
  message: string;
  code?: string;
  statusCode?: number;
}

type ServiceResponse<T> = {
  data?: T;
  error?: ServiceError;
  success: boolean;
}

// Backend configuration types
type BackendType = 'synapy-functions' | 'bolna'

export type{
  CallRecord,
  PhoneNumber,
  AssistantID,
  Assistant,
  KnowledgeBase,
  KnowledgeBaseSource,
  ServiceError,
  ServiceResponse,
  BackendType
}