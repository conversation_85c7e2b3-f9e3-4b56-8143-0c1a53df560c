/**
 * Dependency Injection Container for infrastructure-agnostic service management.
 * This container ensures all dependencies are properly injected and services are decoupled.
 */

import { DatabaseConfig, DatabaseFactory, DocumentDatabase, CacheDatabase } from '../interfaces/database'
import { MessagingConfig, MessagingFactory, MessagePublisher, MessageSubscriber, WhatsAppProvider, EmailProvider } from '../interfaces/messaging'
import { AIConfig, AIFactory, ChatProvider } from '../interfaces/ai'
import { CRMConfig, CRMFactory, ContactProvider, ActivityProvider } from '../interfaces/crm'

export interface ServiceConfig {
  database: {
    document: DatabaseConfig
    cache?: DatabaseConfig
  }
  messaging: {
    publisher: MessagingConfig
    subscriber: MessagingConfig
    whatsapp: MessagingConfig
    email?: MessagingConfig
  }
  ai: {
    chat: AIConfig
    analyzer: AIConfig
  }
  crm: {
    contacts: CRMConfig
    activities: CRMConfig
  }
  environment: 'development' | 'staging' | 'production'
  features: {
    caching: boolean
    analytics: boolean
    webhooks: boolean
  }
}

export interface ServiceRegistry {
  // Database services
  documentDatabase: DocumentDatabase
  cacheDatabase?: CacheDatabase

  // Messaging services
  messagePublisher: MessagePublisher
  messageSubscriber: MessageSubscriber
  whatsappProvider: WhatsAppProvider
  emailProvider?: EmailProvider

  // AI services
  chatProvider: ChatProvider
  // conversationAnalyzer removed - now handled by Analytics Service microservice

  // CRM services
  contactProvider: ContactProvider
  activityProvider: ActivityProvider
}

export class Container {
  private services: Partial<ServiceRegistry> = {}
  private factories: {
    database?: DatabaseFactory
    messaging?: MessagingFactory
    ai?: AIFactory
    crm?: CRMFactory
  } = {}

  constructor(private config: ServiceConfig) {}

  // Factory registration
  registerDatabaseFactory(factory: DatabaseFactory): void {
    this.factories.database = factory
  }

  registerMessagingFactory(factory: MessagingFactory): void {
    this.factories.messaging = factory
  }

  registerAIFactory(factory: AIFactory): void {
    this.factories.ai = factory
  }

  registerCRMFactory(factory: CRMFactory): void {
    this.factories.crm = factory
  }

  // Service initialization
  async initialize(): Promise<void> {
    await this.initializeDatabaseServices()
    await this.initializeMessagingServices()
    await this.initializeAIServices()
    await this.initializeCRMServices()

    // Connect all services
    await this.connectServices()
  }

  private async initializeDatabaseServices(): Promise<void> {
    if (!this.factories.database) {
      throw new Error('Database factory not registered')
    }

    this.services.documentDatabase = this.factories.database.createDocumentDatabase(this.config.database.document)

    if (this.config.database.cache && this.config.features.caching) {
      this.services.cacheDatabase = this.factories.database.createCacheDatabase(this.config.database.cache)
    }
  }

  private async initializeMessagingServices(): Promise<void> {
    if (!this.factories.messaging) {
      throw new Error('Messaging factory not registered')
    }

    this.services.messagePublisher = this.factories.messaging.createMessagePublisher(this.config.messaging.publisher)
    this.services.messageSubscriber = this.factories.messaging.createMessageSubscriber(this.config.messaging.subscriber)
    this.services.whatsappProvider = this.factories.messaging.createWhatsAppProvider(this.config.messaging.whatsapp)

    if (this.config.messaging.email) {
      this.services.emailProvider = this.factories.messaging.createEmailProvider(this.config.messaging.email)
    }
  }

  private async initializeAIServices(): Promise<void> {
    if (!this.factories.ai) {
      throw new Error('AI factory not registered')
    }

    this.services.chatProvider = this.factories.ai.createChatProvider(this.config.ai.chat)
    // conversationAnalyzer removed - now handled by Analytics Service microservice
  }

  private async initializeCRMServices(): Promise<void> {
    if (!this.factories.crm) {
      throw new Error('CRM factory not registered')
    }

    this.services.contactProvider = this.factories.crm.createContactProvider(this.config.crm.contacts)
    this.services.activityProvider = this.factories.crm.createActivityProvider(this.config.crm.activities)
  }

  private async connectServices(): Promise<void> {
    const connectionPromises: Promise<void>[] = []

    // Connect database services
    if (this.services.documentDatabase) {
      connectionPromises.push(this.services.documentDatabase.connect())
    }
    if (this.services.cacheDatabase) {
      connectionPromises.push(this.services.cacheDatabase.connect())
    }

    // Connect messaging services
    if (this.services.messagePublisher) {
      connectionPromises.push(this.services.messagePublisher.connect())
    }
    if (this.services.messageSubscriber) {
      connectionPromises.push(this.services.messageSubscriber.connect())
    }
    if (this.services.whatsappProvider) {
      connectionPromises.push(this.services.whatsappProvider.connect())
    }
    if (this.services.emailProvider) {
      connectionPromises.push(this.services.emailProvider.connect())
    }

    // Connect AI services
    if (this.services.chatProvider) {
      connectionPromises.push(this.services.chatProvider.connect())
    }
    // conversationAnalyzer connection removed - now handled by Analytics Service microservice

    // Connect CRM services
    if (this.services.contactProvider) {
      connectionPromises.push(this.services.contactProvider.connect())
    }
    if (this.services.activityProvider) {
      connectionPromises.push(this.services.activityProvider.connect())
    }

    await Promise.all(connectionPromises)
  }

  // Service getters
  getDocumentDatabase(): DocumentDatabase {
    if (!this.services.documentDatabase) {
      throw new Error('Document database not initialized')
    }
    return this.services.documentDatabase
  }

  getCacheDatabase(): CacheDatabase | undefined {
    return this.services.cacheDatabase
  }

  getMessagePublisher(): MessagePublisher {
    if (!this.services.messagePublisher) {
      throw new Error('Message publisher not initialized')
    }
    return this.services.messagePublisher
  }

  getMessageSubscriber(): MessageSubscriber {
    if (!this.services.messageSubscriber) {
      throw new Error('Message subscriber not initialized')
    }
    return this.services.messageSubscriber
  }

  getWhatsAppProvider(): WhatsAppProvider {
    if (!this.services.whatsappProvider) {
      throw new Error('WhatsApp provider not initialized')
    }
    return this.services.whatsappProvider
  }

  getEmailProvider(): EmailProvider | undefined {
    return this.services.emailProvider
  }

  getChatProvider(): ChatProvider {
    if (!this.services.chatProvider) {
      throw new Error('Chat provider not initialized')
    }
    return this.services.chatProvider
  }

  // getConversationAnalyzer removed - now handled by Analytics Service microservice

  getContactProvider(): ContactProvider {
    if (!this.services.contactProvider) {
      throw new Error('Contact provider not initialized')
    }
    return this.services.contactProvider
  }

  getActivityProvider(): ActivityProvider {
    if (!this.services.activityProvider) {
      throw new Error('Activity provider not initialized')
    }
    return this.services.activityProvider
  }

  // Health check
  async getHealthStatus(): Promise<HealthStatus> {
    const checks: HealthCheck[] = []

    // Check database services
    if (this.services.documentDatabase) {
      const health = await this.services.documentDatabase.getHealth()
      checks.push({ service: 'documentDatabase', ...health })
    }

    if (this.services.cacheDatabase) {
      const health = await this.services.cacheDatabase.getHealth()
      checks.push({ service: 'cacheDatabase', ...health })
    }

    // Check messaging services
    if (this.services.messagePublisher) {
      const health = await this.services.messagePublisher.getHealth()
      checks.push({ service: 'messagePublisher', ...health })
    }

    // Check AI services
    if (this.services.chatProvider) {
      const health = await this.services.chatProvider.getHealth()
      checks.push({ service: 'chatProvider', ...health })
    }

    // Check CRM services
    if (this.services.contactProvider) {
      const health = await this.services.contactProvider.getHealth()
      checks.push({ service: 'contactProvider', ...health })
    }

    const overallStatus = checks.every(check => check.status === 'healthy') ? 'healthy' :
                         checks.some(check => check.status === 'unhealthy') ? 'unhealthy' : 'degraded'

    return {
      status: overallStatus,
      checks,
      timestamp: new Date()
    }
  }

  // Cleanup
  async shutdown(): Promise<void> {
    const disconnectionPromises: Promise<void>[] = []

    Object.values(this.services).forEach(service => {
      if (service && 'disconnect' in service) {
        disconnectionPromises.push(service.disconnect())
      }
    })

    await Promise.all(disconnectionPromises)
  }
}

export interface HealthCheck {
  service: string
  status: 'healthy' | 'unhealthy' | 'degraded'
  latency?: number
  error?: string
  metadata?: Record<string, any>
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded'
  checks: HealthCheck[]
  timestamp: Date
}

// Singleton container instance
let containerInstance: Container | null = null

export function createContainer(config: ServiceConfig): Container {
  containerInstance = new Container(config)
  return containerInstance
}

export function getContainer(): Container {
  if (!containerInstance) {
    throw new Error('Container not initialized. Call createContainer() first.')
  }
  return containerInstance
}
