/**
 * AI and LLM abstraction interfaces for infrastructure-agnostic AI operations.
 * These interfaces ensure business logic is decoupled from specific AI providers.
 */

export interface AIProvider {
  connect(): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  getHealth(): Promise<AIHealth>
  getModels(): Promise<AIModel[]>
}

export interface AIHealth {
  status: 'healthy' | 'unhealthy' | 'degraded'
  latency?: number
  error?: string
  metadata?: Record<string, any>
}

export interface AIModel {
  id: string
  name: string
  provider: string
  type: 'text' | 'chat' | 'embedding' | 'image' | 'audio'
  maxTokens?: number
  costPer1kTokens?: number
  capabilities: string[]
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function'
  content: string
  name?: string
  functionCall?: FunctionCall
}

export interface FunctionCall {
  name: string
  arguments: string
}

export interface ChatCompletionRequest {
  model: string
  messages: ChatMessage[]
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stop?: string[]
  functions?: AIFunction[]
  functionCall?: 'auto' | 'none' | { name: string }
  stream?: boolean
}

export interface ChatCompletionResponse {
  id: string
  model: string
  choices: ChatChoice[]
  usage: TokenUsage
  created: Date
}

export interface ChatChoice {
  index: number
  message: ChatMessage
  finishReason: 'stop' | 'length' | 'function_call' | 'content_filter'
}

export interface TokenUsage {
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost?: number
}

export interface AIFunction {
  name: string
  description: string
  parameters: {
    type: 'object'
    properties: Record<string, any>
    required?: string[]
  }
}

export interface TextCompletionRequest {
  model: string
  prompt: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stop?: string[]
  stream?: boolean
}

export interface TextCompletionResponse {
  id: string
  model: string
  choices: TextChoice[]
  usage: TokenUsage
  created: Date
}

export interface TextChoice {
  index: number
  text: string
  finishReason: 'stop' | 'length' | 'content_filter'
}

export interface EmbeddingRequest {
  model: string
  input: string | string[]
  user?: string
}

export interface EmbeddingResponse {
  model: string
  data: EmbeddingData[]
  usage: TokenUsage
}

export interface EmbeddingData {
  index: number
  embedding: number[]
}

export interface ImageGenerationRequest {
  model: string
  prompt: string
  size?: '256x256' | '512x512' | '1024x1024'
  quality?: 'standard' | 'hd'
  style?: 'vivid' | 'natural'
  responseFormat?: 'url' | 'b64_json'
  user?: string
}

export interface ImageGenerationResponse {
  created: Date
  data: ImageData[]
}

export interface ImageData {
  url?: string
  b64Json?: string
  revisedPrompt?: string
}

export interface AudioTranscriptionRequest {
  model: string
  file: Buffer
  language?: string
  prompt?: string
  responseFormat?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt'
  temperature?: number
}

export interface AudioTranscriptionResponse {
  text: string
  language?: string
  duration?: number
  segments?: TranscriptionSegment[]
}

export interface TranscriptionSegment {
  id: number
  seek: number
  start: number
  end: number
  text: string
  tokens: number[]
  temperature: number
  avgLogprob: number
  compressionRatio: number
  noSpeechProb: number
}

export interface ChatProvider extends AIProvider {
  chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>
  streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<ChatCompletionResponse>
}

export interface TextProvider extends AIProvider {
  textCompletion(request: TextCompletionRequest): Promise<TextCompletionResponse>
  streamTextCompletion(request: TextCompletionRequest): AsyncIterable<TextCompletionResponse>
}

export interface EmbeddingProvider extends AIProvider {
  createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse>
}

export interface ImageProvider extends AIProvider {
  generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse>
}

export interface AudioProvider extends AIProvider {
  transcribeAudio(request: AudioTranscriptionRequest): Promise<AudioTranscriptionResponse>
}

// Conversation analysis interfaces removed - now handled by Analytics Service microservice

export interface AIConfig {
  type: 'openai' | 'anthropic' | 'google' | 'azure' | 'local'
  apiKey?: string
  baseUrl?: string
  model?: string
  options?: Record<string, any>
}

export interface AIFactory {
  createChatProvider(config: AIConfig): ChatProvider
  createTextProvider(config: AIConfig): TextProvider
  createEmbeddingProvider(config: AIConfig): EmbeddingProvider
  createImageProvider(config: AIConfig): ImageProvider
  createAudioProvider(config: AIConfig): AudioProvider
  // createConversationAnalyzer removed - now handled by Analytics Service microservice
}

// Error types
export class AIError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'AIError'
  }
}

export class AIRateLimitError extends AIError {
  constructor(message: string, public retryAfter?: number, originalError?: Error) {
    super(message, 'RATE_LIMIT_ERROR', originalError)
    this.name = 'AIRateLimitError'
  }
}

export class AIQuotaExceededError extends AIError {
  constructor(message: string, originalError?: Error) {
    super(message, 'QUOTA_EXCEEDED_ERROR', originalError)
    this.name = 'AIQuotaExceededError'
  }
}

export class AIModelNotFoundError extends AIError {
  constructor(message: string, originalError?: Error) {
    super(message, 'MODEL_NOT_FOUND_ERROR', originalError)
    this.name = 'AIModelNotFoundError'
  }
}
