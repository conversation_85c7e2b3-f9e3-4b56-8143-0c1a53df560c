/**
 * CRM abstraction interfaces for infrastructure-agnostic CRM operations.
 * These interfaces ensure business logic is decoupled from specific CRM implementations.
 */

export interface CRMProvider {
  connect(): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  getHealth(): Promise<CRMHealth>
  testConnection(): Promise<boolean>
}

export interface CRMHealth {
  status: 'healthy' | 'unhealthy' | 'degraded'
  latency?: number
  error?: string
  metadata?: Record<string, any>
}

export interface Contact {
  id: string
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  company?: string
  jobTitle?: string
  leadStatus?: string
  lifecycleStage?: string
  source?: string
  tags?: string[]
  customProperties?: Record<string, any>
  createdAt?: Date
  updatedAt?: Date
  lastActivityAt?: Date
}

export interface ContactSearchOptions {
  query?: string
  email?: string
  phone?: string
  company?: string
  leadStatus?: string[]
  lifecycleStage?: string[]
  tags?: string[]
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  properties?: string[]
}

export interface ContactUpdateData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  company?: string
  jobTitle?: string
  leadStatus?: string
  lifecycleStage?: string
  source?: string
  tags?: string[]
  customProperties?: Record<string, any>
}

export interface Deal {
  id: string
  name: string
  amount?: number
  currency?: string
  stage: string
  probability?: number
  closeDate?: Date
  contactId?: string
  companyId?: string
  ownerId?: string
  source?: string
  customProperties?: Record<string, any>
  createdAt?: Date
  updatedAt?: Date
}

export interface Company {
  id: string
  name: string
  domain?: string
  industry?: string
  size?: string
  phone?: string
  address?: Address
  customProperties?: Record<string, any>
  createdAt?: Date
  updatedAt?: Date
}

export interface Address {
  street?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
}

export interface Activity {
  id: string
  type: 'call' | 'email' | 'meeting' | 'note' | 'task' | 'whatsapp' | 'sms'
  subject?: string
  body?: string
  contactId?: string
  dealId?: string
  companyId?: string
  ownerId?: string
  direction?: 'inbound' | 'outbound'
  status?: 'completed' | 'scheduled' | 'cancelled'
  scheduledAt?: Date
  completedAt?: Date
  createdAt?: Date
  updatedAt?: Date
}

export interface Pipeline {
  id: string
  name: string
  stages: PipelineStage[]
  isDefault?: boolean
  createdAt?: Date
  updatedAt?: Date
}

export interface PipelineStage {
  id: string
  name: string
  order: number
  probability?: number
  isClosedWon?: boolean
  isClosedLost?: boolean
}

export interface LeadStatusOption {
  key: string
  label: string
  order?: number
  isDefault?: boolean
}

export interface ContactProvider extends CRMProvider {
  // Contact operations
  createContact(contact: Omit<Contact, 'id'>): Promise<Contact>
  getContact(id: string): Promise<Contact | null>
  updateContact(id: string, data: ContactUpdateData): Promise<Contact>
  deleteContact(id: string): Promise<void>
  
  // Contact search and listing
  searchContacts(options: ContactSearchOptions): Promise<Contact[]>
  getContactsByEmail(email: string): Promise<Contact[]>
  getContactsByPhone(phone: string): Promise<Contact[]>
  
  // Lead status operations
  getLeadStatusOptions(): Promise<LeadStatusOption[]>
  updateContactLeadStatus(contactId: string, leadStatus: string): Promise<void>
  
  // Bulk operations
  createContactsBatch(contacts: Omit<Contact, 'id'>[]): Promise<Contact[]>
  updateContactsBatch(updates: Array<{ id: string; data: ContactUpdateData }>): Promise<Contact[]>
}

export interface DealProvider extends CRMProvider {
  // Deal operations
  createDeal(deal: Omit<Deal, 'id'>): Promise<Deal>
  getDeal(id: string): Promise<Deal | null>
  updateDeal(id: string, data: Partial<Deal>): Promise<Deal>
  deleteDeal(id: string): Promise<void>
  
  // Deal search and listing
  getDeals(options?: { contactId?: string; companyId?: string; stage?: string; limit?: number }): Promise<Deal[]>
  
  // Pipeline operations
  getPipelines(): Promise<Pipeline[]>
  getPipelineStages(pipelineId: string): Promise<PipelineStage[]>
}

export interface CompanyProvider extends CRMProvider {
  // Company operations
  createCompany(company: Omit<Company, 'id'>): Promise<Company>
  getCompany(id: string): Promise<Company | null>
  updateCompany(id: string, data: Partial<Company>): Promise<Company>
  deleteCompany(id: string): Promise<void>
  
  // Company search
  searchCompanies(options: { query?: string; domain?: string; limit?: number }): Promise<Company[]>
}

export interface ActivityProvider extends CRMProvider {
  // Activity operations
  createActivity(activity: Omit<Activity, 'id'>): Promise<Activity>
  getActivity(id: string): Promise<Activity | null>
  updateActivity(id: string, data: Partial<Activity>): Promise<Activity>
  deleteActivity(id: string): Promise<void>
  
  // Activity listing
  getActivities(options: {
    contactId?: string
    dealId?: string
    companyId?: string
    type?: string[]
    limit?: number
    offset?: number
  }): Promise<Activity[]>
}

export interface WebhookProvider extends CRMProvider {
  // Webhook operations
  createWebhook(webhook: WebhookConfig): Promise<Webhook>
  getWebhooks(): Promise<Webhook[]>
  updateWebhook(id: string, data: Partial<WebhookConfig>): Promise<Webhook>
  deleteWebhook(id: string): Promise<void>
}

export interface WebhookConfig {
  url: string
  events: string[]
  active?: boolean
  secret?: string
}

export interface Webhook extends WebhookConfig {
  id: string
  createdAt?: Date
  updatedAt?: Date
}

export interface CRMConfig {
  type: 'hubspot' | 'salesforce' | 'pipedrive' | 'zoho' | 'custom'
  apiKey?: string
  baseUrl?: string
  clientId?: string
  clientSecret?: string
  accessToken?: string
  refreshToken?: string
  options?: Record<string, any>
}

export interface CRMFactory {
  createContactProvider(config: CRMConfig): ContactProvider
  createDealProvider(config: CRMConfig): DealProvider
  createCompanyProvider(config: CRMConfig): CompanyProvider
  createActivityProvider(config: CRMConfig): ActivityProvider
  createWebhookProvider(config: CRMConfig): WebhookProvider
}

// Error types
export class CRMError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'CRMError'
  }
}

export class CRMAuthenticationError extends CRMError {
  constructor(message: string, originalError?: Error) {
    super(message, 'AUTHENTICATION_ERROR', originalError)
    this.name = 'CRMAuthenticationError'
  }
}

export class CRMRateLimitError extends CRMError {
  constructor(message: string, public retryAfter?: number, originalError?: Error) {
    super(message, 'RATE_LIMIT_ERROR', originalError)
    this.name = 'CRMRateLimitError'
  }
}

export class CRMNotFoundError extends CRMError {
  constructor(message: string, originalError?: Error) {
    super(message, 'NOT_FOUND_ERROR', originalError)
    this.name = 'CRMNotFoundError'
  }
}

export class CRMValidationError extends CRMError {
  constructor(message: string, public validationErrors?: Record<string, string[]>, originalError?: Error) {
    super(message, 'VALIDATION_ERROR', originalError)
    this.name = 'CRMValidationError'
  }
}
