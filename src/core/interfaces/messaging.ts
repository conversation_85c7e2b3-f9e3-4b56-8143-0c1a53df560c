/**
 * Messaging and communication abstraction interfaces.
 * These interfaces ensure business logic is decoupled from specific messaging implementations.
 */

export interface MessageQueue {
  connect(): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  getHealth(): Promise<QueueHealth>
}

export interface QueueHealth {
  status: 'healthy' | 'unhealthy' | 'degraded'
  queueCount?: number
  error?: string
  metadata?: Record<string, any>
}

export interface Message<T = any> {
  id: string
  data: T
  attributes?: Record<string, string>
  publishTime?: Date
  deliveryAttempt?: number
}

export interface PublishOptions {
  delay?: number // seconds
  priority?: number
  retryPolicy?: RetryPolicy
  attributes?: Record<string, string>
}

export interface RetryPolicy {
  maxAttempts: number
  backoffMultiplier?: number
  maxBackoffSeconds?: number
  minBackoffSeconds?: number
}

export interface SubscriptionOptions {
  maxMessages?: number
  ackDeadlineSeconds?: number
  enableMessageOrdering?: boolean
  filter?: string
}

export interface MessagePublisher extends MessageQueue {
  publish<T>(topic: string, data: T, options?: PublishOptions): Promise<string>
  publishBatch<T>(topic: string, messages: Array<{ data: T; options?: PublishOptions }>): Promise<string[]>
}

export interface MessageSubscriber extends MessageQueue {
  subscribe<T>(
    subscription: string,
    handler: MessageHandler<T>,
    options?: SubscriptionOptions
  ): Promise<void>
  unsubscribe(subscription: string): Promise<void>
}

export interface MessageHandler<T = any> {
  (message: Message<T>): Promise<void>
}

export interface WebSocketConnection {
  id: string
  isConnected: boolean
  send<T>(data: T): Promise<void>
  close(): Promise<void>
  onMessage<T>(handler: (data: T) => void): void
  onClose(handler: () => void): void
  onError(handler: (error: Error) => void): void
}

export interface WebSocketServer {
  start(port: number): Promise<void>
  stop(): Promise<void>
  broadcast<T>(data: T): Promise<void>
  sendToConnection<T>(connectionId: string, data: T): Promise<void>
  getConnections(): WebSocketConnection[]
  onConnection(handler: (connection: WebSocketConnection) => void): void
  onDisconnection(handler: (connectionId: string) => void): void
}

export interface SMSProvider {
  sendMessage(to: string, body: string, options?: SMSOptions): Promise<SMSResult>
  sendBatch(messages: SMSMessage[]): Promise<SMSResult[]>
  getMessageStatus(messageId: string): Promise<SMSStatus>
  validatePhoneNumber(phoneNumber: string): Promise<PhoneValidation>
}

export interface SMSOptions {
  from?: string
  mediaUrls?: string[]
  statusCallback?: string
  maxPrice?: number
  validityPeriod?: number
}

export interface SMSMessage {
  to: string
  body: string
  options?: SMSOptions
}

export interface SMSResult {
  messageId: string
  status: SMSStatus
  cost?: number
  error?: string
}

export interface SMSStatus {
  status: 'queued' | 'sending' | 'sent' | 'delivered' | 'failed' | 'undelivered'
  errorCode?: string
  errorMessage?: string
  dateCreated?: Date
  dateUpdated?: Date
}

export interface PhoneValidation {
  isValid: boolean
  phoneNumber?: string
  countryCode?: string
  carrier?: string
  lineType?: 'mobile' | 'landline' | 'voip'
}

export interface WhatsAppProvider extends SMSProvider {
  sendTemplate(to: string, templateName: string, parameters: any[], options?: SMSOptions): Promise<SMSResult>
  sendMedia(to: string, mediaUrl: string, caption?: string, options?: SMSOptions): Promise<SMSResult>
  markAsRead(messageId: string): Promise<void>
}

export interface EmailProvider {
  sendEmail(email: EmailMessage): Promise<EmailResult>
  sendBatch(emails: EmailMessage[]): Promise<EmailResult[]>
  getEmailStatus(messageId: string): Promise<EmailStatus>
  validateEmail(email: string): Promise<EmailValidation>
}

export interface EmailMessage {
  to: string | string[]
  from: string
  subject: string
  body: string
  isHtml?: boolean
  cc?: string[]
  bcc?: string[]
  attachments?: EmailAttachment[]
  replyTo?: string
}

export interface EmailAttachment {
  filename: string
  content: Buffer | string
  contentType?: string
}

export interface EmailResult {
  messageId: string
  status: EmailStatus
  error?: string
}

export interface EmailStatus {
  status: 'queued' | 'sending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'failed' | 'bounced' | 'spam'
  dateCreated?: Date
  dateUpdated?: Date
}

export interface EmailValidation {
  isValid: boolean
  email?: string
  domain?: string
  disposable?: boolean
  role?: boolean
}

export interface MessagingConfig {
  type: 'pubsub' | 'sqs' | 'rabbitmq' | 'twilio' | 'sendgrid'
  connectionString?: string
  projectId?: string
  credentials?: any
  options?: Record<string, any>
}

export interface MessagingFactory {
  createMessagePublisher(config: MessagingConfig): MessagePublisher
  createMessageSubscriber(config: MessagingConfig): MessageSubscriber
  createWebSocketServer(config: MessagingConfig): WebSocketServer
  createSMSProvider(config: MessagingConfig): SMSProvider
  createWhatsAppProvider(config: MessagingConfig): WhatsAppProvider
  createEmailProvider(config: MessagingConfig): EmailProvider
}

// Error types
export class MessagingError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'MessagingError'
  }
}

export class MessageDeliveryError extends MessagingError {
  constructor(message: string, originalError?: Error) {
    super(message, 'DELIVERY_ERROR', originalError)
    this.name = 'MessageDeliveryError'
  }
}

export class ConnectionError extends MessagingError {
  constructor(message: string, originalError?: Error) {
    super(message, 'CONNECTION_ERROR', originalError)
    this.name = 'ConnectionError'
  }
}
