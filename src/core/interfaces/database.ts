/**
 * Database abstraction interfaces for infrastructure-agnostic data access.
 * These interfaces ensure business logic is decoupled from specific database implementations.
 */

export interface DatabaseConnection {
  connect(): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  getHealth(): Promise<DatabaseHealth>
}

export interface DatabaseHealth {
  status: 'healthy' | 'unhealthy' | 'degraded'
  latency?: number
  error?: string
  metadata?: Record<string, any>
}

export interface QueryOptions {
  limit?: number
  offset?: number
  orderBy?: Array<{ field: string; direction: 'asc' | 'desc' }>
  filters?: Array<{ field: string; operator: FilterOperator; value: any }>
}

export type FilterOperator = 
  | 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' 
  | 'in' | 'not-in' | 'contains' | 'starts-with' | 'ends-with'

export interface DatabaseTransaction {
  commit(): Promise<void>
  rollback(): Promise<void>
  get<T>(collection: string, id: string): Promise<T | null>
  set<T>(collection: string, id: string, data: T): Promise<void>
  update<T>(collection: string, id: string, data: Partial<T>): Promise<void>
  delete(collection: string, id: string): Promise<void>
}

export interface DocumentDatabase extends DatabaseConnection {
  // Document operations
  get<T>(collection: string, id: string): Promise<T | null>
  set<T>(collection: string, id: string, data: T): Promise<void>
  update<T>(collection: string, id: string, data: Partial<T>): Promise<void>
  delete(collection: string, id: string): Promise<void>
  
  // Collection operations
  query<T>(collection: string, options?: QueryOptions): Promise<T[]>
  count(collection: string, options?: QueryOptions): Promise<number>
  
  // Batch operations
  batch(): DatabaseBatch
  
  // Transactions
  transaction<T>(operation: (tx: DatabaseTransaction) => Promise<T>): Promise<T>
  
  // Subcollections
  subcollection(parentCollection: string, parentId: string, subcollection: string): SubcollectionOperations
}

export interface DatabaseBatch {
  set<T>(collection: string, id: string, data: T): DatabaseBatch
  update<T>(collection: string, id: string, data: Partial<T>): DatabaseBatch
  delete(collection: string, id: string): DatabaseBatch
  commit(): Promise<void>
}

export interface SubcollectionOperations {
  get<T>(id: string): Promise<T | null>
  set<T>(id: string, data: T): Promise<void>
  update<T>(id: string, data: Partial<T>): Promise<void>
  delete(id: string): Promise<void>
  query<T>(options?: QueryOptions): Promise<T[]>
  add<T>(data: T): Promise<string>
}

export interface CacheDatabase extends DatabaseConnection {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttlSeconds?: number): Promise<void>
  delete(key: string): Promise<void>
  exists(key: string): Promise<boolean>
  increment(key: string, amount?: number): Promise<number>
  expire(key: string, ttlSeconds: number): Promise<void>
  
  // Hash operations
  hget<T>(key: string, field: string): Promise<T | null>
  hset<T>(key: string, field: string, value: T): Promise<void>
  hgetall<T>(key: string): Promise<Record<string, T>>
  hdel(key: string, field: string): Promise<void>
  
  // List operations
  lpush<T>(key: string, ...values: T[]): Promise<number>
  rpush<T>(key: string, ...values: T[]): Promise<number>
  lpop<T>(key: string): Promise<T | null>
  rpop<T>(key: string): Promise<T | null>
  lrange<T>(key: string, start: number, stop: number): Promise<T[]>
}

export interface DatabaseConfig {
  type: 'firestore' | 'postgres' | 'mongodb' | 'redis'
  connectionString?: string
  projectId?: string
  credentials?: any
  options?: Record<string, any>
}

export interface DatabaseFactory {
  createDocumentDatabase(config: DatabaseConfig): DocumentDatabase
  createCacheDatabase(config: DatabaseConfig): CacheDatabase
}

// Error types
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export class DatabaseConnectionError extends DatabaseError {
  constructor(message: string, originalError?: Error) {
    super(message, 'CONNECTION_ERROR', originalError)
    this.name = 'DatabaseConnectionError'
  }
}

export class DatabaseTransactionError extends DatabaseError {
  constructor(message: string, originalError?: Error) {
    super(message, 'TRANSACTION_ERROR', originalError)
    this.name = 'DatabaseTransactionError'
  }
}

export class DatabaseValidationError extends DatabaseError {
  constructor(message: string, originalError?: Error) {
    super(message, 'VALIDATION_ERROR', originalError)
    this.name = 'DatabaseValidationError'
  }
}
