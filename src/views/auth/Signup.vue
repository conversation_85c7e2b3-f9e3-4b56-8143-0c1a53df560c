<script setup lang="ts">
import Logo from '@/layouts/full/logo/Logo.vue';
import SignupForm from '@/components/auth/SignupForm.vue';
</script>
<template>
    <div class="authentication">
        <v-container fluid class="pa-3">
            <v-row class="h-100vh d-flex justify-center align-center">
                <v-col cols="12" lg="4" xl="3" class="d-flex align-center">
                    <v-card rounded="md" elevation="10" class="px-sm-1 px-0 withbg mx-auto" max-width="500">
                        <v-card-item class="pa-sm-8">
                            <div class="d-flex justify-center py-4 mx-12">
                                <Logo />
                            </div>
                            <div class="text-body-1 text-muted text-center mb-3">Empieza a crear Asistentes de voz para tu negocio</div>
                            <SignupForm />
                            <h6 class="text-h6 text-muted font-weight-bold d-flex justify-center align-center mt-3">
                                ¿Ya tienes una cuenta?
                                <RouterLink to="/auth/login"
                                    class="text-primary text-decoration-none text-body-1 opacity-1 font-weight-medium pl-2">
                                    Login</RouterLink>
                            </h6>
                        </v-card-item>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </div>
</template>
