<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { fetchAssistants } from '@/services/assistantService'
import { getChatService, type ChatMessage } from '@/services/chatService'
import { useAuthStore } from '@/store/useAuthStore'
import type { Assistant } from '@/types/domain'
import { Icon } from '@iconify/vue'

const router = useRouter()
const authStore = useAuthStore()
const chatService = getChatService()

// State
const assistants = ref<Assistant[]>([])
const selectedAssistant = ref<Assistant | null>(null)
const loading = ref(true)
const connecting = ref(false)
const connected = ref(false)
const messages = ref<ChatMessage[]>([])
const newMessage = ref('')
const chatContainer = ref<HTMLElement>()

const hasAssistants = computed(() => assistants.value.length > 0)

// Load assistants on mount
onMounted(async () => {
  await loadAssistants()
})

async function loadAssistants() {
  try {
    loading.value = true
    const userId = authStore.user?.uid
    if (!userId) return

    const result = await fetchAssistants(userId)
    if (result) {
      assistants.value = result
      if (result.length > 0) {
        selectedAssistant.value = result[0]
      }
    }
  } catch (error) {
    console.error('Error loading assistants:', error)
  } finally {
    loading.value = false
  }
}

async function connectToAssistant() {
  if (!selectedAssistant.value?.id) return

  connecting.value = true
  messages.value = []

  try {
    // Set up event handlers
    chatService.onConnect(() => {
      connecting.value = false
      connected.value = true
      addMessage('¡Conexión establecida! Puedes empezar a chatear con tu asistente.', 'assistant')
    })

    chatService.onMessage((message: string) => {
      addMessage(message, 'assistant')
    })

    chatService.onDisconnect(() => {
      connected.value = false
      connecting.value = false
      addMessage('Conexión cerrada.', 'assistant')
    })

    chatService.onError((error: Error) => {
      console.error('Chat service error:', error)
      connecting.value = false
      connected.value = false
      addMessage('Error de conexión. Verifica que el asistente esté disponible.', 'assistant')
    })

    // Connect to the assistant
    await chatService.connect(selectedAssistant.value.id)
  } catch (error) {
    console.error('Error connecting to assistant:', error)
    connecting.value = false
    addMessage('Error al conectar con el asistente.', 'assistant')
  }
}

function disconnectFromAssistant() {
  chatService.disconnect()
  connected.value = false
}

function sendMessage() {
  if (!newMessage.value.trim() || !connected.value) return

  const message = newMessage.value.trim()
  addMessage(message, 'user')

  try {
    // Send message through chat service
    chatService.sendMessage(message)
  } catch (error) {
    console.error('Error sending message:', error)
    addMessage('Error al enviar el mensaje.', 'assistant')
  }

  newMessage.value = ''
}

function addMessage(text: string, sender: 'user' | 'assistant') {
  messages.value.push({
    id: Date.now().toString(),
    text,
    sender,
    timestamp: new Date()
  })

  nextTick(() => {
    scrollToBottom()
  })
}

function scrollToBottom() {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

function goToAssistants() {
  router.push({ name: 'assistants' })
}
</script>

<template>
  <v-container fluid class="pa-0 d-flex flex-row fill-height h-screen justify-center">
    <div v-if="loading">
      <v-progress-circular indeterminate size="64" />
    </div>

    <!-- Empty state when no assistants -->
    <div v-else-if="!hasAssistants" class="d-flex flex-column justify-center align-center fill-height text-center mt-12">
      <Icon icon="solar:chat-round-broken" class="mb-4 text-medium-emphasis" width="80" />
      <h2 class="text-h5 font-weight-bold mb-2">No hay asistentes disponibles</h2>
      <p class="text-body-1 text-medium-emphasis mb-6">
        Necesitas crear al menos un asistente antes de poder probarlo
      </p>
      <v-btn color="primary" @click="goToAssistants">
        Crear Asistente
      </v-btn>
    </div>

    <!-- Main chat interface -->
    <div v-else class="d-flex flex-column fill-height mt-12">
      <!-- Header with assistant selector -->
      <v-card class="mb-4" elevation="1">
        <v-card-text class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <Icon icon="solar:chat-round-broken" class="mr-3 text-primary" width="24" />
            <div>
              <h3 class="text-h6 font-weight-bold">Prueba de Asistente</h3>
              <p class="text-body-2 text-medium-emphasis mb-0">
                Selecciona un asistente y comienza a chatear
              </p>
            </div>
          </div>

          <div class="d-flex align-center gap-3">
            <v-select
              v-model="selectedAssistant"
              :items="assistants"
              item-title="name"
              item-value="id"
              return-object
              label="Seleccionar asistente"
              variant="outlined"
              density="compact"
              style="min-width: 200px"
              :disabled="connected"
            />

            <v-btn
              v-if="!connected"
              color="primary"
              :loading="connecting"
              :disabled="!selectedAssistant"
              @click="connectToAssistant"
            >
              Conectar
            </v-btn>

            <v-btn
              v-else
              color="error"
              variant="outlined"
              @click="disconnectFromAssistant"
            >
              Desconectar
            </v-btn>
          </div>
        </v-card-text>
      </v-card>

      <!-- Chat area -->
      <v-card class="flex-grow-1 d-flex flex-column mb-12" elevation="1">
        <!-- Messages container -->
        <div
          ref="chatContainer"
          class="flex-grow-1 pa-4 overflow-y-auto"
          style="max-height: calc(100vh - 300px)"
        >
          <div v-if="messages.length === 0" class="d-flex justify-center align-center fill-height">
            <div class="text-center text-medium-emphasis">
              <Icon icon="solar:chat-dots-broken" width="48" class="mb-2" />
              <p>Los mensajes aparecerán aquí</p>
            </div>
          </div>

          <div v-else class="d-flex flex-column gap-3">
            <div
              v-for="message in messages"
              :key="message.id"
              class="d-flex"
              :class="message.sender === 'user' ? 'justify-end' : 'justify-start'"
            >
              <div
                class="message-bubble pa-3 rounded-lg"
                :class="{
                  'user-message': message.sender === 'user',
                  'assistant-message': message.sender === 'assistant'
                }"
                style="max-width: 70%"
              >
                <p class="mb-1">{{ message.text }}</p>
                <small class="text-caption opacity-70">
                  {{ message.timestamp.toLocaleTimeString() }}
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Message input -->
        <v-divider />
        <div class="pa-4">
          <v-text-field
            v-model="newMessage"
            placeholder="Escribe tu mensaje..."
            variant="outlined"
            density="comfortable"
            :disabled="!connected"
            @keyup.enter="sendMessage"
            append-inner-icon="mdi-send"
            @click:append-inner="sendMessage"
          />
        </div>
      </v-card>
    </div>
  </v-container>
</template>

<style scoped>
.message-bubble {
  word-wrap: break-word;
}

.user-message {
  background-color: rgb(var(--v-theme-primary));
  color: white;
}

.assistant-message {
  background-color: rgb(var(--v-theme-surface-variant));
  color: rgb(var(--v-theme-on-surface-variant));
}

.gap-3 {
  gap: 12px;
}
</style>
