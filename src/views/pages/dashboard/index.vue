<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAssistantsStore, useContactsStore } from '@/store'
import { Icon } from '@iconify/vue'

const router = useRouter()
const assistantsStore = useAssistantsStore()
const contactsStore = useContactsStore()

// Mock data for now - will be replaced with real API calls
const stats = ref({
  leads: 25,
  conversations: 12,
  roi: '368%'
})

const recentConversations = ref([
  { id: 1, contact: '<PERSON>', time: '2 hours ago', status: 'completed' },
  { id: 2, contact: '<PERSON>', time: '4 hours ago', status: 'in-progress' },
  { id: 3, contact: '<PERSON>', time: '1 day ago', status: 'completed' },
  { id: 4, contact: '<PERSON>', time: '2 days ago', status: 'completed' }
])

const integrationStatus = ref({
  hubspot: { connected: true, status: 'Connected' }
})

const assistantsCount = computed(() => assistantsStore.assistants.length)
const contactsCount = computed(() => contactsStore.contacts.length)

function navigateToAssistants() {
  router.push('/my-assistants')
}

function navigateToMarketplace() {
  router.push('/marketplace')
}

function navigateToIntegrations() {
  router.push('/integrations')
}

function createNewAssistant() {
  // This will be handled by the assistants store
  router.push('/my-assistants')
}

onMounted(() => {
  assistantsStore.fetchAllAssistants()
  contactsStore.initialize()
})
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-h4 font-weight-bold mb-2">Hello, Juan 👋</h1>
      <p class="text-body-1 text-medium-emphasis">Welcome back to your dashboard</p>
    </div>

    <!-- Stats Cards -->
    <v-row class="mb-8">
      <v-col cols="12" md="4">
        <v-card class="pa-6 text-center" elevation="2">
          <div class="text-h3 font-weight-bold text-primary mb-2">{{ stats.leads }}</div>
          <div class="text-body-1 text-medium-emphasis">Leads</div>
        </v-card>
      </v-col>
      <v-col cols="12" md="4">
        <v-card class="pa-6 text-center" elevation="2">
          <div class="text-h3 font-weight-bold text-success mb-2">{{ stats.conversations }}</div>
          <div class="text-body-1 text-medium-emphasis">Conversations</div>
        </v-card>
      </v-col>
      <v-col cols="12" md="4">
        <v-card class="pa-6 text-center" elevation="2">
          <div class="text-h3 font-weight-bold text-warning mb-2">{{ stats.roi }}</div>
          <div class="text-body-1 text-medium-emphasis">ROI</div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Quick Actions -->
    <v-row class="mb-8">
      <v-col cols="12" md="6">
        <v-btn
          color="primary"
          size="large"
          variant="elevated"
          class="w-100"
          @click="createNewAssistant"
        >
          <Icon icon="mdi-plus" class="mr-2" />
          Create Assistant
        </v-btn>
      </v-col>
      <v-col cols="12" md="6">
        <v-btn
          color="secondary"
          size="large"
          variant="outlined"
          class="w-100"
          @click="navigateToMarketplace"
        >
          <Icon icon="mdi-store" class="mr-2" />
          View Marketplace
        </v-btn>
      </v-col>
    </v-row>

    <!-- Content Grid -->
    <v-row>
      <!-- Recent Conversations -->
      <v-col cols="12" lg="8">
        <v-card elevation="2">
          <v-card-title class="d-flex align-center justify-space-between">
            <span>Recent Conversations</span>
            <v-btn variant="text" size="small" @click="router.push('/conversations')">
              View All
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item
                v-for="conversation in recentConversations"
                :key="conversation.id"
                class="px-0"
              >
                <template #prepend>
                  <v-avatar size="40" color="primary">
                    <Icon icon="mdi-account" />
                  </v-avatar>
                </template>
                <v-list-item-title>{{ conversation.contact }}</v-list-item-title>
                <v-list-item-subtitle>{{ conversation.time }}</v-list-item-subtitle>
                <template #append>
                  <v-chip
                    :color="conversation.status === 'completed' ? 'success' : 'warning'"
                    size="small"
                    variant="tonal"
                  >
                    {{ conversation.status }}
                  </v-chip>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Integration Status -->
      <v-col cols="12" lg="4">
        <v-card elevation="2" class="mb-4">
          <v-card-title>Integration Status</v-card-title>
          <v-card-text>
            <div class="d-flex align-center justify-space-between mb-4">
              <div class="d-flex align-center">
                <Icon icon="simple-icons:hubspot" class="mr-3" color="orange" size="24" />
                <span>HubSpot</span>
              </div>
              <v-chip color="success" size="small" variant="tonal">
                <v-icon start icon="mdi-check-circle"></v-icon>
                Connected
              </v-chip>
            </div>
            <v-btn
              variant="outlined"
              size="small"
              class="w-100"
              @click="navigateToIntegrations"
            >
              Manage Integrations
            </v-btn>
          </v-card-text>
        </v-card>

        <!-- Quick Stats -->
        <v-card elevation="2">
          <v-card-title>Quick Stats</v-card-title>
          <v-card-text>
            <div class="d-flex justify-space-between mb-3">
              <span>My Assistants</span>
              <span class="font-weight-bold">{{ assistantsCount }}</span>
            </div>
            <div class="d-flex justify-space-between mb-3">
              <span>Total Contacts</span>
              <span class="font-weight-bold">{{ contactsCount }}</span>
            </div>
            <div class="d-flex justify-space-between">
              <span>Active Integrations</span>
              <span class="font-weight-bold">1</span>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.w-100 {
  width: 100%;
}
</style>
