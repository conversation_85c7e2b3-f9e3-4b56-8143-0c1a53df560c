<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

</script>

<template>
    <v-container class="d-flex flex-row fill-height h-screen">
    <div class="d-flex flex-column justify-center align-center text-center flex-grow-1 mt-n16">
      <div class="mb-10">
        <h1 class="text-h5 font-weight-bold mb-2">HOLA</h1>
        <p class="text-body-1 text-medium-emphasis">
          Selecciona las integraciones que quieres activar para tu asistente
        </p>
      </div>

      <v-row class="w-100 justify-center" no-gutters>
      </v-row>
    </div>
  </v-container>
</template>
