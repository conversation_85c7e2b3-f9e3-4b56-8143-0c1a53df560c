<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useContactsStore } from '@/store'

const router = useRouter()
const contactsStore = useContactsStore()

// Form state
const loading = ref(false)
const saving = ref(false)
const testingConnection = ref(false)
const fetchingContacts = ref(false)

const form = reactive({
  apiKey: '',
  portalId: '',
  accessToken: '',
  refreshToken: '',
  leadPipeline: '',
  dealPipeline: '',
  contactOwner: '',
  autoCreateContacts: true,
  autoCreateDeals: false,
  syncActivities: true,
  enabled: false
})

// Load existing configuration on mount
onMounted(async () => {
  loading.value = true
  try {
    await contactsStore.loadHubSpotConfig()

    // Populate form with existing config
    if (contactsStore.hubspotConfig) {
      const config = contactsStore.hubspotConfig

      // Explicitly map each field to ensure proper assignment
      form.apiKey = config.apiKey || ''
      form.portalId = config.portalId || ''
      form.accessToken = config.accessToken || ''
      form.refreshToken = config.refreshToken || ''
      form.leadPipeline = config.leadPipeline || ''
      form.dealPipeline = config.dealPipeline || ''
      form.contactOwner = config.contactOwner || ''
      form.autoCreateContacts = config.autoCreateContacts ?? true
      form.autoCreateDeals = config.autoCreateDeals ?? false
      form.syncActivities = config.syncActivities ?? true
      form.enabled = config.enabled ?? false
    } else {
      console.log('📭 No HubSpot configuration found to populate form')
    }
  } catch (error) {
    console.error('Error loading HubSpot configuration:', error)
  } finally {
    loading.value = false
  }
})

const formRules = {
  apiKey: [
    (v: string) => !!v || 'API Key es requerido'
  ],
  portalId: [
    (v: string) => !!v || 'Portal ID es requerido',
    (v: string) => /^\d+$/.test(v) || 'Portal ID debe ser numérico'
  ]
}

// Mock data for dropdowns
const pipelines = [
  { title: 'Sales Pipeline', value: 'sales' },
  { title: 'Marketing Pipeline', value: 'marketing' },
  { title: 'Customer Success', value: 'customer-success' }
]

const owners = [
  { title: 'Juan Carlos González', value: 'juan.gonzalez' },
  { title: 'David Merino', value: 'david.merino' },
  { title: 'David Ríos', value: 'david.rios' }
]

async function saveConfiguration() {
  try {
    saving.value = true
    contactsStore.clearError()

    // Save configuration using contacts store
    await contactsStore.saveHubSpotConfig({ ...form })

    // Show success message
    console.log('HubSpot configuration saved successfully')

    // Redirect back to integrations
    router.push({ name: 'integrations' })
  } catch (error) {
    console.error('Error saving HubSpot configuration:', error)
  } finally {
    saving.value = false
  }
}

async function testConnection() {
  try {
    testingConnection.value = true
    contactsStore.clearError()

    // Test connection by fetching contacts
    await contactsStore.fetchHubSpotContacts()

    console.log('HubSpot connection test successful')
    console.log(`Fetched ${contactsStore.hubspotContacts.length} contacts`)
  } catch (error) {
    console.error('HubSpot connection test failed:', error)
  } finally {
    testingConnection.value = false
  }
}

async function fetchContacts() {
  try {
    fetchingContacts.value = true
    contactsStore.clearError()

    await contactsStore.fetchHubSpotContacts()
  } catch (error) {
    console.error('Error fetching HubSpot contacts:', error)
  } finally {
    fetchingContacts.value = false
  }
}

function goBack() {
  router.push({ name: 'integrations' })
}
</script>

<template>
  <v-container class="pa-6">
    <!-- Header -->
    <div class="d-flex align-center mb-6">
      <v-btn
        icon="mdi-arrow-left"
        variant="text"
        @click="goBack"
        class="mr-3"
      />
      <div class="d-flex align-center">
        <Icon icon="solar:users-group-rounded-broken" class="mr-3 text-primary" width="32" />
        <div>
          <h1 class="text-h4 font-weight-bold">Integración HubSpot</h1>
          <p class="text-body-1 text-medium-emphasis mb-0">
            Gestiona leads, contactos y actividades de ventas automáticamente
          </p>
        </div>
      </div>
    </div>

    <v-row>
      <v-col cols="12" md="8">
        <v-card elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:key-broken" class="mr-2" width="20" />
            Credenciales de API
          </v-card-title>

          <v-card-text>
            <v-form @submit.prevent="saveConfiguration">
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="form.apiKey"
                    label="API Key"
                    type="password"
                    placeholder="pat-na1-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                    variant="outlined"
                    :rules="formRules.apiKey"
                    required
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="form.portalId"
                    label="Portal ID"
                    placeholder="12345678"
                    variant="outlined"
                    :rules="formRules.portalId"
                    required
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="form.accessToken"
                    label="Access Token (Opcional)"
                    type="password"
                    placeholder="Para OAuth 2.0"
                    variant="outlined"
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>

        <v-card elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:settings-broken" class="mr-2" width="20" />
            Configuración de Pipelines
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-select
                  v-model="form.leadPipeline"
                  :items="pipelines"
                  label="Pipeline de Leads"
                  variant="outlined"
                  hint="Pipeline donde se crearán los nuevos leads"
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-select
                  v-model="form.dealPipeline"
                  :items="pipelines"
                  label="Pipeline de Deals"
                  variant="outlined"
                  hint="Pipeline para oportunidades de venta"
                />
              </v-col>

              <v-col cols="12">
                <v-select
                  v-model="form.contactOwner"
                  :items="owners"
                  label="Propietario por Defecto"
                  variant="outlined"
                  hint="Usuario que será asignado como propietario de nuevos contactos"
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <v-card elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:automation-broken" class="mr-2" width="20" />
            Automatización
          </v-card-title>

          <v-card-text>
            <div class="d-flex flex-column gap-3">
              <v-switch
                v-model="form.autoCreateContacts"
                label="Crear contactos automáticamente"
                color="primary"
                inset
                hint="Crear nuevos contactos cuando no existan en HubSpot"
              />

              <v-switch
                v-model="form.autoCreateDeals"
                label="Crear deals automáticamente"
                color="primary"
                inset
                hint="Crear oportunidades de venta para leads calificados"
              />

              <v-switch
                v-model="form.syncActivities"
                label="Sincronizar actividades"
                color="primary"
                inset
                hint="Registrar llamadas y conversaciones como actividades"
              />

              <v-switch
                v-model="form.enabled"
                label="Habilitar integración"
                color="primary"
                inset
              />
            </div>

            <v-divider class="my-4" />

            <div class="d-flex gap-3">
              <v-btn
                color="primary"
                :loading="saving"
                :disabled="testingConnection"
                @click="saveConfiguration"
              >
                Guardar Configuración
              </v-btn>

              <v-btn
                variant="outlined"
                :loading="testingConnection"
                :disabled="saving || !form.apiKey || !form.portalId"
                @click="testConnection"
              >
                Probar Conexión
              </v-btn>

              <v-btn
                variant="outlined"
                color="success"
                :loading="fetchingContacts"
                :disabled="saving || testingConnection || !form.enabled"
                @click="fetchContacts"
              >
                Obtener Contactos
              </v-btn>
            </div>
          </v-card-text>
        </v-card>

        <!-- Contacts Preview -->
        <v-card v-if="contactsStore.hubspotContacts.length > 0" elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:users-group-rounded-broken" class="mr-2" width="20" />
            Contactos HubSpot ({{ contactsStore.hubspotContacts.length }})
          </v-card-title>

          <v-card-text>
            <v-list density="compact">
              <v-list-item
                v-for="contact in contactsStore.hubspotContacts.slice(0, 5)"
                :key="contact.id"
                class="px-0"
              >
                <template #prepend>
                  <v-avatar size="32" color="primary">
                    <span class="text-caption">{{ contact.name.charAt(0) }}</span>
                  </v-avatar>
                </template>

                <v-list-item-title class="text-body-2">
                  {{ contact.name }}
                </v-list-item-title>

                <v-list-item-subtitle class="text-caption">
                  {{ contact.phone }} • {{ contact.company }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>

            <v-btn
              v-if="contactsStore.hubspotContacts.length > 5"
              variant="text"
              size="small"
              @click="router.push({ name: 'whatsapp' })"
              class="mt-2"
            >
              Ver todos los contactos ({{ contactsStore.hubspotContacts.length }})
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="4">
        <v-card elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:info-circle-broken" class="mr-2" width="20" />
            Configuración
          </v-card-title>

          <v-card-text>
            <div class="mb-4">
              <h4 class="font-weight-bold mb-2">¿Cómo obtener las credenciales?</h4>
              <ol class="text-body-2">
                <li class="mb-1">Ve a HubSpot → Settings → Integrations</li>
                <li class="mb-1">Selecciona "Private Apps"</li>
                <li class="mb-1">Crea una nueva aplicación privada</li>
                <li class="mb-1">Copia el Access Token generado</li>
                <li class="mb-1">El Portal ID está en la URL de tu cuenta</li>
              </ol>
            </div>

            <v-btn
              variant="outlined"
              size="small"
              href="https://app.hubspot.com/private-apps"
              target="_blank"
              block
            >
              Crear App Privada
            </v-btn>
          </v-card-text>
        </v-card>

        <v-card elevation="1" class="mb-4">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:list-check-broken" class="mr-2" width="20" />
            Funcionalidades
          </v-card-title>

          <v-card-text>
            <ul class="text-body-2">
              <li class="mb-2">✅ Creación automática de contactos</li>
              <li class="mb-2">✅ Gestión de leads y oportunidades</li>
              <li class="mb-2">✅ Registro de actividades de llamadas</li>
              <li class="mb-2">✅ Actualización de propiedades</li>
              <li class="mb-2">✅ Asignación de propietarios</li>
              <li class="mb-2">✅ Workflows automáticos</li>
            </ul>
          </v-card-text>
        </v-card>

        <v-card elevation="1">
          <v-card-title class="d-flex align-center">
            <Icon icon="solar:shield-check-broken" class="mr-2" width="20" />
            Permisos Requeridos
          </v-card-title>

          <v-card-text>
            <v-alert
              type="info"
              variant="tonal"
              density="compact"
            >
              <small>
                Tu aplicación privada necesita permisos para:
                Contacts, Deals, Companies, y Timeline Events
              </small>
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.gap-3 {
  gap: 12px;
}
</style>
