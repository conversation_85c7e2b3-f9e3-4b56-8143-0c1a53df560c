<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useContactsStore } from '@/store'
import { Icon } from '@iconify/vue'

const router = useRouter()
const contactsStore = useContactsStore()

const integrations = ref([
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'Manage leads, contacts and sales activities automatically',
    icon: 'simple-icons:hubspot',
    status: 'available',
    connected: false,
    features: ['Contact Management', 'Lead Tracking', 'Sales Pipeline', 'Activity Sync']
  },
  {
    id: 'zapier',
    name: 'Zapier',
    description: 'Connect with 5000+ apps through Zapier automation',
    icon: 'simple-icons:zapier',
    status: 'coming-soon',
    connected: false,
    features: ['Workflow Automation', '5000+ App Integrations', 'Custom Triggers']
  },
  {
    id: 'salesforce',
    name: 'Salesforce',
    description: 'Integrate with Salesforce CRM for enterprise sales management',
    icon: 'simple-icons:salesforce',
    status: 'coming-soon',
    connected: false,
    features: ['CRM Integration', 'Lead Management', 'Opportunity Tracking']
  },
  {
    id: 'calendly',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Schedule appointments and meetings automatically',
    icon: 'simple-icons:calendly',
    status: 'coming-soon',
    connected: false,
    features: ['Appointment Scheduling', 'Calendar Sync', 'Meeting Automation']
  }
])

const hubspotIntegration = computed(() =>
  integrations.value.find(i => i.id === 'hubspot')
)

const hubspotConnected = computed(() =>
  contactsStore.hubspotConfig?.enabled || false
)

function handleIntegrationClick(integration: any) {
  if (integration.id === 'hubspot') {
    router.push('/integrations/hubspot')
  } else if (integration.status === 'coming-soon') {
    // Show coming soon message
    console.log(`${integration.name} is coming soon!`)
  }
}

onMounted(async () => {
  await contactsStore.initialize()
  // Update HubSpot connection status
  if (hubspotIntegration.value) {
    hubspotIntegration.value.connected = hubspotConnected.value
  }
})
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-h4 font-weight-bold mb-2">Integrations</h1>
      <p class="text-body-1 text-medium-emphasis">
        Connect your assistants with external services and tools
      </p>
    </div>

    <!-- Integrations Grid -->
    <v-row>
      <v-col
        v-for="integration in integrations"
        :key="integration.id"
        cols="12"
        sm="6"
        md="4"
        lg="3"
      >
        <v-card
          class="h-100 d-flex flex-column"
          elevation="2"
          :class="{
            'border-success': integration.connected,
            'cursor-pointer': integration.status === 'available'
          }"
          @click="handleIntegrationClick(integration)"
        >
          <!-- Integration Header -->
          <v-card-text class="text-center pa-6">
            <div class="position-relative mb-4">
              <v-avatar size="64" color="grey-lighten-3" class="mb-3">
                <Icon :icon="integration.icon" size="32" />
              </v-avatar>

              <!-- Status Badge -->
              <v-chip
                v-if="integration.connected"
                color="success"
                size="small"
                variant="elevated"
                class="position-absolute"
                style="top: 0; right: 50%; transform: translateX(50%);"
              >
                <v-icon start icon="mdi-check-circle" size="small" />
                Connected
              </v-chip>

              <v-chip
                v-else-if="integration.status === 'coming-soon'"
                color="warning"
                size="small"
                variant="tonal"
                class="position-absolute"
                style="top: 0; right: 50%; transform: translateX(50%);"
              >
                Coming Soon
              </v-chip>
            </div>

            <h3 class="text-h6 font-weight-bold mb-2">{{ integration.name }}</h3>
            <p class="text-body-2 text-medium-emphasis mb-4">
              {{ integration.description }}
            </p>

            <!-- Features -->
            <div class="text-left">
              <h4 class="text-subtitle-2 font-weight-bold mb-2">Features:</h4>
              <ul class="text-body-2 text-medium-emphasis">
                <li v-for="feature in integration.features" :key="feature" class="mb-1">
                  {{ feature }}
                </li>
              </ul>
            </div>
          </v-card-text>

          <!-- Action Button -->
          <v-card-actions class="pa-6 pt-0 mt-auto">
            <v-btn
              v-if="integration.connected"
              color="success"
              variant="tonal"
              class="w-100"
              @click.stop="handleIntegrationClick(integration)"
            >
              <Icon icon="mdi-cog" class="mr-2" />
              Manage
            </v-btn>

            <v-btn
              v-else-if="integration.status === 'available'"
              color="primary"
              variant="elevated"
              class="w-100"
              @click.stop="handleIntegrationClick(integration)"
            >
              <Icon icon="mdi-plus" class="mr-2" />
              Connect
            </v-btn>

            <v-btn
              v-else
              variant="outlined"
              class="w-100"
              disabled
            >
              <Icon icon="mdi-clock" class="mr-2" />
              Coming Soon
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Help Section -->
    <v-card elevation="2" class="mt-8">
      <v-card-text class="pa-6">
        <div class="d-flex align-center mb-4">
          <Icon icon="mdi-help-circle" size="24" class="mr-3" />
          <h3 class="text-h6 font-weight-bold">Need Help?</h3>
        </div>

        <p class="text-body-1 mb-4">
          Having trouble setting up an integration? Our support team is here to help you get connected.
        </p>

        <v-btn variant="outlined" @click="$router.push('/profile-settings')">
          <Icon icon="mdi-email" class="mr-2" />
          Contact Support
        </v-btn>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<style scoped>
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.border-success {
  border: 2px solid rgb(var(--v-theme-success)) !important;
}
</style>
