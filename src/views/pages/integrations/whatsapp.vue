<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useContactsStore, useAssistantsStore } from '@/store'
import { whatsappMicroservicesClient } from '@/services/whatsapp/whatsappMicroservicesClient'
import type { Contact } from '@/store/useContactsStore'
import ContactCard from '@/components/ContactCard.vue'

const router = useRouter()
const contactsStore = useContactsStore()
const assistantsStore = useAssistantsStore()

// State
const loading = ref(false)
const activatingAgent = ref(false)
const selectedContact = ref<Contact | null>(null)
const selectedAgent = ref('')
const welcomeMessage = ref('')
const showActivateDialog = ref(false)
const showMessagePanel = ref<string | null>(null)
const conversations = ref<Record<string, any[]>>({})

// Real-time messaging state
const eventSources = ref<Map<string, EventSource>>(new Map())
const connectionStatus = ref<Record<string, 'connecting' | 'connected' | 'disconnected' | 'error'>>({})



// Lead status grouping state
const groupByLeadStatus = ref(false)
const expandedGroups = ref<Set<string>>(new Set())
const sortBy = ref<'createdAt' | 'lastActivity'>('lastActivity')

// Computed
const availableContacts = computed(() => contactsStore.hubspotContacts)
const availableAssistants = computed(() => assistantsStore.assistants)

const activeWhatsAppContacts = computed(() =>
  availableContacts.value.filter(contact => {
    const agent = contactsStore.getWhatsAppAgentByContact(contact.id)
    return agent?.isActive
  })
)

const inactiveContacts = computed(() =>
  availableContacts.value.filter(contact => {
    const agent = contactsStore.getWhatsAppAgentByContact(contact.id)
    return !agent?.isActive
  })
)

// Lead status grouping computed properties
const contactsByLeadStatus = computed(() => {
  if (!groupByLeadStatus.value) {
    return {}
  }

  const grouped: Record<string, Contact[]> = {}
  const contacts = inactiveContacts.value

  contacts.forEach(contact => {
    const status = contact.leadStatus || 'sin_clasificar'
    if (!grouped[status]) {
      grouped[status] = []
    }
    grouped[status].push(contact)
  })

  // Sort contacts within each group
  Object.keys(grouped).forEach(status => {
    grouped[status].sort((a, b) => {
      if (sortBy.value === 'createdAt') {
        return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
      } else {
        return new Date(b.lastActivity || 0).getTime() - new Date(a.lastActivity || 0).getTime()
      }
    })
  })

  return grouped
})

const leadStatusGroups = computed(() => {
  return Object.keys(contactsByLeadStatus.value).map(status => ({
    status,
    label: contactsStore.getLeadStatusLabel(status),
    contacts: contactsByLeadStatus.value[status],
    count: contactsByLeadStatus.value[status].length
  }))
})

// Initialize
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([
      contactsStore.initialize(),
      assistantsStore.fetchAllAssistants()
    ])

    // Start real-time connections for active agents
    startRealtimeForActiveAgents()
  } catch (error) {
    console.error('Error initializing WhatsApp integration:', error)
  } finally {
    loading.value = false
  }
})

// Cleanup on unmount
onUnmounted(() => {
  stopAllRealtimeConnections()
})



// Actions
function openActivateDialog(contact: Contact) {
  selectedContact.value = contact
  selectedAgent.value = ''
  welcomeMessage.value = `¡Hola ${contact.name}! Soy tu asistente virtual. ¿En qué puedo ayudarte hoy?`
  showActivateDialog.value = true
}

async function activateAgent() {
  if (!selectedContact.value || !selectedAgent.value) return

  try {
    activatingAgent.value = true

    await contactsStore.activateWhatsAppAgent(
      selectedContact.value.id,
      selectedAgent.value,
      welcomeMessage.value
    )

    // Start real-time connection for the newly activated agent
    if (selectedAgent.value && !eventSources.value.has(selectedAgent.value)) {
      startRealtimeConnection(selectedAgent.value)
    }

    showActivateDialog.value = false
    console.log(`Agent activated for ${selectedContact.value.name}`)
  } catch (error) {
    console.error('Error activating agent:', error)
  } finally {
    activatingAgent.value = false
  }
}

async function deactivateAgent(contact: Contact) {
  try {
    // Get the agent ID before deactivating
    const agent = contactsStore.getWhatsAppAgentByContact(contact.id)

    if (!agent || !agent.agentId) {
      throw new Error(`No agent found for contact ${contact.id}`)
    }

    const agentId = agent.agentId

    console.log(`Agent found: ${agentId}`) // TODO: Remove debug log

    await contactsStore.deactivateWhatsAppAgent(contact.id, agentId)

    console.log('Deactivated?') // TODO: Remove debug log

    // Stop real-time connection for the deactivated agent
    if (eventSources.value.has(agentId)) {
      stopRealtimeConnection(agentId)
    }

    console.log(`Agent deactivated for ${contact.name}`)
  } catch (error) {
    console.error('Error deactivating agent:', error)
  }
}

async function loadConversation(contact: Contact, silent: boolean = false) {
  try {
    if (!silent) {
      console.log('Loading conversation for:', contact.name)
    }

    // Get the active agent for this contact
    const agent = contactsStore.getWhatsAppAgentByContact(contact.id)

    if (!agent || !agent.agentId) {
      if (!silent) {
        console.log(`No active agent found for ${contact.name}`)
      }
      conversations.value[contact.id] = []
      return
    }

    // Load conversation from Communications Service using agent ID
    const response = await whatsappMicroservicesClient.getConversationByAgentAndPhone(
      agent.agentId,
      contact.phone
    )

    if (response.success && response.data) {
      const messages = response.data.messages || []

      // Transform messages to match the expected format
      const transformedMessages = messages.map((msg: any) => ({
        sid: msg.message_id,
        body: msg.content,
        direction: msg.direction,
        timestamp: msg.stored_at || msg.timestamp,
        from: msg.from_number,
        to: msg.to_number,
        status: msg.status
      }))

      // Check if there are new messages (for silent refresh)
      const currentMessages = conversations.value[contact.id] || []
      const hasNewMessages = transformedMessages.length > currentMessages.length

      // Store in conversations reactive object
      conversations.value[contact.id] = transformedMessages

      if (!silent) {
        console.log(`✅ Loaded ${transformedMessages.length} messages for ${contact.name} with agent ${agent.agentId}`)
      } else if (hasNewMessages) {
        console.log(`🔄 Updated conversation for ${contact.name}: ${transformedMessages.length - currentMessages.length} new messages`)
      }
    } else {
      if (!silent) {
        console.log(`No conversation history found for ${contact.name} with agent ${agent.agentId}`)
      }
      conversations.value[contact.id] = []
    }
  } catch (error) {
    if (!silent) {
      console.error('Error loading conversation:', error)
    }
    conversations.value[contact.id] = []
  }
}

function toggleMessagePanel(contact: Contact) {
  if (showMessagePanel.value === contact.id) {
    showMessagePanel.value = null
  } else {
    showMessagePanel.value = contact.id
    loadConversation(contact)

    // Ensure real-time connection is active for this contact's agent
    const agent = contactsStore.getWhatsAppAgentByContact(contact.id)
    if (agent?.agentId && !eventSources.value.has(agent.agentId)) {
      startRealtimeConnection(agent.agentId)
    }
  }
}

// Lead status grouping functions
function toggleGroupExpansion(status: string) {
  if (expandedGroups.value.has(status)) {
    expandedGroups.value.delete(status)
  } else {
    expandedGroups.value.add(status)
  }
}

function isGroupExpanded(status: string): boolean {
  return expandedGroups.value.has(status)
}

function toggleGroupByLeadStatus() {
  groupByLeadStatus.value = !groupByLeadStatus.value
  if (groupByLeadStatus.value) {
    // Expand all groups by default
    leadStatusGroups.value.forEach(group => {
      expandedGroups.value.add(group.status)
    })
  } else {
    expandedGroups.value.clear()
  }
}

async function updateContactLeadStatus(contact: Contact, newStatus: string) {
  try {
    await contactsStore.updateContactLeadStatus(contact.id, newStatus)
    // The store will handle updating the local contact data
  } catch (error) {
    console.error('Error updating lead status:', error)
  }
}

function getAgentName(contact: Contact) {
  const agent = contactsStore.getWhatsAppAgentByContact(contact.id)
  if (!agent) return ''

  const assistant = availableAssistants.value.find(a => a.id === agent.agentId)
  return assistant?.name || agent.agentId
}

function goBack() {
  router.push({ name: 'integrations' })
}

// Real-time messaging functions
function startRealtimeConnection(agentId: string) {
  // Prevent duplicate connections
  if (eventSources.value.has(agentId)) {
    console.log(`Real-time connection already exists for agent ${agentId}`)
    return
  }

  try {
    connectionStatus.value[agentId] = 'connecting'

    // Get the communications service URL from environment
    const communicationsServiceUrl = import.meta.env.VITE_COMMUNICATIONS_SERVICE_URL || 'http://localhost:8083'
    const eventSource = new EventSource(`${communicationsServiceUrl}/conversations/stream/${agentId}`)

    eventSource.onopen = () => {
      connectionStatus.value[agentId] = 'connected'
      console.log(`✅ Real-time connection established for agent ${agentId}`)
    }

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleRealtimeUpdate(data)
      } catch (error) {
        console.error('Error parsing SSE message:', error)
      }
    }

    eventSource.onerror = (error) => {
      console.error(`❌ SSE connection error for agent ${agentId}:`, error)
      connectionStatus.value[agentId] = 'error'

      // Check if the agent is still active before attempting to reconnect
      const isAgentStillActive = activeWhatsAppContacts.value.some(contact => {
        const agent = contactsStore.getWhatsAppAgentByContact(contact.id)
        return agent?.agentId === agentId && agent?.isActive
      })

      if (!isAgentStillActive) {
        console.log(`🔌 Agent ${agentId} is no longer active, not reconnecting SSE`)
        stopRealtimeConnection(agentId)
        return
      }

      // Close the connection and let the component handle reconnection if needed
      stopRealtimeConnection(agentId)
    }

    eventSources.value.set(agentId, eventSource)
    console.log(`🔄 Started real-time connection for agent ${agentId}`)

  } catch (error) {
    console.error(`Error starting real-time connection for agent ${agentId}:`, error)
    connectionStatus.value[agentId] = 'error'
  }
}

function stopRealtimeConnection(agentId: string) {
  const eventSource = eventSources.value.get(agentId)
  if (eventSource) {
    eventSource.close()
    eventSources.value.delete(agentId)
    connectionStatus.value[agentId] = 'disconnected'
    console.log(`🛑 Stopped real-time connection for agent ${agentId}`)
  }
}

function handleRealtimeUpdate(data: any) {
  switch (data.type) {
    case 'connected':
      console.log(`🔗 Connected to real-time updates for agent ${data.agent_id}`)
      break

    case 'conversation_update':
      updateConversationFromRealtime(data)
      break

    case 'heartbeat':
      // Keep connection alive - no action needed
      break

    case 'agent_deactivated':
      console.log(`🔌 Agent ${data.agent_id} was deactivated, closing SSE connection`)
      // Stop the connection for this agent
      if (data.agent_id) {
        stopRealtimeConnection(data.agent_id)
      }
      break

    case 'error':
      console.error('Real-time update error:', data.message)
      // Don't reconnect on server errors
      break

    default:
      console.log('Unknown real-time update type:', data.type)
  }
}

function updateConversationFromRealtime(data: any) {
  try {
    // Find the contact by phone number
    const contact = availableContacts.value.find(c => c.phone === data.phone_number)
    if (!contact) {
      return // Silently ignore updates for contacts not in current view
    }

    // Transform messages to match the expected format
    const transformedMessages = (data.messages || []).map((msg: any) => ({
      sid: msg.message_id,
      body: msg.content,
      direction: msg.direction,
      timestamp: msg.stored_at || msg.timestamp,
      from: msg.from_number,
      to: msg.to_number,
      status: msg.status
    }))

    // Update the conversation if it has changed
    const currentMessages = conversations.value[contact.id] || []
    if (transformedMessages.length !== currentMessages.length) {
      conversations.value[contact.id] = transformedMessages
      console.log(`🔄 Updated conversation for ${contact.name}: ${transformedMessages.length} messages`)
    }

  } catch (error) {
    console.error('Error updating conversation from real-time data:', error)
  }
}

function startRealtimeForActiveAgents() {
  // Get unique agent IDs from active contacts
  const activeAgentIds = new Set(
    activeWhatsAppContacts.value
      .map(contact => contactsStore.getWhatsAppAgentByContact(contact.id)?.agentId)
      .filter(Boolean)
  )

  // Start connections for active agents that don't already have connections
  activeAgentIds.forEach(agentId => {
    if (agentId && !eventSources.value.has(agentId)) {
      startRealtimeConnection(agentId)
    }
  })
}

function stopAllRealtimeConnections() {
  // Create a copy of the keys to avoid modification during iteration
  const agentIds = Array.from(eventSources.value.keys())
  agentIds.forEach(agentId => {
    stopRealtimeConnection(agentId)
  })
}


</script>

<template>
  <v-container class="pa-6">
    <!-- Header -->
    <div class="d-flex align-center mb-6">
      <v-btn
        icon="mdi-arrow-left"
        variant="text"
        @click="goBack"
        class="mr-3"
      />
      <div class="d-flex align-center">
        <Icon icon="solar:chat-round-broken" class="mr-3 text-primary" width="32" />
        <div>
          <h1 class="text-h4 font-weight-bold">Integración WhatsApp</h1>
          <p class="text-body-1 text-medium-emphasis mb-0">
            Gestiona conversaciones de WhatsApp con tus contactos de HubSpot
          </p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <v-card v-if="loading" elevation="1" class="mb-4">
      <v-card-text class="text-center py-8">
        <v-progress-circular indeterminate color="primary" class="mb-4" />
        <p class="text-body-1">Cargando contactos y asistentes...</p>
      </v-card-text>
    </v-card>

    <!-- No HubSpot Contacts -->
    <v-card v-else-if="availableContacts.length === 0" elevation="1" class="mb-4">
      <v-card-text class="text-center py-8">
        <Icon icon="solar:users-group-rounded-broken" class="mb-4 text-medium-emphasis" width="64" />
        <h3 class="text-h6 mb-2">No hay contactos disponibles</h3>
        <p class="text-body-2 text-medium-emphasis mb-4">
          Primero necesitas configurar HubSpot y obtener contactos
        </p>
        <v-btn
          color="primary"
          @click="router.push({ name: 'integrations-hubspot' })"
        >
          Configurar HubSpot
        </v-btn>
      </v-card-text>
    </v-card>

    <!-- Active WhatsApp Contacts -->
    <template v-else>
      <v-card v-if="activeWhatsAppContacts.length > 0" elevation="1" class="mb-6">
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <Icon icon="solar:chat-round-bold" class="mr-2 text-success" width="20" />
            Contactos Activos ({{ activeWhatsAppContacts.length }})
          </div>

          <!-- Grouping Controls for Active Contacts -->
          <div class="d-flex align-center gap-2">
            <v-select
              v-model="sortBy"
              :items="[
                { value: 'lastActivity', title: 'Última actividad' },
                { value: 'createdAt', title: 'Fecha de creación' }
              ]"
              item-value="value"
              item-title="title"
              variant="outlined"
              density="compact"
              hide-details
              style="min-width: 160px;"
            />

            <v-btn
              :color="groupByLeadStatus ? 'primary' : 'default'"
              :variant="groupByLeadStatus ? 'flat' : 'outlined'"
              size="small"
              @click="toggleGroupByLeadStatus"
            >
              <Icon icon="solar:folder-broken" class="mr-1" width="16" />
              {{ groupByLeadStatus ? 'Desagrupar' : 'Agrupar por Estado' }}
            </v-btn>
          </div>
        </v-card-title>

        <v-card-text>
          <!-- Grouped View for Active Contacts -->
          <div v-if="groupByLeadStatus">
            <div
              v-for="group in leadStatusGroups.filter(g => g.contacts.some(c => activeWhatsAppContacts.includes(c)))"
              :key="`active-${group.status}`"
              class="mb-4"
            >
              <!-- Group Header -->
              <v-card
                variant="outlined"
                class="mb-2"
                :color="isGroupExpanded(group.status) ? 'primary' : 'default'"
                @click="toggleGroupExpansion(group.status)"
                style="cursor: pointer;"
              >
                <v-card-text class="py-2">
                  <div class="d-flex align-center justify-space-between">
                    <div class="d-flex align-center">
                      <Icon
                        :icon="isGroupExpanded(group.status) ? 'solar:alt-arrow-down-broken' : 'solar:alt-arrow-right-broken'"
                        class="mr-2"
                        width="16"
                      />
                      <span class="font-weight-medium">{{ group.label }}</span>
                      <v-chip size="small" class="ml-2" variant="tonal">
                        {{ group.contacts.filter(c => activeWhatsAppContacts.includes(c)).length }}
                      </v-chip>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <!-- Group Active Contacts -->
              <v-expand-transition>
                <v-row v-if="isGroupExpanded(group.status)">
                  <v-col
                    v-for="contact in group.contacts.filter(c => activeWhatsAppContacts.includes(c))"
                    :key="contact.id"
                    cols="12"
                    md="6"
                    lg="4"
                  >
                    <ContactCard
                      :contact="contact"
                      :show-lead-status="true"
                      :is-active="true"
                      :agent-name="getAgentName(contact)"
                      @activate="openActivateDialog"
                      @deactivate="deactivateAgent"
                      @update-lead-status="updateContactLeadStatus"
                      @toggle-messages="toggleMessagePanel"
                      :show-messages="showMessagePanel === contact.id"
                      :messages="conversations[contact.id] || []"
                    />
                  </v-col>
                </v-row>
              </v-expand-transition>
            </div>
          </div>

          <!-- Ungrouped View for Active Contacts -->
          <v-row v-else>
            <v-col
              v-for="contact in activeWhatsAppContacts"
              :key="contact.id"
              cols="12"
              md="6"
              lg="4"
            >
              <ContactCard
                :contact="contact"
                :show-lead-status="true"
                :is-active="true"
                :agent-name="getAgentName(contact)"
                @activate="openActivateDialog"
                @deactivate="deactivateAgent"
                @update-lead-status="updateContactLeadStatus"
                @toggle-messages="toggleMessagePanel"
                :show-messages="showMessagePanel === contact.id"
                :messages="conversations[contact.id] || []"
              />
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <!-- Available Contacts -->
      <v-card v-if="inactiveContacts.length > 0" elevation="1" class="mb-6">
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <Icon icon="solar:users-group-rounded-broken" class="mr-2 text-primary" width="20" />
            Contactos Disponibles ({{ inactiveContacts.length }})
          </div>

          <!-- Grouping Controls -->
          <div class="d-flex align-center gap-2">
            <v-select
              v-model="sortBy"
              :items="[
                { value: 'lastActivity', title: 'Última actividad' },
                { value: 'createdAt', title: 'Fecha de creación' }
              ]"
              item-value="value"
              item-title="title"
              variant="outlined"
              density="compact"
              hide-details
              style="min-width: 160px;"
            />

            <v-btn
              :color="groupByLeadStatus ? 'primary' : 'default'"
              :variant="groupByLeadStatus ? 'flat' : 'outlined'"
              size="small"
              @click="toggleGroupByLeadStatus"
            >
              <Icon icon="solar:folder-broken" class="mr-1" width="16" />
              {{ groupByLeadStatus ? 'Desagrupar' : 'Agrupar por Estado' }}
            </v-btn>
          </div>
        </v-card-title>

        <v-card-text>
          <!-- Grouped View -->
          <div v-if="groupByLeadStatus">
            <div
              v-for="group in leadStatusGroups"
              :key="group.status"
              class="mb-4"
            >
              <!-- Group Header -->
              <v-card
                variant="outlined"
                class="mb-2"
                :color="isGroupExpanded(group.status) ? 'primary' : 'default'"
                @click="toggleGroupExpansion(group.status)"
                style="cursor: pointer;"
              >
                <v-card-text class="py-2">
                  <div class="d-flex align-center justify-space-between">
                    <div class="d-flex align-center">
                      <Icon
                        :icon="isGroupExpanded(group.status) ? 'solar:alt-arrow-down-broken' : 'solar:alt-arrow-right-broken'"
                        class="mr-2"
                        width="16"
                      />
                      <span class="font-weight-medium">{{ group.label }}</span>
                      <v-chip size="small" class="ml-2" variant="tonal">
                        {{ group.count }}
                      </v-chip>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <!-- Group Contacts -->
              <v-expand-transition>
                <v-row v-if="isGroupExpanded(group.status)">
                  <v-col
                    v-for="contact in group.contacts"
                    :key="contact.id"
                    cols="12"
                    md="6"
                    lg="4"
                  >
                    <ContactCard
                      :contact="contact"
                      :show-lead-status="true"
                      @activate="openActivateDialog"
                      @update-lead-status="updateContactLeadStatus"
                    />
                  </v-col>
                </v-row>
              </v-expand-transition>
            </div>
          </div>

          <!-- Ungrouped View -->
          <v-row v-else>
            <v-col
              v-for="contact in inactiveContacts"
              :key="contact.id"
              cols="12"
              md="6"
              lg="4"
            >
              <ContactCard
                :contact="contact"
                :show-lead-status="true"
                @activate="openActivateDialog"
                @update-lead-status="updateContactLeadStatus"
              />
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </template>

    <!-- Activate Agent Dialog -->
    <v-dialog v-model="showActivateDialog" max-width="500">
      <v-card>
        <v-card-title class="d-flex align-center">
          <Icon icon="solar:settings-broken" class="mr-2" width="20" />
          Activar Asistente WhatsApp
        </v-card-title>

        <v-card-text>
          <div v-if="selectedContact" class="mb-4">
            <div class="d-flex align-center mb-3">
              <v-avatar size="32" color="primary" class="mr-3">
                <span class="text-white font-weight-bold">{{ selectedContact.name.charAt(0) }}</span>
              </v-avatar>
              <div>
                <h4 class="text-subtitle-1 font-weight-bold">{{ selectedContact.name }}</h4>
                <p class="text-caption text-medium-emphasis mb-0">{{ selectedContact.phone }}</p>
              </div>
            </div>
          </div>

          <v-select
            v-model="selectedAgent"
            :items="availableAssistants"
            item-title="name"
            item-value="id"
            label="Seleccionar Asistente"
            variant="outlined"
            :loading="assistantsStore.isLoading"
            required
          >
            <template #item="{ props, item }">
              <v-list-item v-bind="props">
                <template #prepend>
                  <v-avatar size="24" color="primary">
                    <span class="text-caption">{{ item.raw.name.charAt(0) }}</span>
                  </v-avatar>
                </template>
              </v-list-item>
            </template>
          </v-select>

          <v-textarea
            v-model="welcomeMessage"
            label="Mensaje de Bienvenida (Opcional)"
            variant="outlined"
            rows="3"
            hint="Este mensaje se enviará automáticamente cuando se active el asistente"
          />
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="text"
            @click="showActivateDialog = false"
            :disabled="activatingAgent"
          >
            Cancelar
          </v-btn>
          <v-btn
            color="primary"
            :loading="activatingAgent"
            :disabled="!selectedAgent"
            @click="activateAgent"
          >
            Activar Asistente
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<style scoped>
.gap-2 {
  gap: 8px;
}

.message-bubble {
  display: inline-block;
  border-radius: 12px;
}

.messages-container {
  display: flex;
  flex-direction: column;
}

.message-item {
  display: flex;
  width: 100%;
}
</style>
