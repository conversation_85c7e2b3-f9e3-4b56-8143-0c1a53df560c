<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useContactsStore, useAssistantsStore } from '@/store'
import { Icon } from '@iconify/vue'

const router = useRouter()
const contactsStore = useContactsStore()
const assistantsStore = useAssistantsStore()

const search = ref('')
const selectedContacts = ref<string[]>([])
const showAssignModal = ref(false)
const showEditModal = ref(false)
const editingContact = ref<any>(null)
const newLeadStatus = ref('')
const selectedAssistantForAssign = ref<string | null>(null)
const initialMessage = ref('Hello! I\'m your AI assistant. How can I help you today?')
const itemsPerPage = ref(10)
const page = ref(1)

const headers = [
  { title: 'Name', key: 'name', sortable: true },
  { title: 'Email', key: 'email', sortable: true },
  { title: 'Phone', key: 'phone', sortable: false },
  { title: 'Company', key: 'company', sortable: true },
  { title: 'Lead Status', key: 'leadStatus', sortable: true },
  { title: 'Assigned Assistant', key: 'assignedAssistant', sortable: false },
  { title: 'Actions', key: 'actions', sortable: false }
]

const filteredContacts = computed(() => {
  if (!search.value) return contactsStore.contacts

  const searchTerm = search.value.toLowerCase()
  return contactsStore.contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm) ||
    contact.email?.toLowerCase().includes(searchTerm) ||
    contact.phone.toLowerCase().includes(searchTerm) ||
    contact.company?.toLowerCase().includes(searchTerm)
  )
})

const totalContacts = computed(() => filteredContacts.value.length)
const hubspotConnected = computed(() => contactsStore.hubspotConfig?.enabled || false)

function getLeadStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    'new': 'primary',
    'contacted': 'info',
    'qualified': 'warning',
    'converted': 'success',
    'lost': 'error'
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function getAssignedAssistant(contactId: string) {
  // This would come from the backend - for now return mock data
  const mockAssignments: Record<string, string> = {}
  const assistantId = mockAssignments[contactId]
  if (!assistantId) return null

  return assistantsStore.assistants.find(a => a.id === assistantId)
}

function openAssignModal() {
  if (selectedContacts.value.length === 0) return
  showAssignModal.value = true
}

function closeAssignModal() {
  showAssignModal.value = false
  selectedAssistantForAssign.value = null
  initialMessage.value = 'Hello! I\'m your AI assistant. How can I help you today?'
}

async function assignAssistant() {
  if (!selectedAssistantForAssign.value || selectedContacts.value.length === 0) return

  try {
    // This would call the backend API to assign assistant to contacts and send initial message
    console.log('Connecting assistant', selectedAssistantForAssign.value, 'to contacts', selectedContacts.value, 'with message:', initialMessage.value)

    // Mock success
    selectedContacts.value = []
    closeAssignModal()

    // Show success message
    // You could use a toast/snackbar here
  } catch (error) {
    console.error('Failed to connect assistant:', error)
  }
}

async function refreshContacts() {
  try {
    await contactsStore.fetchHubSpotContacts()
  } catch (error) {
    console.error('Failed to refresh contacts:', error)
  }
}

function viewContactDetails(contact: any) {
  // Navigate to conversations page and filter by this contact
  router.push({
    path: '/conversations',
    query: {
      contactId: contact.id,
      contactName: contact.name
    }
  })
}

function editContact(contact: any) {
  editingContact.value = contact
  newLeadStatus.value = contact.leadStatus || ''
  showEditModal.value = true
}

function closeEditModal() {
  showEditModal.value = false
  editingContact.value = null
  newLeadStatus.value = ''
}

async function updateContactLeadStatus() {
  if (!editingContact.value || !newLeadStatus.value) return

  try {
    // Call the API to update lead status
    await contactsStore.updateContactLeadStatus(editingContact.value.id, newLeadStatus.value)

    // Update local contact data
    const contactIndex = contactsStore.contacts.findIndex(c => c.id === editingContact.value.id)
    if (contactIndex !== -1) {
      contactsStore.contacts[contactIndex].leadStatus = newLeadStatus.value
    }

    closeEditModal()

    // Show success message
    console.log('Lead status updated successfully')
  } catch (error) {
    console.error('Failed to update lead status:', error)
  }
}

onMounted(async () => {
  await contactsStore.initialize()
  await assistantsStore.fetchAllAssistants()

  if (hubspotConnected.value) {
    await refreshContacts()
  }
})
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">Contacts</h1>
        <p class="text-body-1 text-medium-emphasis">
          Manage your HubSpot contacts and assign assistants
        </p>
      </div>

      <div class="d-flex gap-2">
        <v-btn
          v-if="selectedContacts.length > 0"
          color="primary"
          variant="elevated"
          @click="openAssignModal"
        >
          <Icon icon="mdi-account-plus" class="mr-2" />
          Assign Assistant ({{ selectedContacts.length }})
        </v-btn>

        <v-btn
          variant="outlined"
          @click="refreshContacts"
          :loading="contactsStore.isLoading"
          :disabled="!hubspotConnected"
        >
          <Icon icon="mdi-refresh" class="mr-2" />
          Refresh
        </v-btn>
      </div>
    </div>

    <!-- HubSpot Connection Status -->
    <v-alert
      v-if="!hubspotConnected"
      type="warning"
      variant="tonal"
      class="mb-6"
    >
      <template #prepend>
        <Icon icon="simple-icons:hubspot" />
      </template>
      <div class="d-flex align-center justify-space-between">
        <div>
          <strong>HubSpot not connected</strong>
          <p class="mb-0">Connect your HubSpot account to view and manage contacts.</p>
        </div>
        <v-btn
          variant="outlined"
          size="small"
          @click="$router.push('/integrations/hubspot')"
        >
          Connect HubSpot
        </v-btn>
      </div>
    </v-alert>

    <!-- Search and Filters -->
    <v-card class="mb-6" elevation="2">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="6">
            <v-text-field
              v-model="search"
              placeholder="Search contacts..."
              variant="outlined"
              hide-details
              clearable
            >
              <template #prepend-inner>
                <Icon icon="mdi-magnify" />
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" class="text-right">
            <div class="text-body-2 text-medium-emphasis">
              {{ totalContacts }} contact{{ totalContacts !== 1 ? 's' : '' }} total
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Contacts Table -->
    <v-card elevation="2">
      <v-data-table
        v-model="selectedContacts"
        :headers="headers"
        :items="filteredContacts"
        :items-per-page="itemsPerPage"
        :page="page"
        :loading="contactsStore.isLoading"
        item-value="id"
        show-select
        class="elevation-0"
      >
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <v-avatar size="32" color="primary" class="mr-3">
              <span class="text-white text-caption">
                {{ item.name.split(' ').map((n: string) => n[0]).join('').toUpperCase() }}
              </span>
            </v-avatar>
            <div>
              <div class="font-weight-medium">{{ item.name }}</div>
              <div class="text-caption text-medium-emphasis">ID: {{ item.id.slice(0, 8) }}...</div>
            </div>
          </div>
        </template>

        <template #item.email="{ item }">
          <div v-if="item.email">
            <a :href="`mailto:${item.email}`" class="text-decoration-none">
              {{ item.email }}
            </a>
          </div>
          <span v-else class="text-medium-emphasis">—</span>
        </template>

        <template #item.phone="{ item }">
          <div v-if="item.phone">
            <a :href="`tel:${item.phone}`" class="text-decoration-none">
              {{ item.phone }}
            </a>
          </div>
          <span v-else class="text-medium-emphasis">—</span>
        </template>

        <template #item.company="{ item }">
          <span v-if="item.company">{{ item.company }}</span>
          <span v-else class="text-medium-emphasis">—</span>
        </template>

        <template #item.leadStatus="{ item }">
          <v-chip
            v-if="item.leadStatus"
            :color="getLeadStatusColor(item.leadStatus)"
            size="small"
            variant="tonal"
          >
            {{ item.leadStatusLabel || item.leadStatus }}
          </v-chip>
          <span v-else class="text-medium-emphasis">—</span>
        </template>

        <template #item.assignedAssistant="{ item }">
          <div v-if="getAssignedAssistant(item.id)" class="d-flex align-center">
            <v-avatar size="24" color="primary" class="mr-2">
              <Icon icon="mdi-robot" size="12" />
            </v-avatar>
            <span class="text-body-2">{{ getAssignedAssistant(item.id)?.name }}</span>
          </div>
          <v-chip v-else size="small" variant="outlined" color="grey">
            Not assigned
          </v-chip>
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <v-btn
              icon="mdi-eye"
              size="small"
              variant="text"
              @click="viewContactDetails(item)"
            />
            <v-btn
              icon="mdi-pencil"
              size="small"
              variant="text"
              @click="editContact(item)"
            />
          </div>
        </template>

        <template #no-data>
          <div class="text-center py-8">
            <Icon icon="mdi-account-search" size="64" class="text-medium-emphasis mb-4" />
            <h3 class="text-h6 font-weight-bold mb-2">No contacts found</h3>
            <p class="text-body-1 text-medium-emphasis mb-4">
              {{ search ? 'Try adjusting your search terms.' : 'Connect HubSpot to import your contacts.' }}
            </p>
            <v-btn
              v-if="!hubspotConnected"
              color="primary"
              variant="elevated"
              @click="$router.push('/integrations/hubspot')"
            >
              Connect HubSpot
            </v-btn>
          </div>
        </template>
      </v-data-table>
    </v-card>
  </v-container>

  <!-- Assign Assistant Modal -->
  <v-dialog v-model="showAssignModal" max-width="500">
    <v-card>
      <v-card-title>
        <span class="text-h6">Connect Assistant</span>
      </v-card-title>

      <v-card-text>
        <p class="mb-4">
          Connect an assistant to {{ selectedContacts.length }} selected contact{{ selectedContacts.length !== 1 ? 's' : '' }}.
        </p>

        <v-select
          v-model="selectedAssistantForAssign"
          :items="assistantsStore.assistants"
          item-title="name"
          item-value="id"
          label="Select Assistant"
          variant="outlined"
          :disabled="assistantsStore.assistants.length === 0"
          class="mb-4"
        >
          <template #item="{ props, item }">
            <v-list-item v-bind="props">
              <template #prepend>
                <v-avatar size="32" color="primary">
                  <Icon icon="solar:user-speak-broken" />
                </v-avatar>
              </template>
              <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
              <v-list-item-subtitle>{{ item.raw.type }}</v-list-item-subtitle>
            </v-list-item>
          </template>
        </v-select>

        <v-textarea
          v-model="initialMessage"
          label="Initial Message"
          variant="outlined"
          rows="3"
          placeholder="Enter the initial message to send to contacts..."
          class="mb-4"
        />

        <!-- Informational Notice -->
        <v-alert
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <template #prepend>
            <Icon icon="solar:info-circle-broken" />
          </template>
          <div class="text-body-2">
            Connecting the agent will start chatting with the selected contacts using the initial message above.
          </div>
        </v-alert>

        <v-alert
          v-if="assistantsStore.assistants.length === 0"
          type="info"
          variant="tonal"
          class="mt-4"
        >
          No assistants available. Create an assistant first.
        </v-alert>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn variant="text" @click="closeAssignModal">Cancel</v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :disabled="!selectedAssistantForAssign"
          @click="assignAssistant"
        >
          Connect
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <!-- Edit Contact Modal -->
  <v-dialog v-model="showEditModal" max-width="500">
    <v-card>
      <v-card-title>
        <span class="text-h6">Edit Contact</span>
      </v-card-title>

      <v-card-text>
        <div v-if="editingContact" class="mb-4">
          <div class="d-flex align-center mb-4">
            <v-avatar size="40" color="primary" class="mr-3">
              <span class="text-white text-caption">
                {{ editingContact.name.split(' ').map((n: string) => n[0]).join('').toUpperCase() }}
              </span>
            </v-avatar>
            <div>
              <div class="font-weight-medium">{{ editingContact.name }}</div>
              <div class="text-caption text-medium-emphasis">{{ editingContact.email }}</div>
            </div>
          </div>

          <v-select
            v-model="newLeadStatus"
            :items="contactsStore.leadStatusOptions"
            item-title="label"
            item-value="value"
            label="Lead Status"
            variant="outlined"
          />
        </div>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn variant="text" @click="closeEditModal">Cancel</v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :disabled="!newLeadStatus || newLeadStatus === editingContact?.leadStatus"
          @click="updateContactLeadStatus"
        >
          Update
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.gap-2 {
  gap: 8px;
}

.gap-1 {
  gap: 4px;
}
</style>
