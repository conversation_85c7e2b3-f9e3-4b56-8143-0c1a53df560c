<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'

interface PricingPlan {
  id: string
  name: string
  price: number
  period: 'month' | 'year'
  popular?: boolean
  features: string[]
  limits: {
    assistants: number | 'unlimited'
    conversations: number | 'unlimited'
    integrations: number | 'unlimited'
    phoneNumbers: number | 'unlimited'
    support: string
  }
  stripePriceId?: string
}

const currentPlan = ref('free') // This would come from user's subscription data
const billingPeriod = ref<'month' | 'year'>('month')
const isUpgrading = ref(false)

const plans = ref<PricingPlan[]>([
  {
    id: 'free',
    name: 'Free',
    price: 0,
    period: 'month',
    features: [
      '1 AI Assistant',
      '50 conversations/month',
      'Basic integrations',
      'Email support',
      'Standard voice quality'
    ],
    limits: {
      assistants: 1,
      conversations: 50,
      integrations: 2,
      phoneNumbers: 1,
      support: 'Email'
    }
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 49,
    period: 'month',
    popular: true,
    features: [
      '5 AI Assistants',
      '1,000 conversations/month',
      'All integrations',
      'Priority support',
      'Premium voice quality',
      'Advanced analytics',
      'Custom templates'
    ],
    limits: {
      assistants: 5,
      conversations: 1000,
      integrations: 'unlimited',
      phoneNumbers: 3,
      support: 'Priority'
    },
    stripePriceId: 'price_pro_monthly'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 199,
    period: 'month',
    features: [
      'Unlimited AI Assistants',
      'Unlimited conversations',
      'All integrations',
      '24/7 dedicated support',
      'Premium voice quality',
      'Advanced analytics',
      'Custom templates',
      'White-label options',
      'API access',
      'Custom integrations'
    ],
    limits: {
      assistants: 'unlimited',
      conversations: 'unlimited',
      integrations: 'unlimited',
      phoneNumbers: 'unlimited',
      support: '24/7 Dedicated'
    },
    stripePriceId: 'price_enterprise_monthly'
  }
])

const yearlyPlans = computed(() => {
  return plans.value.map(plan => ({
    ...plan,
    price: plan.id === 'free' ? 0 : Math.floor(plan.price * 12 * 0.8), // 20% discount for yearly
    period: 'year' as const,
    stripePriceId: plan.stripePriceId?.replace('monthly', 'yearly')
  }))
})

const displayPlans = computed(() => {
  return billingPeriod.value === 'year' ? yearlyPlans.value : plans.value
})

const currentPlanData = computed(() => {
  return plans.value.find(plan => plan.id === currentPlan.value)
})

function formatPrice(price: number, period: 'month' | 'year'): string {
  if (price === 0) return 'Free'
  
  if (period === 'year') {
    const monthlyEquivalent = Math.floor(price / 12)
    return `$${price}/year ($${monthlyEquivalent}/month)`
  }
  
  return `$${price}/${period}`
}

function getYearlySavings(monthlyPrice: number): number {
  if (monthlyPrice === 0) return 0
  const yearlyPrice = Math.floor(monthlyPrice * 12 * 0.8)
  const monthlyCost = monthlyPrice * 12
  return monthlyCost - yearlyPrice
}

async function upgradeToPlan(planId: string) {
  if (planId === currentPlan.value || planId === 'free') return
  
  isUpgrading.value = true
  
  try {
    const plan = displayPlans.value.find(p => p.id === planId)
    if (!plan?.stripePriceId) {
      throw new Error('No Stripe price ID found for this plan')
    }
    
    // This would integrate with Stripe
    console.log('Upgrading to plan:', planId, 'with price ID:', plan.stripePriceId)
    
    // Mock success after delay
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    currentPlan.value = planId
    
    // Show success message
    console.log('Successfully upgraded to', plan.name)
    
  } catch (error) {
    console.error('Failed to upgrade plan:', error)
    // Show error message
  } finally {
    isUpgrading.value = false
  }
}

function contactSales() {
  // This would open a contact form or redirect to sales page
  console.log('Contact sales clicked')
}

function manageSubscription() {
  // This would redirect to Stripe customer portal
  console.log('Manage subscription clicked')
}
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-h3 font-weight-bold mb-4">Choose Your Plan</h1>
      <p class="text-h6 text-medium-emphasis mb-6">
        Scale your AI assistant capabilities with the right plan for your business
      </p>
      
      <!-- Current Plan Badge -->
      <v-chip
        v-if="currentPlanData"
        color="success"
        variant="tonal"
        size="large"
        class="mb-6"
      >
        <Icon icon="mdi-check-circle" class="mr-2" />
        Currently on {{ currentPlanData.name }} Plan
      </v-chip>
    </div>

    <!-- Billing Period Toggle -->
    <div class="text-center mb-8">
      <v-btn-toggle
        v-model="billingPeriod"
        mandatory
        variant="outlined"
        divided
      >
        <v-btn value="month">Monthly</v-btn>
        <v-btn value="year">
          Yearly
          <v-chip size="small" color="success" variant="tonal" class="ml-2">
            Save 20%
          </v-chip>
        </v-btn>
      </v-btn-toggle>
    </div>

    <!-- Pricing Cards -->
    <v-row justify="center" class="mb-8">
      <v-col
        v-for="plan in displayPlans"
        :key="plan.id"
        cols="12"
        md="4"
        class="d-flex"
      >
        <v-card
          class="flex-grow-1 d-flex flex-column position-relative"
          :class="{
            'border-primary': plan.popular,
            'elevation-8': plan.popular,
            'elevation-2': !plan.popular
          }"
          :variant="plan.popular ? 'elevated' : 'flat'"
        >
          <!-- Popular Badge -->
          <div
            v-if="plan.popular"
            class="position-absolute"
            style="top: -12px; left: 50%; transform: translateX(-50%); z-index: 1;"
          >
            <v-chip color="primary" variant="elevated" size="small">
              <Icon icon="mdi-star" class="mr-1" />
              Most Popular
            </v-chip>
          </div>

          <v-card-text class="text-center pa-6">
            <!-- Plan Name -->
            <h2 class="text-h4 font-weight-bold mb-2">{{ plan.name }}</h2>
            
            <!-- Price -->
            <div class="mb-4">
              <div class="text-h3 font-weight-bold text-primary">
                {{ formatPrice(plan.price, plan.period) }}
              </div>
              <div v-if="billingPeriod === 'year' && plan.price > 0" class="text-body-2 text-success">
                Save ${{ getYearlySavings(plans.find(p => p.id === plan.id)?.price || 0) }}/year
              </div>
            </div>

            <!-- Current Plan Indicator -->
            <v-chip
              v-if="plan.id === currentPlan"
              color="success"
              variant="tonal"
              size="small"
              class="mb-4"
            >
              Current Plan
            </v-chip>
          </v-card-text>

          <!-- Features List -->
          <v-card-text class="flex-grow-1 pt-0">
            <v-list density="compact">
              <v-list-item
                v-for="feature in plan.features"
                :key="feature"
                class="px-0"
              >
                <template #prepend>
                  <v-icon icon="mdi-check" color="success" size="small" />
                </template>
                <v-list-item-title class="text-body-2">{{ feature }}</v-list-item-title>
              </v-list-item>
            </v-list>

            <!-- Limits -->
            <v-divider class="my-4" />
            <div class="text-body-2 text-medium-emphasis">
              <div class="mb-1">
                <strong>Assistants:</strong> {{ plan.limits.assistants }}
              </div>
              <div class="mb-1">
                <strong>Conversations:</strong> {{ plan.limits.conversations }}/month
              </div>
              <div class="mb-1">
                <strong>Phone Numbers:</strong> {{ plan.limits.phoneNumbers }}
              </div>
              <div>
                <strong>Support:</strong> {{ plan.limits.support }}
              </div>
            </div>
          </v-card-text>

          <!-- Action Button -->
          <v-card-actions class="pa-6 pt-0">
            <v-btn
              v-if="plan.id === currentPlan"
              variant="outlined"
              class="w-100"
              @click="manageSubscription"
            >
              Manage Subscription
            </v-btn>
            <v-btn
              v-else-if="plan.id === 'free'"
              variant="outlined"
              class="w-100"
              disabled
            >
              Current Plan
            </v-btn>
            <v-btn
              v-else-if="plan.id === 'enterprise'"
              color="primary"
              variant="elevated"
              class="w-100"
              @click="contactSales"
            >
              Contact Sales
            </v-btn>
            <v-btn
              v-else
              color="primary"
              :variant="plan.popular ? 'elevated' : 'outlined'"
              class="w-100"
              :loading="isUpgrading"
              @click="upgradeToPlan(plan.id)"
            >
              Upgrade to {{ plan.name }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- FAQ Section -->
    <v-card elevation="2" class="mb-8">
      <v-card-title>
        <h2 class="text-h5 font-weight-bold">Frequently Asked Questions</h2>
      </v-card-title>
      <v-card-text>
        <v-expansion-panels variant="accordion">
          <v-expansion-panel>
            <v-expansion-panel-title>
              Can I change my plan at any time?
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              Yes, you can upgrade or downgrade your plan at any time. Changes will be prorated and reflected in your next billing cycle.
            </v-expansion-panel-text>
          </v-expansion-panel>
          
          <v-expansion-panel>
            <v-expansion-panel-title>
              What happens if I exceed my plan limits?
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              If you exceed your conversation limit, your assistants will be temporarily paused until the next billing cycle or you can upgrade your plan.
            </v-expansion-panel-text>
          </v-expansion-panel>
          
          <v-expansion-panel>
            <v-expansion-panel-title>
              Do you offer refunds?
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              We offer a 30-day money-back guarantee for all paid plans. Contact our support team if you're not satisfied.
            </v-expansion-panel-text>
          </v-expansion-panel>
          
          <v-expansion-panel>
            <v-expansion-panel-title>
              Is there a setup fee?
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              No, there are no setup fees or hidden costs. You only pay the monthly or yearly subscription fee.
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>
    </v-card>

    <!-- Contact Support -->
    <div class="text-center">
      <h3 class="text-h6 font-weight-bold mb-2">Need help choosing?</h3>
      <p class="text-body-1 text-medium-emphasis mb-4">
        Our team is here to help you find the perfect plan for your business needs.
      </p>
      <v-btn variant="outlined" @click="contactSales">
        <Icon icon="mdi-email" class="mr-2" />
        Contact Support
      </v-btn>
    </div>
  </v-container>
</template>

<style scoped>
.w-100 {
  width: 100%;
}

.border-primary {
  border: 2px solid rgb(var(--v-theme-primary)) !important;
}
</style>
