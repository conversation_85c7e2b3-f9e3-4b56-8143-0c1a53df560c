<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { useAssistantsStore } from '@/store'
import { Icon } from '@iconify/vue'

const assistantsStore = useAssistantsStore()

const selectedAssistantId = ref<string | null>(null)
const messages = ref<Array<{
  id: string
  text: string
  sender: 'user' | 'assistant'
  timestamp: Date
  status?: 'sending' | 'sent' | 'error'
}>>([])
const newMessage = ref('')
const isConnected = ref(false)
const isConnecting = ref(false)
const chatContainer = ref<HTMLElement>()

const selectedAssistant = computed(() => {
  if (!selectedAssistantId.value) return null
  return assistantsStore.assistants.find(a => a.id === selectedAssistantId.value)
})

const canSendMessage = computed(() => {
  return isConnected.value && newMessage.value.trim().length > 0
})

async function selectAssistant(assistantId: string) {
  selectedAssistantId.value = assistantId
  messages.value = []
  await connectToAssistant()
}

async function connectToAssistant() {
  if (!selectedAssistant.value) return

  isConnecting.value = true
  try {
    // Simulate connection delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    isConnected.value = true
    
    // Add welcome message
    addMessage({
      id: Date.now().toString(),
      text: `Hello! I'm ${selectedAssistant.value.name}. How can I help you today?`,
      sender: 'assistant',
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Failed to connect to assistant:', error)
  } finally {
    isConnecting.value = false
  }
}

function disconnectFromAssistant() {
  isConnected.value = false
  selectedAssistantId.value = null
  messages.value = []
}

function addMessage(message: any) {
  messages.value.push(message)
  nextTick(() => {
    scrollToBottom()
  })
}

async function sendMessage() {
  if (!canSendMessage.value) return

  const messageText = newMessage.value.trim()
  const userMessage = {
    id: Date.now().toString(),
    text: messageText,
    sender: 'user' as const,
    timestamp: new Date(),
    status: 'sending' as const
  }

  addMessage(userMessage)
  newMessage.value = ''

  try {
    // Update message status
    userMessage.status = 'sent'
    
    // Simulate assistant response delay
    setTimeout(() => {
      const assistantResponse = {
        id: (Date.now() + 1).toString(),
        text: generateMockResponse(messageText),
        sender: 'assistant' as const,
        timestamp: new Date()
      }
      addMessage(assistantResponse)
    }, 1000 + Math.random() * 2000)

  } catch (error) {
    userMessage.status = 'error'
    console.error('Failed to send message:', error)
  }
}

function generateMockResponse(userMessage: string): string {
  const responses = [
    "I understand your request. Let me help you with that.",
    "That's a great question! Based on my knowledge, I can tell you that...",
    "I'd be happy to assist you with that. Here's what I recommend...",
    "Thank you for asking. Let me provide you with the information you need.",
    "I see what you're looking for. Here's how I can help..."
  ]
  return responses[Math.floor(Math.random() * responses.length)]
}

function scrollToBottom() {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

onMounted(() => {
  assistantsStore.fetchAllAssistants()
})
</script>

<template>
  <v-container fluid class="pa-0 h-screen d-flex">
    <!-- Assistant Selection Sidebar -->
    <div class="bg-surface" style="width: 300px; border-right: 1px solid rgba(0,0,0,0.12);">
      <div class="pa-4 border-b">
        <h2 class="text-h6 font-weight-bold">Test Assistants</h2>
        <p class="text-body-2 text-medium-emphasis">Select an assistant to start testing</p>
      </div>
      
      <v-list nav density="compact">
        <v-list-item
          v-for="assistant in assistantsStore.assistants"
          :key="assistant.id"
          :active="selectedAssistantId === assistant.id"
          @click="selectAssistant(assistant.id)"
        >
          <template #prepend>
            <v-avatar size="32" color="primary">
              <Icon icon="mdi-robot" />
            </v-avatar>
          </template>
          <v-list-item-title>{{ assistant.name }}</v-list-item-title>
          <v-list-item-subtitle>{{ assistant.type }}</v-list-item-subtitle>
          <template #append>
            <v-chip
              v-if="selectedAssistantId === assistant.id && isConnected"
              size="x-small"
              color="success"
              variant="tonal"
            >
              Connected
            </v-chip>
          </template>
        </v-list-item>
      </v-list>

      <div v-if="assistantsStore.assistants.length === 0" class="pa-4 text-center">
        <Icon icon="mdi-robot-off" size="48" class="text-medium-emphasis mb-2" />
        <p class="text-body-2 text-medium-emphasis">No assistants available</p>
        <v-btn size="small" variant="outlined" @click="$router.push('/my-assistants')">
          Create Assistant
        </v-btn>
      </div>
    </div>

    <!-- Chat Interface -->
    <div class="flex-grow-1 d-flex flex-column">
      <!-- Chat Header -->
      <div v-if="selectedAssistant" class="pa-4 border-b bg-surface">
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-avatar size="40" color="primary" class="mr-3">
              <Icon icon="mdi-robot" />
            </v-avatar>
            <div>
              <h3 class="text-h6 font-weight-bold">{{ selectedAssistant.name }}</h3>
              <div class="d-flex align-center">
                <v-chip
                  :color="isConnected ? 'success' : 'warning'"
                  size="small"
                  variant="tonal"
                  class="mr-2"
                >
                  <v-icon
                    :icon="isConnected ? 'mdi-check-circle' : 'mdi-clock'"
                    start
                    size="small"
                  />
                  {{ isConnecting ? 'Connecting...' : isConnected ? 'Connected' : 'Disconnected' }}
                </v-chip>
                <span class="text-body-2 text-medium-emphasis">{{ selectedAssistant.type }}</span>
              </div>
            </div>
          </div>
          <v-btn
            v-if="isConnected"
            variant="outlined"
            size="small"
            @click="disconnectFromAssistant"
          >
            Disconnect
          </v-btn>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!selectedAssistant" class="flex-grow-1 d-flex align-center justify-center">
        <div class="text-center">
          <Icon icon="mdi-chat-question" size="64" class="text-medium-emphasis mb-4" />
          <h3 class="text-h6 font-weight-bold mb-2">Select an Assistant to Test</h3>
          <p class="text-body-1 text-medium-emphasis">
            Choose an assistant from the sidebar to start a conversation
          </p>
        </div>
      </div>

      <!-- Chat Messages -->
      <div
        v-else
        ref="chatContainer"
        class="flex-grow-1 overflow-y-auto pa-4"
        style="max-height: calc(100vh - 200px);"
      >
        <div
          v-for="message in messages"
          :key="message.id"
          class="mb-4"
          :class="message.sender === 'user' ? 'text-right' : 'text-left'"
        >
          <div
            class="d-inline-block pa-3 rounded-lg max-w-75"
            :class="{
              'bg-primary text-white': message.sender === 'user',
              'bg-grey-lighten-4': message.sender === 'assistant'
            }"
          >
            <p class="mb-1">{{ message.text }}</p>
            <div class="d-flex align-center justify-end">
              <span
                class="text-caption"
                :class="message.sender === 'user' ? 'text-white' : 'text-medium-emphasis'"
              >
                {{ formatTime(message.timestamp) }}
              </span>
              <v-icon
                v-if="message.sender === 'user' && message.status"
                :icon="message.status === 'sent' ? 'mdi-check' : message.status === 'error' ? 'mdi-alert-circle' : 'mdi-clock'"
                :color="message.status === 'sent' ? 'success' : message.status === 'error' ? 'error' : 'warning'"
                size="small"
                class="ml-1"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Message Input -->
      <div v-if="selectedAssistant" class="pa-4 border-t bg-surface">
        <v-text-field
          v-model="newMessage"
          placeholder="Type your message..."
          variant="outlined"
          hide-details
          :disabled="!isConnected"
          @keyup.enter="sendMessage"
        >
          <template #append-inner>
            <v-btn
              :disabled="!canSendMessage"
              icon="mdi-send"
              variant="text"
              color="primary"
              @click="sendMessage"
            />
          </template>
        </v-text-field>
      </div>
    </div>
  </v-container>
</template>

<style scoped>
.max-w-75 {
  max-width: 75%;
}

.border-b {
  border-bottom: 1px solid rgba(0,0,0,0.12);
}

.border-t {
  border-top: 1px solid rgba(0,0,0,0.12);
}
</style>
