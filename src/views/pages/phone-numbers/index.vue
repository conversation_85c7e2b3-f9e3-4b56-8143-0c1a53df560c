<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'

interface PhoneNumber {
  id: string
  number: string
  provider: 'twilio' | 'plivo' | 'other'
  status: 'active' | 'inactive' | 'pending'
  assignedAssistant?: string
  dateAdded: Date
  lastUsed?: Date
  monthlyUsage: number
  country: string
  type: 'local' | 'toll-free' | 'mobile'
}

const showAddModal = ref(false)
const selectedNumbers = ref<string[]>([])
const search = ref('')

// Mock data - this would come from backend API
const phoneNumbers = ref<PhoneNumber[]>([
  {
    id: '1',
    number: '+****************',
    provider: 'twilio',
    status: 'active',
    assignedAssistant: 'Real Estate Assistant',
    dateAdded: new Date('2024-01-10'),
    lastUsed: new Date('2024-01-15'),
    monthlyUsage: 45,
    country: 'US',
    type: 'local'
  },
  {
    id: '2',
    number: '+****************',
    provider: 'twilio',
    status: 'active',
    assignedAssistant: 'Healthcare Assistant',
    dateAdded: new Date('2024-01-05'),
    lastUsed: new Date('2024-01-14'),
    monthlyUsage: 23,
    country: 'US',
    type: 'toll-free'
  },
  {
    id: '3',
    number: '+****************',
    provider: 'plivo',
    status: 'inactive',
    dateAdded: new Date('2024-01-01'),
    monthlyUsage: 0,
    country: 'US',
    type: 'local'
  }
])

// Form data for adding new number
const newNumber = ref({
  number: '',
  provider: 'twilio' as const,
  country: 'US',
  type: 'local' as const
})

const headers = [
  { title: 'Phone Number', key: 'number', sortable: true },
  { title: 'Provider', key: 'provider', sortable: true },
  { title: 'Status', key: 'status', sortable: true },
  { title: 'Type', key: 'type', sortable: true },
  { title: 'Assigned Assistant', key: 'assignedAssistant', sortable: false },
  { title: 'Monthly Usage', key: 'monthlyUsage', sortable: true },
  { title: 'Date Added', key: 'dateAdded', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false }
]

const providerOptions = [
  { value: 'twilio', text: 'Twilio' },
  { value: 'plivo', text: 'Plivo' },
  { value: 'other', text: 'Other' }
]

const typeOptions = [
  { value: 'local', text: 'Local' },
  { value: 'toll-free', text: 'Toll-Free' },
  { value: 'mobile', text: 'Mobile' }
]

const countryOptions = [
  { value: 'US', text: 'United States' },
  { value: 'CA', text: 'Canada' },
  { value: 'UK', text: 'United Kingdom' },
  { value: 'ES', text: 'Spain' }
]

const filteredNumbers = computed(() => {
  if (!search.value) return phoneNumbers.value
  
  const searchTerm = search.value.toLowerCase()
  return phoneNumbers.value.filter(number =>
    number.number.toLowerCase().includes(searchTerm) ||
    number.provider.toLowerCase().includes(searchTerm) ||
    number.assignedAssistant?.toLowerCase().includes(searchTerm)
  )
})

function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    'active': 'success',
    'inactive': 'grey',
    'pending': 'warning'
  }
  return colors[status] || 'grey'
}

function getProviderColor(provider: string): string {
  const colors: Record<string, string> = {
    'twilio': 'red',
    'plivo': 'blue',
    'other': 'grey'
  }
  return colors[provider] || 'grey'
}

function openAddModal() {
  showAddModal.value = true
}

function closeAddModal() {
  showAddModal.value = false
  resetForm()
}

function resetForm() {
  newNumber.value = {
    number: '',
    provider: 'twilio',
    country: 'US',
    type: 'local'
  }
}

function addPhoneNumber() {
  // Validate form
  if (!newNumber.value.number) return

  // Create new phone number object
  const phoneNumber: PhoneNumber = {
    id: Date.now().toString(),
    number: newNumber.value.number,
    provider: newNumber.value.provider,
    status: 'pending',
    dateAdded: new Date(),
    monthlyUsage: 0,
    country: newNumber.value.country,
    type: newNumber.value.type
  }

  // Add to list (in real app, this would call backend API)
  phoneNumbers.value.unshift(phoneNumber)
  
  closeAddModal()
  
  // Show success message
  console.log('Phone number added:', phoneNumber)
}

function toggleStatus(phoneNumber: PhoneNumber) {
  phoneNumber.status = phoneNumber.status === 'active' ? 'inactive' : 'active'
  // In real app, this would call backend API
  console.log('Status toggled for:', phoneNumber.number)
}

function deleteNumber(phoneNumber: PhoneNumber) {
  const index = phoneNumbers.value.findIndex(n => n.id === phoneNumber.id)
  if (index > -1) {
    phoneNumbers.value.splice(index, 1)
    // In real app, this would call backend API
    console.log('Phone number deleted:', phoneNumber.number)
  }
}

function assignAssistant(phoneNumber: PhoneNumber) {
  // This would open a modal to assign assistant
  console.log('Assign assistant to:', phoneNumber.number)
}

function formatDate(date: Date): string {
  return date.toLocaleDateString()
}
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">Phone Numbers</h1>
        <p class="text-body-1 text-medium-emphasis">
          Manage your phone numbers for voice assistant calls
        </p>
      </div>
      
      <v-btn color="primary" variant="elevated" @click="openAddModal">
        <Icon icon="mdi-plus" class="mr-2" />
        Add Phone Number
      </v-btn>
    </div>

    <!-- Search and Stats -->
    <v-row class="mb-6">
      <v-col cols="12" md="6">
        <v-text-field
          v-model="search"
          placeholder="Search phone numbers..."
          variant="outlined"
          hide-details
          clearable
        >
          <template #prepend-inner>
            <Icon icon="mdi-magnify" />
          </template>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="6">
        <div class="d-flex justify-end align-center h-100">
          <div class="text-body-2 text-medium-emphasis">
            {{ filteredNumbers.length }} number{{ filteredNumbers.length !== 1 ? 's' : '' }} total
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Stats Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card class="pa-4 text-center" elevation="2">
          <div class="text-h4 font-weight-bold text-success mb-1">
            {{ phoneNumbers.filter(n => n.status === 'active').length }}
          </div>
          <div class="text-body-2 text-medium-emphasis">Active Numbers</div>
        </v-card>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-card class="pa-4 text-center" elevation="2">
          <div class="text-h4 font-weight-bold text-warning mb-1">
            {{ phoneNumbers.filter(n => n.status === 'inactive').length }}
          </div>
          <div class="text-body-2 text-medium-emphasis">Inactive Numbers</div>
        </v-card>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-card class="pa-4 text-center" elevation="2">
          <div class="text-h4 font-weight-bold text-primary mb-1">
            {{ phoneNumbers.reduce((sum, n) => sum + n.monthlyUsage, 0) }}
          </div>
          <div class="text-body-2 text-medium-emphasis">Total Monthly Usage</div>
        </v-card>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-card class="pa-4 text-center" elevation="2">
          <div class="text-h4 font-weight-bold text-info mb-1">
            {{ new Set(phoneNumbers.map(n => n.provider)).size }}
          </div>
          <div class="text-body-2 text-medium-emphasis">Providers</div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Phone Numbers Table -->
    <v-card elevation="2">
      <v-data-table
        v-model="selectedNumbers"
        :headers="headers"
        :items="filteredNumbers"
        item-value="id"
        show-select
        class="elevation-0"
      >
        <template #item.number="{ item }">
          <div class="d-flex align-center">
            <Icon icon="mdi-phone" class="mr-2" />
            <div>
              <div class="font-weight-medium">{{ item.number }}</div>
              <div class="text-caption text-medium-emphasis">{{ item.country }}</div>
            </div>
          </div>
        </template>

        <template #item.provider="{ item }">
          <v-chip
            :color="getProviderColor(item.provider)"
            size="small"
            variant="tonal"
          >
            {{ item.provider }}
          </v-chip>
        </template>

        <template #item.status="{ item }">
          <v-chip
            :color="getStatusColor(item.status)"
            size="small"
            variant="tonal"
          >
            <v-icon
              :icon="item.status === 'active' ? 'mdi-check-circle' : item.status === 'pending' ? 'mdi-clock' : 'mdi-pause-circle'"
              start
              size="small"
            />
            {{ item.status }}
          </v-chip>
        </template>

        <template #item.type="{ item }">
          <span class="text-capitalize">{{ item.type }}</span>
        </template>

        <template #item.assignedAssistant="{ item }">
          <div v-if="item.assignedAssistant" class="d-flex align-center">
            <v-avatar size="24" color="primary" class="mr-2">
              <Icon icon="mdi-robot" size="12" />
            </v-avatar>
            <span class="text-body-2">{{ item.assignedAssistant }}</span>
          </div>
          <v-btn
            v-else
            size="small"
            variant="outlined"
            @click="assignAssistant(item)"
          >
            Assign
          </v-btn>
        </template>

        <template #item.monthlyUsage="{ item }">
          <div class="text-center">
            <div class="font-weight-medium">{{ item.monthlyUsage }}</div>
            <div class="text-caption text-medium-emphasis">calls</div>
          </div>
        </template>

        <template #item.dateAdded="{ item }">
          {{ formatDate(item.dateAdded) }}
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <v-btn
              :icon="item.status === 'active' ? 'mdi-pause' : 'mdi-play'"
              size="small"
              variant="text"
              :color="item.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(item)"
            />
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="text"
              color="error"
              @click="deleteNumber(item)"
            />
          </div>
        </template>

        <template #no-data>
          <div class="text-center py-8">
            <Icon icon="mdi-phone-off" size="64" class="text-medium-emphasis mb-4" />
            <h3 class="text-h6 font-weight-bold mb-2">No phone numbers found</h3>
            <p class="text-body-1 text-medium-emphasis mb-4">
              {{ search ? 'Try adjusting your search terms.' : 'Add your first phone number to get started.' }}
            </p>
            <v-btn
              v-if="!search"
              color="primary"
              variant="elevated"
              @click="openAddModal"
            >
              Add Phone Number
            </v-btn>
          </div>
        </template>
      </v-data-table>
    </v-card>
  </v-container>

  <!-- Add Phone Number Modal -->
  <v-dialog v-model="showAddModal" max-width="500">
    <v-card>
      <v-card-title>
        <span class="text-h6">Add Phone Number</span>
      </v-card-title>
      
      <v-card-text>
        <v-form>
          <v-text-field
            v-model="newNumber.number"
            label="Phone Number"
            placeholder="+****************"
            variant="outlined"
            class="mb-4"
            required
          />
          
          <v-select
            v-model="newNumber.provider"
            :items="providerOptions"
            item-title="text"
            item-value="value"
            label="Provider"
            variant="outlined"
            class="mb-4"
          />
          
          <v-select
            v-model="newNumber.country"
            :items="countryOptions"
            item-title="text"
            item-value="value"
            label="Country"
            variant="outlined"
            class="mb-4"
          />
          
          <v-select
            v-model="newNumber.type"
            :items="typeOptions"
            item-title="text"
            item-value="value"
            label="Number Type"
            variant="outlined"
          />
        </v-form>
        
        <v-alert type="info" variant="tonal" class="mt-4">
          <strong>Note:</strong> Backend integration is pending. This is currently frontend-only functionality.
        </v-alert>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn variant="text" @click="closeAddModal">Cancel</v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :disabled="!newNumber.number"
          @click="addPhoneNumber"
        >
          Add Number
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.gap-1 {
  gap: 4px;
}
</style>
