<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAssistantsStore, useProfileStore } from '@/store'
import { Icon } from '@iconify/vue'

const router = useRouter()
const assistantsStore = useAssistantsStore()
const profileStore = useProfileStore()

const selectedSector = ref('all')
const showTemplateModal = ref(false)
const selectedTemplate = ref<any>(null)

// Template data - this would come from backend API
const templates = ref([
  {
    id: 'real-estate-appointments',
    title: 'Real Estate Appointments',
    description: 'Manage property viewings and client appointments efficiently',
    sector: 'real-estate',
    icon: 'mdi-home-city',
    features: ['Appointment scheduling', 'Property information', 'Client qualification'],
    type: 'reservations'
  },
  {
    id: 'real-estate-prospecting',
    title: 'Property Prospecting',
    description: 'Automate calls to property owners for acquisition opportunities',
    sector: 'real-estate',
    icon: 'mdi-phone-outgoing',
    features: ['Lead qualification', 'Property valuation', 'Owner contact'],
    type: 'prospection'
  },
  {
    id: 'real-estate-advisor',
    title: 'Real Estate Advisor',
    description: 'Guide clients through property search and selection process',
    sector: 'real-estate',
    icon: 'mdi-account-tie',
    features: ['Property recommendations', 'Market insights', 'Client consultation'],
    type: 'advisor'
  },
  {
    id: 'healthcare-appointments',
    title: 'Healthcare Appointments',
    description: 'Schedule medical appointments and manage patient inquiries',
    sector: 'healthcare',
    icon: 'mdi-medical-bag',
    features: ['Appointment booking', 'Symptom assessment', 'Insurance verification'],
    type: 'reservations'
  },
  {
    id: 'restaurant-reservations',
    title: 'Restaurant Reservations',
    description: 'Handle table bookings and customer service inquiries',
    sector: 'hospitality',
    icon: 'mdi-silverware-fork-knife',
    features: ['Table booking', 'Menu information', 'Special requests'],
    type: 'reservations'
  },
  {
    id: 'fitness-bookings',
    title: 'Fitness Class Bookings',
    description: 'Manage gym class schedules and member inquiries',
    sector: 'fitness',
    icon: 'mdi-dumbbell',
    features: ['Class scheduling', 'Membership info', 'Trainer availability'],
    type: 'reservations'
  }
])

const sectors = ref([
  { value: 'all', text: 'All Sectors' },
  { value: 'real-estate', text: 'Real Estate' },
  { value: 'healthcare', text: 'Healthcare' },
  { value: 'hospitality', text: 'Hospitality' },
  { value: 'fitness', text: 'Fitness' }
])

const filteredTemplates = computed(() => {
  if (selectedSector.value === 'all') {
    return templates.value
  }
  return templates.value.filter(template => template.sector === selectedSector.value)
})

function openTemplateModal(template: any) {
  selectedTemplate.value = template
  showTemplateModal.value = true
}

function closeTemplateModal() {
  showTemplateModal.value = false
  selectedTemplate.value = null
}

async function useTemplate() {
  if (!selectedTemplate.value) return

  try {
    // Check if company name is set
    if (!profileStore.companyName) {
      // This would trigger the company name modal
      router.push('/my-assistants')
      return
    }

    // Create assistant from template
    await assistantsStore.createNewAssistant(
      selectedTemplate.value.type,
      profileStore.companyName,
      selectedTemplate.value
    )

    closeTemplateModal()
    router.push('/my-assistants')
  } catch (error) {
    console.error('Error creating assistant from template:', error)
  }
}
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-h4 font-weight-bold mb-2">Template Marketplace</h1>
      <p class="text-body-1 text-medium-emphasis">
        Choose from our collection of industry-specific assistant templates
      </p>
    </div>

    <!-- Sector Filter -->
    <div class="mb-6">
      <v-select
        v-model="selectedSector"
        :items="sectors"
        item-title="text"
        item-value="value"
        label="Filter by Sector"
        variant="outlined"
        class="max-w-sm"
        hide-details
      />
    </div>

    <!-- Templates Grid -->
    <v-row>
      <v-col
        v-for="template in filteredTemplates"
        :key="template.id"
        cols="12"
        sm="6"
        md="4"
        lg="3"
      >
        <v-card
          class="h-100 d-flex flex-column"
          elevation="2"
          hover
          @click="openTemplateModal(template)"
        >
          <v-card-text class="flex-grow-1">
            <div class="d-flex align-center mb-4">
              <v-avatar size="48" color="primary" class="mr-3">
                <Icon :icon="template.icon" size="24" />
              </v-avatar>
              <div>
                <h3 class="text-h6 font-weight-bold">{{ template.title }}</h3>
                <v-chip size="small" variant="tonal" color="primary">
                  {{ sectors.find(s => s.value === template.sector)?.text }}
                </v-chip>
              </div>
            </div>
            
            <p class="text-body-2 text-medium-emphasis mb-4">
              {{ template.description }}
            </p>

            <div class="mb-4">
              <h4 class="text-subtitle-2 font-weight-bold mb-2">Key Features:</h4>
              <ul class="text-body-2">
                <li v-for="feature in template.features" :key="feature" class="mb-1">
                  {{ feature }}
                </li>
              </ul>
            </div>
          </v-card-text>

          <v-card-actions>
            <v-btn
              color="primary"
              variant="elevated"
              class="w-100"
              @click.stop="openTemplateModal(template)"
            >
              <Icon icon="mdi-plus" class="mr-2" />
              Use Template
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="filteredTemplates.length === 0" class="text-center py-12">
      <Icon icon="mdi-store-search" size="64" class="text-medium-emphasis mb-4" />
      <h3 class="text-h6 font-weight-bold mb-2">No templates found</h3>
      <p class="text-body-1 text-medium-emphasis">
        Try selecting a different sector or check back later for new templates.
      </p>
    </div>
  </v-container>

  <!-- Template Details Modal -->
  <v-dialog v-model="showTemplateModal" max-width="600">
    <v-card v-if="selectedTemplate">
      <v-card-title class="d-flex align-center">
        <v-avatar size="40" color="primary" class="mr-3">
          <Icon :icon="selectedTemplate.icon" size="20" />
        </v-avatar>
        <div>
          <h2 class="text-h6">{{ selectedTemplate.title }}</h2>
          <v-chip size="small" variant="tonal" color="primary">
            {{ sectors.find(s => s.value === selectedTemplate.sector)?.text }}
          </v-chip>
        </div>
        <v-spacer />
        <v-btn icon="mdi-close" variant="text" @click="closeTemplateModal" />
      </v-card-title>

      <v-card-text>
        <p class="text-body-1 mb-4">{{ selectedTemplate.description }}</p>
        
        <h3 class="text-subtitle-1 font-weight-bold mb-2">Features Included:</h3>
        <v-list density="compact">
          <v-list-item
            v-for="feature in selectedTemplate.features"
            :key="feature"
            class="px-0"
          >
            <template #prepend>
              <v-icon icon="mdi-check-circle" color="success" size="small" />
            </template>
            <v-list-item-title>{{ feature }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-card-text>

      <v-card-actions class="justify-end">
        <v-btn variant="text" @click="closeTemplateModal">Cancel</v-btn>
        <v-btn color="primary" variant="elevated" @click="useTemplate">
          <Icon icon="mdi-plus" class="mr-2" />
          Create Assistant
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.max-w-sm {
  max-width: 300px;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}
</style>
