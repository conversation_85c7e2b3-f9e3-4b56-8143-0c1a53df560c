<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAssistantsStore, useContactsStore } from '@/store'
import { Icon } from '@iconify/vue'

const route = useRoute()
const assistantsStore = useAssistantsStore()
const contactsStore = useContactsStore()

const selectedConversationId = ref<string | null>(null)
const filterAssistant = ref<string>('all')
const filterContact = ref<string>('all')
const filterDate = ref<string>('all')
const search = ref('')

// Mock conversations data - this would come from backend API
const conversations = ref([
  {
    id: '1',
    contactName: '<PERSON>',
    contactPhone: '+1234567890',
    assistantName: 'Real Estate Assistant',
    assistantId: 'assistant-1',
    startTime: new Date('2024-01-15T10:30:00'),
    endTime: new Date('2024-01-15T10:45:00'),
    duration: 15,
    status: 'completed',
    summary: 'Customer inquiry about property viewing',
    messages: [
      { id: '1', sender: 'assistant', text: 'Hello! How can I help you today?', timestamp: new Date('2024-01-15T10:30:00') },
      { id: '2', sender: 'user', text: 'Hi, I\'m interested in viewing the property on Main Street.', timestamp: new Date('2024-01-15T10:30:30') },
      { id: '3', sender: 'assistant', text: 'Great! I can help you schedule a viewing. What day works best for you?', timestamp: new Date('2024-01-15T10:31:00') },
      { id: '4', sender: 'user', text: 'How about this Saturday morning?', timestamp: new Date('2024-01-15T10:31:30') },
      { id: '5', sender: 'assistant', text: 'Perfect! I\'ve scheduled you for Saturday at 10 AM. You\'ll receive a confirmation shortly.', timestamp: new Date('2024-01-15T10:32:00') }
    ]
  },
  {
    id: '2',
    contactName: 'Jane Smith',
    contactPhone: '+**********',
    assistantName: 'Healthcare Assistant',
    assistantId: 'assistant-2',
    startTime: new Date('2024-01-15T14:20:00'),
    endTime: new Date('2024-01-15T14:35:00'),
    duration: 15,
    status: 'completed',
    summary: 'Appointment booking for medical consultation',
    messages: [
      { id: '1', sender: 'assistant', text: 'Hello! I\'m here to help you book an appointment.', timestamp: new Date('2024-01-15T14:20:00') },
      { id: '2', sender: 'user', text: 'I need to see a doctor for a check-up.', timestamp: new Date('2024-01-15T14:20:30') },
      { id: '3', sender: 'assistant', text: 'I can help with that. Do you have a preferred doctor or time?', timestamp: new Date('2024-01-15T14:21:00') }
    ]
  },
  {
    id: '3',
    contactName: 'Mike Johnson',
    contactPhone: '+**********',
    assistantName: 'Real Estate Assistant',
    assistantId: 'assistant-1',
    startTime: new Date('2024-01-14T16:45:00'),
    endTime: new Date('2024-01-14T17:00:00'),
    duration: 15,
    status: 'completed',
    summary: 'Property information request',
    messages: [
      { id: '1', sender: 'assistant', text: 'Hi there! How can I assist you today?', timestamp: new Date('2024-01-14T16:45:00') },
      { id: '2', sender: 'user', text: 'Can you tell me about the properties in downtown area?', timestamp: new Date('2024-01-14T16:45:30') }
    ]
  }
])

const dateFilters = [
  { value: 'all', text: 'All Time' },
  { value: 'today', text: 'Today' },
  { value: 'week', text: 'This Week' },
  { value: 'month', text: 'This Month' }
]

const filteredConversations = computed(() => {
  let filtered = conversations.value

  // Filter by search
  if (search.value) {
    const searchTerm = search.value.toLowerCase()
    filtered = filtered.filter(conv =>
      conv.contactName.toLowerCase().includes(searchTerm) ||
      conv.assistantName.toLowerCase().includes(searchTerm) ||
      conv.summary.toLowerCase().includes(searchTerm)
    )
  }

  // Filter by assistant
  if (filterAssistant.value !== 'all') {
    filtered = filtered.filter(conv => conv.assistantId === filterAssistant.value)
  }

  // Filter by date
  if (filterDate.value !== 'all') {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(conv => {
      const convDate = new Date(conv.startTime)

      switch (filterDate.value) {
        case 'today':
          return convDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return convDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          return convDate >= monthAgo
        default:
          return true
      }
    })
  }

  return filtered.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
})

const selectedConversation = computed(() => {
  if (!selectedConversationId.value) return null
  return conversations.value.find(conv => conv.id === selectedConversationId.value)
})

const assistantOptions = computed(() => {
  const assistants = [{ value: 'all', text: 'All Assistants' }]
  const uniqueAssistants = [...new Set(conversations.value.map(conv => conv.assistantId))]

  uniqueAssistants.forEach(assistantId => {
    const conv = conversations.value.find(c => c.assistantId === assistantId)
    if (conv) {
      assistants.push({
        value: assistantId,
        text: conv.assistantName
      })
    }
  })

  return assistants
})

function selectConversation(conversationId: string) {
  selectedConversationId.value = conversationId
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

function formatDate(date: Date): string {
  return date.toLocaleDateString([], {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}h ${remainingMinutes}m`
}

function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    'completed': 'success',
    'in-progress': 'warning',
    'failed': 'error',
    'cancelled': 'grey'
  }
  return colors[status] || 'grey'
}

function exportConversation() {
  if (!selectedConversation.value) return

  // This would implement conversation export functionality
  console.log('Exporting conversation:', selectedConversation.value.id)
}

onMounted(() => {
  assistantsStore.fetchAllAssistants()
  contactsStore.initialize()

  // Handle filtering from contacts page
  if (route.query.contactId && route.query.contactName) {
    const contactName = route.query.contactName as string
    search.value = contactName

    // Find and select the conversation for this contact
    const contactConversation = conversations.value.find(conv =>
      conv.contactName.toLowerCase().includes(contactName.toLowerCase())
    )

    if (contactConversation) {
      selectedConversationId.value = contactConversation.id
    }
  }
})
</script>

<template>
  <v-container fluid class="pa-0 h-screen d-flex">
    <!-- Conversations List -->
    <div class="bg-surface" style="width: 400px; border-right: 1px solid rgba(0,0,0,0.12);">
      <!-- Header -->
      <div class="pa-4 border-b">
        <h2 class="text-h6 font-weight-bold mb-4">Conversations</h2>

        <!-- Search -->
        <v-text-field
          v-model="search"
          placeholder="Search conversations..."
          variant="outlined"
          density="compact"
          hide-details
          clearable
          class="mb-4"
        >
          <template #prepend-inner>
            <Icon icon="mdi-magnify" />
          </template>
        </v-text-field>

        <!-- Filters -->
        <div class="d-flex flex-column gap-2">
          <v-select
            v-model="filterAssistant"
            :items="assistantOptions"
            item-title="text"
            item-value="value"
            label="Assistant"
            variant="outlined"
            density="compact"
            hide-details
          />

          <v-select
            v-model="filterDate"
            :items="dateFilters"
            item-title="text"
            item-value="value"
            label="Date Range"
            variant="outlined"
            density="compact"
            hide-details
          />
        </div>
      </div>

      <!-- Conversations List -->
      <div class="overflow-y-auto" style="height: calc(100vh - 200px);">
        <v-list nav density="compact">
          <v-list-item
            v-for="conversation in filteredConversations"
            :key="conversation.id"
            :active="selectedConversationId === conversation.id"
            @click="selectConversation(conversation.id)"
            class="py-3"
          >
            <template #prepend>
              <v-avatar size="40" color="primary">
                <span class="text-white text-caption">
                  {{ conversation.contactName.split(' ').map(n => n[0]).join('').toUpperCase() }}
                </span>
              </v-avatar>
            </template>

            <v-list-item-title class="font-weight-medium">
              {{ conversation.contactName }}
            </v-list-item-title>

            <v-list-item-subtitle class="mb-1">
              {{ conversation.assistantName }}
            </v-list-item-subtitle>

            <v-list-item-subtitle class="text-caption">
              {{ formatDate(conversation.startTime) }} • {{ formatDuration(conversation.duration) }}
            </v-list-item-subtitle>

            <template #append>
              <div class="d-flex flex-column align-end">
                <v-chip
                  :color="getStatusColor(conversation.status)"
                  size="x-small"
                  variant="tonal"
                  class="mb-1"
                >
                  {{ conversation.status }}
                </v-chip>
              </div>
            </template>
          </v-list-item>
        </v-list>

        <!-- Empty State -->
        <div v-if="filteredConversations.length === 0" class="pa-4 text-center">
          <Icon icon="mdi-message-text-outline" size="48" class="text-medium-emphasis mb-2" />
          <p class="text-body-2 text-medium-emphasis">
            {{ search || filterAssistant !== 'all' || filterDate !== 'all'
              ? 'No conversations match your filters'
              : 'No conversations yet' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Conversation Detail -->
    <div class="flex-grow-1 d-flex flex-column">
      <!-- Empty State -->
      <div v-if="!selectedConversation" class="flex-grow-1 d-flex align-center justify-center">
        <div class="text-center">
          <Icon icon="mdi-message-text" size="64" class="text-medium-emphasis mb-4" />
          <h3 class="text-h6 font-weight-bold mb-2">Select a Conversation</h3>
          <p class="text-body-1 text-medium-emphasis">
            Choose a conversation from the list to view details
          </p>
        </div>
      </div>

      <!-- Conversation Header -->
      <div v-else class="pa-4 border-b bg-surface">
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-avatar size="48" color="primary" class="mr-4">
              <span class="text-white">
                {{ selectedConversation.contactName.split(' ').map(n => n[0]).join('').toUpperCase() }}
              </span>
            </v-avatar>
            <div>
              <h3 class="text-h6 font-weight-bold">{{ selectedConversation.contactName }}</h3>
              <div class="d-flex align-center gap-2">
                <v-chip size="small" variant="tonal" color="primary">
                  {{ selectedConversation.assistantName }}
                </v-chip>
                <v-chip
                  :color="getStatusColor(selectedConversation.status)"
                  size="small"
                  variant="tonal"
                >
                  {{ selectedConversation.status }}
                </v-chip>
              </div>
              <p class="text-body-2 text-medium-emphasis mt-1">
                {{ formatDate(selectedConversation.startTime) }} •
                Duration: {{ formatDuration(selectedConversation.duration) }}
              </p>
            </div>
          </div>

          <v-btn variant="outlined" size="small" @click="exportConversation">
            <Icon icon="mdi-download" class="mr-2" />
            Export
          </v-btn>
        </div>
      </div>

      <!-- Messages -->
      <div v-if="selectedConversation" class="flex-grow-1 overflow-y-auto pa-4">
        <div
          v-for="message in selectedConversation.messages"
          :key="message.id"
          class="mb-4"
          :class="message.sender === 'user' ? 'text-right' : 'text-left'"
        >
          <div
            class="d-inline-block pa-3 rounded-lg max-w-75"
            :class="{
              'bg-primary text-white': message.sender === 'user',
              'bg-grey-lighten-4': message.sender === 'assistant'
            }"
          >
            <p class="mb-1">{{ message.text }}</p>
            <div class="text-caption" :class="message.sender === 'user' ? 'text-white' : 'text-medium-emphasis'">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </v-container>
</template>

<style scoped>
.max-w-75 {
  max-width: 75%;
}

.border-b {
  border-bottom: 1px solid rgba(0,0,0,0.12);
}

.gap-2 {
  gap: 8px;
}
</style>
