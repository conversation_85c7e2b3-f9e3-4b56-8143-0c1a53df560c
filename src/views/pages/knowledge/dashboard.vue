<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import CardButton from '@/components/CardButton.vue'
import { useKnowledgeBasesStore } from '@/store/useKnowledgeBaseStore'
import type { KnowledgeBaseTextInput, AddSourcesFormData } from '@/services/knowledgeBaseService'
import type { KnowledgeBase } from '@/types/domain'

interface PredefinedKB {
  id: string
  title: string
  icon: string
  description: string
}

const predefinedKBs: PredefinedKB[] = [
  {
    id: 'inventory',
    title: 'Inventario Inmobiliario',
    description: 'Propiedades disponibles (precio, ubicación, tipo, características, etc.)',
    icon: 'solar:clipboard-list-broken',
  },
  {
    id: 'process',
    title: 'Proceso de Compra Inmobiliaria',
    description: 'Pasos del proceso (reserva, contrato, pagos, notario, documentación, etc.)',
    icon: 'solar:dollar-broken',
  },
  {
    id: 'faq',
    title: 'Manual de Objeciones y FAQs',
    description: 'Objeciones frecuentes, preguntas y respuestas persuasivas.',
    icon: 'solar:question-circle-broken',
  },
  {
    id: 'areas',
    title: 'Guía de Zonas y Perfiles',
    description: 'Descripción de zonas, estilos de vida y perfiles de clientes.',
    icon: 'solar:city-broken',
  },
  {
    id: 'policies',
    title: 'Políticas, términos y condiciones',
    description:
      'Políticas de la agencia. Requisitos de compra, políticas de visitas, de mascotas, de cancelaciones, etc.',
    icon: 'solar:shield-keyhole-broken',
  },
  {
    id: 'custom',
    title: 'Personalizado',
    description: 'Crea tu propio conjunto de datos a medida.',
    icon: 'solar:add-circle-broken',
  }
]

const knowledgeBaseStore = useKnowledgeBasesStore()

// Controla cuándo mostrar las tarjetas (true después de hacer clic en “Empieza ahora”):
const showCards = ref(false)

// Diálogo de “editar” abierto o cerrado:
const dialog = ref(false)
// Tipo de KB seleccionado al clicar una tarjeta:
const selectedKBType = ref<string | null>(null)

// Form data
const formData = ref({
  name: '',
  customTrigger: '',
  enableAutoRefresh: true,
  texts: [] as KnowledgeBaseTextInput[],
  urls: [] as string[],
  files: [] as File[]
})

// Loading states
const isCreating = ref(false)
const isAddingSources = ref(false)
const isDeletingSource = ref(false)

// Navigation state
const currentNavigationIndex = ref(0) // 0 = create form, 1+ = existing knowledge bases
const isEditMode = ref(false)

// Form validation
const nameRules = [
  (v: string) => !!v || 'El nombre es requerido',
  (v: string) => v.length >= 3 || 'El nombre debe tener al menos 3 caracteres'
]

/**
 * Add new text input
 */
function addTextInput() {
  formData.value.texts.push({ text: '', title: '' })
}

/**
 * Remove text input
 */
function removeTextInput(index: number) {
  formData.value.texts.splice(index, 1)
}

/**
 * Add new URL
 */
function addUrl() {
  formData.value.urls.push('')
}

/**
 * Remove URL
 */
function removeUrl(index: number) {
  formData.value.urls.splice(index, 1)
}

/**
 * Handle file selection
 */
function onFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files) {
    formData.value.files = Array.from(target.files)
  }
}

/**
 * Reset form data
 */
function resetForm() {
  formData.value = {
    name: '',
    customTrigger: '',
    enableAutoRefresh: true,
    texts: [],
    urls: [],
    files: []
  }
}

function startNow(): void {
  showCards.value = true
}

function openDialog(typeId: string): void {
  selectedKBType.value = typeId

  // Check if there are existing knowledge bases of this type
  const existingKBs = getKnowledgeBasesByType(typeId)

  if (existingKBs.length > 0) {
    // Start with the first knowledge base (navigation index 1)
    currentNavigationIndex.value = 1
    switchToEditMode()
  } else {
    // Start in create mode (navigation index 0)
    currentNavigationIndex.value = 0
    switchToCreateMode()
  }

  dialog.value = true
}

function closeDialog(): void {
  dialog.value = false
  selectedKBType.value = null
  currentNavigationIndex.value = 0
  isEditMode.value = false
  knowledgeBaseStore.setSelectedKBId(null)
  resetForm()
}

async function addSourcesToKB(): Promise<void> {
  const kb = currentKnowledgeBase.value
  if (!kb) return

  // Create AddSourcesFormData object
  const sourcesFormData: AddSourcesFormData = {
    texts: formData.value.texts.length > 0 ? formData.value.texts : undefined,
    urls: formData.value.urls.length > 0 ? formData.value.urls : undefined,
    files: formData.value.files.length > 0 ? formData.value.files : undefined,
  }

  // Check if there are any sources to add
  if (!sourcesFormData.texts && !sourcesFormData.urls && !sourcesFormData.files) {
    return
  }

  isAddingSources.value = true
  try {
    await knowledgeBaseStore.addKnowledgeBaseSourcesById(kb.id, sourcesFormData)

    // Clear form after successful addition
    formData.value.texts = []
    formData.value.urls = []
    formData.value.files = []

    console.log('✅ Sources added successfully')
  } catch (error) {
    console.error('❌ Error adding sources:', error)
  } finally {
    isAddingSources.value = false
  }
}

async function deleteSource(sourceId: string): Promise<void> {
  const kb = currentKnowledgeBase.value
  if (!kb) return

  isDeletingSource.value = true
  try {
    await knowledgeBaseStore.deleteKnowledgeBaseSourceById(kb.id, sourceId)
    console.log('✅ Source deleted successfully')
  } catch (error) {
    console.error('❌ Error deleting source:', error)
  } finally {
    isDeletingSource.value = false
  }
}

async function saveKB(): Promise<void> {
  if (!selectedKBType.value) return

  if (isInEditMode.value) {
    // In edit mode, only add sources
    await addSourcesToKB()
  } else {
    // In create mode, create new knowledge base
    isCreating.value = true
    try {
      await knowledgeBaseStore.createNewKnowledgeBase({
        name: formData.value.name,
        type: selectedKBType.value,
        customTrigger: formData.value.customTrigger || undefined,

        texts: formData.value.texts.length > 0 ? formData.value.texts : undefined,
        urls: formData.value.urls.length > 0 ? formData.value.urls : undefined,
        files: formData.value.files.length > 0 ? formData.value.files : undefined,
      })

      // Cerrar el diálogo y resetear
      closeDialog()
    } catch (error) {
      console.error('❌ Error creating knowledge base:', error)
    } finally {
      isCreating.value = false
    }
  }
}

function getKnowledgeBasesByType(type: string): KnowledgeBase[] {
  return knowledgeBaseStore.knowledgeBases.filter(kb => kb.type === type)
}

function getKnowledgeBaseInitial(name: string): string {
  return name.charAt(0).toUpperCase()
}

function getAvatarColor(status: string): string {
  switch (status) {
    case 'ready':
      return 'success'
    case 'in_progress':
      return 'warning'
    case 'error':
      return 'error'
    default:
      return 'primary'
  }
}

/**
 * Navigate to the next item in the circular navigation
 * Order: Create Form (0) → KB1 (1) → KB2 (2) → ... → KBn (n) → Create Form (0)
 */
function navigateNext(): void {
  const totalItems = totalNavigationItems.value

  if (totalItems <= 1) {
    // Only create form exists, no navigation needed
    return
  }

  // Move to next index, wrapping around to 0 if we reach the end
  currentNavigationIndex.value = (currentNavigationIndex.value + 1) % totalItems

  // Update mode based on new index
  if (currentNavigationIndex.value === 0) {
    switchToCreateMode()
  } else {
    switchToEditMode()
  }
}

/**
 * Navigate to the previous item in the circular navigation
 * Order: Create Form (0) ← KB1 (1) ← KB2 (2) ← ... ← KBn (n) ← Create Form (0)
 */
function navigatePrevious(): void {
  const totalItems = totalNavigationItems.value

  if (totalItems <= 1) {
    // Only create form exists, no navigation needed
    return
  }

  // Move to previous index, wrapping around to the end if we're at 0
  currentNavigationIndex.value = currentNavigationIndex.value === 0
    ? totalItems - 1
    : currentNavigationIndex.value - 1

  // Update mode based on new index
  if (currentNavigationIndex.value === 0) {
    switchToCreateMode()
  } else {
    switchToEditMode()
  }
}

function switchToEditMode(): void {
  isEditMode.value = true
  loadCurrentKnowledgeBase()
}

function switchToCreateMode(): void {
  isEditMode.value = false
  resetForm()

  // Set default name based on type
  const kbDetails = predefinedKBs.find(kb => kb.id === selectedKBType.value)
  if (kbDetails) {
    formData.value.name = kbDetails.title
  }
}

function loadCurrentKnowledgeBase(): void {
  const kb = currentKnowledgeBase.value
  if (!kb) return

  knowledgeBaseStore.setSelectedKBId(kb.id)

  // Pre-fill the form with existing data
  formData.value.name = kb.name
  formData.value.customTrigger = kb.customTrigger || ''
  formData.value.enableAutoRefresh = true // Default value for edit mode

  // Clear sources for edit mode (only add/delete allowed)
  formData.value.texts = []
  formData.value.urls = []
  formData.value.files = []
}

function onAvatarClick(knowledgeBase: KnowledgeBase): void {
  selectedKBType.value = knowledgeBase.type

  // Find the index of the clicked knowledge base
  const kbs = getKnowledgeBasesByType(knowledgeBase.type)
  const kbIndex = kbs.findIndex(kb => kb.id === knowledgeBase.id)

  // Set navigation index (KB index + 1 because create form is at index 0)
  currentNavigationIndex.value = kbIndex + 1

  // Switch to edit mode and load the knowledge base
  switchToEditMode()
  dialog.value = true
}

/**
 * Get all knowledge bases of the current type
 */
const currentTypeKnowledgeBases = computed(() => {
  if (!selectedKBType.value) return []
  return getKnowledgeBasesByType(selectedKBType.value)
})

/**
 * Total navigation items: create form (index 0) + existing knowledge bases (1+)
 */
const totalNavigationItems = computed(() => {
  return 1 + currentTypeKnowledgeBases.value.length // 1 for create form + existing KBs
})

/**
 * Current knowledge base index (adjusted for navigation index)
 */
const currentKBIndex = computed(() => {
  // If we're on create form (index 0), return -1 to indicate no KB selected
  if (currentNavigationIndex.value === 0) return -1
  // Otherwise, return the KB index (navigation index - 1)
  return currentNavigationIndex.value - 1
})

/**
 * Get the current knowledge base based on navigation index
 */
const currentKnowledgeBase = computed(() => {
  const kbs = currentTypeKnowledgeBases.value
  const kbIndex = currentKBIndex.value
  if (kbIndex < 0 || kbIndex >= kbs.length) return null
  return kbs[kbIndex]
})

/**
 * Determine if we're in edit mode (navigation index > 0 and valid KB exists)
 */
const isInEditMode = computed(() => {
  return currentNavigationIndex.value > 0 && currentKnowledgeBase.value !== null
})

/**
 * Check if navigation buttons should be disabled
 */
const isNavigationDisabled = computed(() => {
  return totalNavigationItems.value <= 1 // Only disable if only create form exists
})

const dialogTitle = computed(() => {
  if (isInEditMode.value && currentKnowledgeBase.value) {
    return `Editar: ${currentKnowledgeBase.value.name}`
  } else if (selectedKBDetails.value) {
    return `Crear: ${selectedKBDetails.value.title}`
  }
  return '...'
})

const actionButtonText = computed(() => {
  return isInEditMode.value ? 'Actualizar' : 'Crear'
})

const selectedKBDetails = computed<PredefinedKB | null>(() => {
  if (!selectedKBType.value) return null
  return predefinedKBs.find((kb) => kb.id === selectedKBType.value) || null
})

onMounted(() => {
  knowledgeBaseStore.fetchAllKnowledgeBases();
});
</script>

<template>
  <v-container
    v-if="knowledgeBaseStore.knowledgeBases.length === 0 && !showCards"
    class="d-flex flex-row fill-height h-screen"
  >
    <v-row class="justify-center mt-n16">
      <v-col cols="12" sm="8" md="6" lg="5">
        <div class="d-flex flex-column align-start">
          <div class="align-self-start mb-6">
            <div class="icon-circle d-flex align-center justify-center text-borderColor">
              <Icon
                icon="mdi:book-open-page-variant"
                width="40"
                height="40"
                class="text-grey-darken-1"
              />
            </div>
          </div>

          <h1 class="text-h4 font-weight-bold mb-2">
            Comienza a integrar los datos de tu negocio
          </h1>

          <p class="text-body-1 text-medium-emphasis mb-6">
            Centraliza toda la información en una colección de documentos<br />
            que la IA del asistente pueda consultar para proporcionar respuestas
            precisas.
          </p>

          <v-btn
            variant="elevated"
            color="primary"
            min-width="150"
            max-width="150"
            class="rounded-lg font-weight-medium text-none"
            @click="startNow"
          >
            Empieza ahora
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>

  <div v-else>
    <v-container fluid class="pa-8">
      <h2 class="text-h5 font-weight-bold mb-4">Tus Datos de Negocio</h2>
      <v-row dense>
        <v-col
          v-for="kb in predefinedKBs"
          :key="kb.id"
          cols="12"
          sm="6"
          md="4"
          lg="2"
          class="d-flex mt-12"
        >
          <CardButton
            :title="kb.title"
            :subtitle="kb.description"
            :icon="kb.icon"
            @click="openDialog(kb.id)"
          >
            <template #badge>
              <!-- Knowledge Base Avatars -->
              <div
                v-if="getKnowledgeBasesByType(kb.id).length > 0"
                class="d-flex flex-wrap gap-1 align-center"
              >
                <v-tooltip
                  v-for="knowledgeBase in getKnowledgeBasesByType(kb.id).slice(0, 3)"
                  :key="knowledgeBase.id"
                  :text="knowledgeBase.name"
                  location="top"
                >
                  <template #activator="{ props }">
                    <v-avatar
                      v-bind="props"
                      size="24"
                      :color="getAvatarColor(knowledgeBase.status)"
                      class="avatar-clickable"
                      @click.stop="onAvatarClick(knowledgeBase)"
                    >
                      <span class="text-white text-caption font-weight-bold">
                        {{ getKnowledgeBaseInitial(knowledgeBase.name) }}
                      </span>
                    </v-avatar>
                  </template>
                </v-tooltip>

                <!-- Show +N indicator if there are more than 3 knowledge bases -->
                <v-tooltip
                  v-if="getKnowledgeBasesByType(kb.id).length > 3"
                  :text="`${getKnowledgeBasesByType(kb.id).length - 3} más`"
                  location="top"
                >
                  <template #activator="{ props }">
                    <v-avatar
                      v-bind="props"
                      size="24"
                      color="grey-lighten-1"
                      class="avatar-clickable"
                    >
                      <span class="text-grey-darken-2 text-caption font-weight-bold">
                        +{{ getKnowledgeBasesByType(kb.id).length - 3 }}
                      </span>
                    </v-avatar>
                  </template>
                </v-tooltip>
              </div>
            </template>
          </CardButton>
        </v-col>
      </v-row>
    </v-container>
  </div>

  <v-dialog v-model="dialog" max-width="600">
    <v-card>
      <v-toolbar flat color="primary" dark>
        <!-- Navigation Left -->
        <v-btn
          icon="mdi-chevron-left"
          @click="navigatePrevious"
          :disabled="isNavigationDisabled"
        />

        <v-toolbar-title>
          {{ dialogTitle }}
        </v-toolbar-title>

        <!-- Navigation Right -->
        <v-btn
          icon="mdi-chevron-right"
          @click="navigateNext"
          :disabled="isNavigationDisabled"
        />

        <v-spacer />
        <v-btn icon="mdi-close" @click="closeDialog" />
      </v-toolbar>

      <v-card-text>
        <div v-if="selectedKBDetails">
          <!-- Mode Indicator -->
          <v-alert
            v-if="isInEditMode"
            type="info"
            variant="tonal"
            density="compact"
            class="mb-4"
          >
            <div class="d-flex align-center">
              <v-icon icon="mdi-pencil" class="me-2" />
              <div>
                <div class="font-weight-medium">Modo Edición</div>
                <div class="text-caption">
                  Puedes agregar o eliminar fuentes de datos
                </div>
              </div>
            </div>
          </v-alert>

          <!-- Navigation Indicator -->
          <v-chip
            v-if="totalNavigationItems > 1"
            size="small"
            variant="outlined"
            class="mb-4"
          >
            <span v-if="currentNavigationIndex === 0">
              Crear nuevo
            </span>
            <span v-else>
              {{ currentKnowledgeBase?.name }} ({{ currentNavigationIndex }} de {{ totalNavigationItems - 1 }})
            </span>
          </v-chip>

          <p class="mb-4">
            {{ selectedKBDetails.description }}
          </p>

          <!-- Knowledge Base Name -->
          <div v-if="isInEditMode" class="mb-4">
            <v-label class="text-subtitle-2 text-medium-emphasis mb-2">
              Nombre del conjunto de datos
            </v-label>
            <v-card variant="outlined" class="pa-3">
              <div class="text-body-1 font-weight-medium">
                {{ formData.name }}
              </div>
            </v-card>
          </div>
          <v-text-field
            v-else
            v-model="formData.name"
            label="Nombre del conjunto de datos *"
            :rules="nameRules"
            required
            class="mb-4"
          />

          <!-- Custom Trigger (only for custom type) -->
          <v-text-field
            v-if="selectedKBType === 'custom'"
            v-model="formData.customTrigger"
            label="¿Cuándo debo consultar estos datos?"
            placeholder="Ej: Cuando el cliente pregunte por nuestro plan de financiación"
            class="mb-4"
          />

          <!-- Existing Sources (Edit Mode Only) -->
          <v-card v-if="isInEditMode && currentKnowledgeBase?.sources" variant="outlined" class="mb-4">
            <v-card-title class="text-subtitle-1">
              Fuentes Existentes
            </v-card-title>
            <v-card-text>
              <div v-if="!currentKnowledgeBase.sources || currentKnowledgeBase.sources.length === 0"
                   class="text-body-2 text-medium-emphasis">
                No hay fuentes existentes.
              </div>
              <div v-else class="space-y-3">
                <v-card
                  v-for="(source, index) in currentKnowledgeBase.sources"
                  :key="source.sourceId || index"
                  variant="outlined"
                  class="mb-3"
                >
                  <v-card-text class="py-3">
                    <div class="d-flex align-start">
                      <v-icon
                        :icon="source.type === 'text' ? 'mdi-text' : source.type === 'url' ? 'mdi-link' : 'mdi-file'"
                        size="small"
                        class="me-3 mt-1"
                        :color="source.type === 'text' ? 'blue' : source.type === 'url' ? 'green' : 'orange'"
                      />

                      <div class="flex-grow-1">
                        <!-- Text Source Preview -->
                        <div v-if="source.type === 'text'">
                          <div class="text-subtitle-2 font-weight-medium mb-1">
                            {{ source.name || 'Texto sin título' }}
                          </div>
                          <v-expansion-panels variant="accordion" class="mb-2">
                            <v-expansion-panel>
                              <v-expansion-panel-title class="text-caption pa-2">
                                {{ source.text?.substring(0, 100) }}{{ source.text && source.text.length > 100 ? '...' : '' }}
                              </v-expansion-panel-title>
                              <v-expansion-panel-text class="pa-3">
                                <div class="text-body-2" style="white-space: pre-wrap;">
                                  {{ source.text }}
                                </div>
                              </v-expansion-panel-text>
                            </v-expansion-panel>
                          </v-expansion-panels>
                        </div>

                        <!-- URL Source Preview -->
                        <div v-else-if="source.type === 'url'">
                          <div class="text-subtitle-2 font-weight-medium mb-1">
                            Enlace Web
                          </div>
                          <a
                            :href="source.url"
                            target="_blank"
                            rel="noopener noreferrer"
                            class="text-primary text-decoration-none text-body-2"
                          >
                            <v-icon icon="mdi-open-in-new" size="x-small" class="me-1" />
                            {{ source.url }}
                          </a>
                        </div>

                        <!-- Document Source Preview -->
                        <div v-else-if="source.type === 'document'">
                          <div class="text-subtitle-2 font-weight-medium mb-1">
                            {{ source.name || 'Documento' }}
                          </div>
                          <a
                            :href="source.url"
                            target="_blank"
                            rel="noopener noreferrer"
                            class="text-primary text-decoration-none text-body-2"
                          >
                            <v-icon icon="mdi-download" size="x-small" class="me-1" />
                            Abrir documento
                          </a>
                        </div>
                      </div>

                      <v-btn
                        icon="mdi-delete"
                        size="small"
                        variant="text"
                        color="error"
                        :loading="isDeletingSource"
                        @click="deleteSource(source.sourceId || '')"
                      />
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
          </v-card>

          <!-- Text Inputs Section -->
          <v-card variant="outlined" class="mb-4">
            <v-card-title class="text-subtitle-1">
              {{ isInEditMode ? 'Agregar Textos' : 'Textos' }}
              <v-spacer />
              <v-btn
                icon="mdi-plus"
                size="small"
                variant="text"
                @click="addTextInput"
              />
            </v-card-title>
            <v-card-text>
              <div v-if="formData.texts.length === 0" class="text-body-2 text-medium-emphasis">
                No hay textos agregados. Haz clic en + para agregar.
              </div>
              <div
                v-for="(text, index) in formData.texts"
                :key="index"
                class="mb-3"
              >
                <v-row>
                  <v-col cols="12" md="4">
                    <v-text-field
                      v-model="text.title"
                      label="Título (opcional)"
                      density="compact"
                    />
                  </v-col>
                  <v-col cols="12" md="7">
                    <v-textarea
                      v-model="text.text"
                      label="Contenido del texto"
                      rows="2"
                      density="compact"
                      required
                    />
                  </v-col>
                  <v-col cols="12" md="1" class="d-flex align-center">
                    <v-btn
                      icon="mdi-delete"
                      size="small"
                      variant="text"
                      color="error"
                      @click="removeTextInput(index)"
                    />
                  </v-col>
                </v-row>
              </div>
            </v-card-text>
          </v-card>

          <!-- URLs Section -->
          <v-card variant="outlined" class="mb-4">
            <v-card-title class="text-subtitle-1">
              {{ isInEditMode ? 'Agregar URLs' : 'URLs' }}
              <v-spacer />
              <v-btn
                icon="mdi-plus"
                size="small"
                variant="text"
                @click="addUrl"
              />
            </v-card-title>
            <v-card-text>
              <div v-if="formData.urls.length === 0" class="text-body-2 text-medium-emphasis">
                No hay URLs agregadas. Haz clic en + para agregar.
              </div>
              <div
                v-for="(_, index) in formData.urls"
                :key="index"
                class="mb-3"
              >
                <v-row>
                  <v-col cols="11">
                    <v-text-field
                      v-model="formData.urls[index]"
                      label="URL"
                      placeholder="https://ejemplo.com"
                      density="compact"
                      required
                    />
                  </v-col>
                  <v-col cols="1" class="d-flex align-center">
                    <v-btn
                      icon="mdi-delete"
                      size="small"
                      variant="text"
                      color="error"
                      @click="removeUrl(index)"
                    />
                  </v-col>
                </v-row>
              </div>
            </v-card-text>
          </v-card>

          <!-- Files Section -->
          <v-card variant="outlined" class="mb-4">
            <v-card-title class="text-subtitle-1">
              {{ isInEditMode ? 'Agregar Archivos' : 'Archivos' }}
            </v-card-title>
            <v-card-text>
              <v-file-input
                v-model="formData.files"
                label="Seleccionar archivos"
                multiple
                accept=".pdf,.txt,.docx,.doc"
                prepend-icon="mdi-file-document"
                show-size
                @change="onFileChange"
              />
              <v-chip-group v-if="formData.files.length > 0" class="mt-2">
                <v-chip
                  v-for="(file, index) in formData.files"
                  :key="index"
                  closable
                  @click:close="formData.files.splice(index, 1)"
                >
                  {{ file.name }}
                </v-chip>
              </v-chip-group>
            </v-card-text>
          </v-card>
        </div>
      </v-card-text>

      <v-divider />

      <v-card-actions class="justify-end">
        <v-btn text color="grey" @click="closeDialog">
          Cancelar
        </v-btn>
        <v-btn
          color="primary"
          :loading="isCreating || isAddingSources"
          @click="saveKB"
        >
          {{ actionButtonText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid var(--v-theme-borderColor);
}

.avatar-clickable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.avatar-clickable:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}
</style>
