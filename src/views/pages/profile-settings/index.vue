<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore, useProfileStore } from '@/store'
import { Icon } from '@iconify/vue'

const authStore = useAuthStore()
const profileStore = useProfileStore()

const tab = ref(0)
const isLoading = ref(false)
const showPasswordDialog = ref(false)

// Profile form data
const profileForm = ref({
  displayName: '',
  email: '',
  phone: '',
  avatar: null as File | null
})

// Company form data
const companyForm = ref({
  name: '',
  industry: '',
  size: '',
  website: '',
  logo: null as File | null
})

// Preferences form data
const preferencesForm = ref({
  language: 'en',
  timezone: 'UTC',
  emailNotifications: true,
  smsNotifications: false,
  marketingEmails: true,
  theme: 'light'
})

// Password change form
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const industryOptions = [
  'Real Estate',
  'Healthcare',
  'Hospitality',
  'Fitness',
  'Education',
  'Finance',
  'Technology',
  'Retail',
  'Other'
]

const companySizeOptions = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '500+ employees'
]

const languageOptions = [
  { value: 'en', text: 'English' },
  { value: 'es', text: 'Spanish' },
  { value: 'fr', text: 'French' },
  { value: 'de', text: 'German' }
]

const timezoneOptions = [
  'UTC',
  'America/New_York',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Madrid',
  'Asia/Tokyo'
]

const themeOptions = [
  { value: 'light', text: 'Light' },
  { value: 'dark', text: 'Dark' },
  { value: 'auto', text: 'Auto' }
]

const user = computed(() => authStore.user)

async function saveProfile() {
  isLoading.value = true
  try {
    // This would call the backend API to update profile
    console.log('Saving profile:', profileForm.value)
    
    // Mock success
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message
    console.log('Profile saved successfully')
  } catch (error) {
    console.error('Failed to save profile:', error)
  } finally {
    isLoading.value = false
  }
}

async function saveCompany() {
  isLoading.value = true
  try {
    // Update company name in profile store
    if (companyForm.value.name !== profileStore.companyName) {
      await profileStore.updateCompanyName(companyForm.value.name)
    }
    
    // This would call the backend API to update company info
    console.log('Saving company:', companyForm.value)
    
    // Mock success
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message
    console.log('Company info saved successfully')
  } catch (error) {
    console.error('Failed to save company info:', error)
  } finally {
    isLoading.value = false
  }
}

async function savePreferences() {
  isLoading.value = true
  try {
    // This would call the backend API to update preferences
    console.log('Saving preferences:', preferencesForm.value)
    
    // Mock success
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message
    console.log('Preferences saved successfully')
  } catch (error) {
    console.error('Failed to save preferences:', error)
  } finally {
    isLoading.value = false
  }
}

async function changePassword() {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    console.error('Passwords do not match')
    return
  }
  
  isLoading.value = true
  try {
    // This would call Firebase Auth to update password
    console.log('Changing password')
    
    // Mock success
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Reset form
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    
    showPasswordDialog.value = false
    
    // Show success message
    console.log('Password changed successfully')
  } catch (error) {
    console.error('Failed to change password:', error)
  } finally {
    isLoading.value = false
  }
}

function handleAvatarUpload(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    profileForm.value.avatar = target.files[0]
  }
}

function handleLogoUpload(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    companyForm.value.logo = target.files[0]
  }
}

async function signOut() {
  try {
    await authStore.logout()
  } catch (error) {
    console.error('Failed to sign out:', error)
  }
}

onMounted(async () => {
  // Initialize form data with current user data
  if (user.value) {
    profileForm.value.displayName = user.value.displayName || ''
    profileForm.value.email = user.value.email || ''
  }
  
  // Load company name
  await profileStore.fetchCompanyName()
  if (profileStore.companyName) {
    companyForm.value.name = profileStore.companyName
  }
})
</script>

<template>
  <v-container fluid class="pa-8">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-h4 font-weight-bold mb-2">Profile & Settings</h1>
      <p class="text-body-1 text-medium-emphasis">
        Manage your account settings and preferences
      </p>
    </div>

    <!-- Tabs -->
    <v-card elevation="2">
      <v-tabs v-model="tab" color="primary" align-tabs="title">
        <v-tab>
          <Icon icon="mdi-account" class="mr-2" />
          Profile
        </v-tab>
        <v-tab>
          <Icon icon="mdi-office-building" class="mr-2" />
          Company
        </v-tab>
        <v-tab>
          <Icon icon="mdi-cog" class="mr-2" />
          Preferences
        </v-tab>
      </v-tabs>

      <v-window v-model="tab">
        <!-- Profile Tab -->
        <v-window-item>
          <v-card-text class="pa-6">
            <v-form @submit.prevent="saveProfile">
              <v-row>
                <!-- Avatar Section -->
                <v-col cols="12" class="text-center mb-6">
                  <v-avatar size="120" color="primary" class="mb-4">
                    <span v-if="!profileForm.avatar" class="text-h3">
                      {{ (profileForm.displayName || user?.email || 'U')[0].toUpperCase() }}
                    </span>
                    <img v-else :src="URL.createObjectURL(profileForm.avatar)" alt="Avatar" />
                  </v-avatar>
                  <div>
                    <v-btn variant="outlined" size="small" @click="$refs.avatarInput.click()">
                      <Icon icon="mdi-camera" class="mr-2" />
                      Change Avatar
                    </v-btn>
                    <input
                      ref="avatarInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleAvatarUpload"
                    />
                  </div>
                </v-col>

                <!-- Profile Fields -->
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.displayName"
                    label="Display Name"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.email"
                    label="Email"
                    type="email"
                    variant="outlined"
                    class="mb-4"
                    readonly
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileForm.phone"
                    label="Phone Number"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-btn
                    variant="outlined"
                    class="mb-4"
                    @click="showPasswordDialog = true"
                  >
                    <Icon icon="mdi-lock" class="mr-2" />
                    Change Password
                  </v-btn>
                </v-col>
              </v-row>

              <v-divider class="my-6" />

              <div class="d-flex justify-space-between">
                <v-btn
                  color="error"
                  variant="outlined"
                  @click="signOut"
                >
                  <Icon icon="mdi-logout" class="mr-2" />
                  Sign Out
                </v-btn>
                
                <v-btn
                  color="primary"
                  variant="elevated"
                  type="submit"
                  :loading="isLoading"
                >
                  Save Profile
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-window-item>

        <!-- Company Tab -->
        <v-window-item>
          <v-card-text class="pa-6">
            <v-form @submit.prevent="saveCompany">
              <v-row>
                <!-- Company Logo -->
                <v-col cols="12" class="text-center mb-6">
                  <div class="mb-4">
                    <v-avatar size="120" color="grey-lighten-3" class="mb-4">
                      <Icon v-if="!companyForm.logo" icon="mdi-office-building" size="48" />
                      <img v-else :src="URL.createObjectURL(companyForm.logo)" alt="Company Logo" />
                    </v-avatar>
                  </div>
                  <v-btn variant="outlined" size="small" @click="$refs.logoInput.click()">
                    <Icon icon="mdi-image" class="mr-2" />
                    Upload Logo
                  </v-btn>
                  <input
                    ref="logoInput"
                    type="file"
                    accept="image/*"
                    style="display: none"
                    @change="handleLogoUpload"
                  />
                </v-col>

                <!-- Company Fields -->
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="companyForm.name"
                    label="Company Name"
                    variant="outlined"
                    class="mb-4"
                    required
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="companyForm.industry"
                    :items="industryOptions"
                    label="Industry"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="companyForm.size"
                    :items="companySizeOptions"
                    label="Company Size"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="companyForm.website"
                    label="Website"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
              </v-row>

              <v-divider class="my-6" />

              <div class="text-right">
                <v-btn
                  color="primary"
                  variant="elevated"
                  type="submit"
                  :loading="isLoading"
                >
                  Save Company Info
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-window-item>

        <!-- Preferences Tab -->
        <v-window-item>
          <v-card-text class="pa-6">
            <v-form @submit.prevent="savePreferences">
              <v-row>
                <!-- General Preferences -->
                <v-col cols="12">
                  <h3 class="text-h6 font-weight-bold mb-4">General</h3>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="preferencesForm.language"
                    :items="languageOptions"
                    item-title="text"
                    item-value="value"
                    label="Language"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="preferencesForm.timezone"
                    :items="timezoneOptions"
                    label="Timezone"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="preferencesForm.theme"
                    :items="themeOptions"
                    item-title="text"
                    item-value="value"
                    label="Theme"
                    variant="outlined"
                    class="mb-4"
                  />
                </v-col>

                <!-- Notifications -->
                <v-col cols="12">
                  <h3 class="text-h6 font-weight-bold mb-4">Notifications</h3>
                </v-col>
                
                <v-col cols="12">
                  <v-switch
                    v-model="preferencesForm.emailNotifications"
                    label="Email Notifications"
                    color="primary"
                    class="mb-2"
                  />
                  <v-switch
                    v-model="preferencesForm.smsNotifications"
                    label="SMS Notifications"
                    color="primary"
                    class="mb-2"
                  />
                  <v-switch
                    v-model="preferencesForm.marketingEmails"
                    label="Marketing Emails"
                    color="primary"
                    class="mb-2"
                  />
                </v-col>
              </v-row>

              <v-divider class="my-6" />

              <div class="text-right">
                <v-btn
                  color="primary"
                  variant="elevated"
                  type="submit"
                  :loading="isLoading"
                >
                  Save Preferences
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-window-item>
      </v-window>
    </v-card>
  </v-container>

  <!-- Change Password Dialog -->
  <v-dialog v-model="showPasswordDialog" max-width="500">
    <v-card>
      <v-card-title>Change Password</v-card-title>
      
      <v-card-text>
        <v-form @submit.prevent="changePassword">
          <v-text-field
            v-model="passwordForm.currentPassword"
            label="Current Password"
            type="password"
            variant="outlined"
            class="mb-4"
            required
          />
          
          <v-text-field
            v-model="passwordForm.newPassword"
            label="New Password"
            type="password"
            variant="outlined"
            class="mb-4"
            required
          />
          
          <v-text-field
            v-model="passwordForm.confirmPassword"
            label="Confirm New Password"
            type="password"
            variant="outlined"
            required
          />
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn variant="text" @click="showPasswordDialog = false">Cancel</v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          :loading="isLoading"
          :disabled="!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword"
          @click="changePassword"
        >
          Change Password
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
/* Add any custom styles here */
</style>
