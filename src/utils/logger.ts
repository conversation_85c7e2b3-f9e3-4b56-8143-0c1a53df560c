/**
 * Logging utility for debugging service operations
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: Date
  service?: string
}

class Logger {
  private isDevelopment = import.meta.env.DEV
  private logs: LogEntry[] = []
  private maxLogs = 100

  private log(level: LogLevel, message: string, data?: any, service?: string) {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      service
    }

    // Add to internal log storage
    this.logs.push(entry)
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // Console output in development
    if (this.isDevelopment) {
      const prefix = service ? `[${service}]` : ''
      const emoji = this.getEmoji(level)
      
      switch (level) {
        case 'debug':
          console.debug(`${emoji} ${prefix} ${message}`, data || '')
          break
        case 'info':
          console.info(`${emoji} ${prefix} ${message}`, data || '')
          break
        case 'warn':
          console.warn(`${emoji} ${prefix} ${message}`, data || '')
          break
        case 'error':
          console.error(`${emoji} ${prefix} ${message}`, data || '')
          break
      }
    }
  }

  private getEmoji(level: LogLevel): string {
    switch (level) {
      case 'debug': return '🔍'
      case 'info': return 'ℹ️'
      case 'warn': return '⚠️'
      case 'error': return '❌'
      default: return '📝'
    }
  }

  debug(message: string, data?: any, service?: string) {
    this.log('debug', message, data, service)
  }

  info(message: string, data?: any, service?: string) {
    this.log('info', message, data, service)
  }

  warn(message: string, data?: any, service?: string) {
    this.log('warn', message, data, service)
  }

  error(message: string, data?: any, service?: string) {
    this.log('error', message, data, service)
  }

  // Get recent logs for debugging
  getRecentLogs(count = 20): LogEntry[] {
    return this.logs.slice(-count)
  }

  // Clear logs
  clear() {
    this.logs = []
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// Export singleton instance
export const logger = new Logger()

// Convenience functions for service-specific logging
export function logServiceOperation(
  service: string,
  operation: string,
  level: LogLevel = 'info',
  data?: any
) {
  logger.log(level, `${operation}`, data, service)
}

export function logServiceError(
  service: string,
  operation: string,
  error: any
) {
  logger.error(`${operation} failed: ${error?.message || error}`, error, service)
}
