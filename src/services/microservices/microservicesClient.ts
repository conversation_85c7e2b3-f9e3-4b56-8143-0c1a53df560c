/**
 * Microservices Client for Bolna Platform
 *
 * This client provides a unified interface to communicate with the new microservices
 * architecture, replacing Firebase Functions for WhatsApp and HubSpot operations.
 */
import type { ServiceResponse } from '../core/types'
import { ServiceError, ValidationError } from '../core/types'

// Microservices configuration
interface MicroservicesConfig {
  agentServiceUrl: string
  communicationsServiceUrl: string
  contactServiceUrl: string
  analyticsServiceUrl: string
}

// Default configuration - can be overridden via environment variables
const DEFAULT_CONFIG: MicroservicesConfig = {
  agentServiceUrl: import.meta.env.VITE_AGENT_SERVICE_URL || 'http://localhost:8080',
  communicationsServiceUrl: import.meta.env.VITE_COMMUNICATIONS_SERVICE_URL || 'http://localhost:8083',
  contactServiceUrl: import.meta.env.VITE_CONTACT_SERVICE_URL || 'http://localhost:8081',
  analyticsServiceUrl: import.meta.env.VITE_ANALYTICS_SERVICE_URL || 'http://localhost:8082'
}

// Request/Response types
interface ContactResponse {
  contact_id?: string
  phone_number: string
  email?: string
  first_name?: string
  last_name?: string
  company?: string
  hubspot_id?: string
  lead_status?: string
  user_id?: string
  created_at?: string
  last_modified?: string
  source?: string
  properties?: Record<string, any>
}

interface LeadStatusUpdateRequest {
  contact_id: string
  user_id: string
  lead_status: string
  reason?: string
  metadata?: Record<string, any>
}

interface LeadStatusHistory {
  contact_id: string
  old_status?: string
  new_status: string
  reason?: string
  timestamp: string
  hubspot_id?: string
}

interface AgentActivationRequest {
  phone_number: string
  agent_id: string
  user_id?: string
  initial_message?: string
}

interface MessageRequest {
  to: string
  message: string
  from_number?: string
}

/**
 * Microservices client for the new architecture
 */
export class MicroservicesClient {
  private config: MicroservicesConfig

  constructor(config?: Partial<MicroservicesConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * Make HTTP request to microservice
   */
  private async makeRequest<T>(
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<ServiceResponse<T>> {
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      }

      if (data && method !== 'GET') {
        options.body = JSON.stringify(data)
      }

      const response = await fetch(url, options)
      const responseData = await response.json()

      if (!response.ok) {
        throw new ServiceError(
          responseData.detail || responseData.error || 'Request failed',
          responseData.code || 'REQUEST_FAILED'
        )
      }

      // Handle microservice response format
      if (responseData.success === false) {
        throw new ServiceError(
          responseData.error || 'Service error',
          responseData.code || 'SERVICE_ERROR'
        )
      }

      return {
        data: responseData.data || responseData,
        success: true
      }

    } catch (error: any) {
      console.error(`❌ [MicroservicesClient] Request failed:`, error)

      if (error instanceof ServiceError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError(
          error.message || 'Unknown error occurred',
          'NETWORK_ERROR'
        ),
        success: false
      }
    }
  }

  // Contact Service Methods

  /**
   * Get contact by phone number
   */
  async getContactByPhone(phoneNumber: string, userId: string): Promise<ServiceResponse<ContactResponse>> {
    if (!phoneNumber || !userId) {
      return {
        error: new ValidationError('Phone number and user ID are required'),
        success: false
      }
    }

    const url = `${this.config.contactServiceUrl}/contact/by-phone/${encodeURIComponent(phoneNumber)}?user_id=${userId}`
    return this.makeRequest<ContactResponse>(url)
  }

  /**
   * Get contact by contact ID
   */
  async getContactById(contactId: string, userId: string): Promise<ServiceResponse<ContactResponse>> {
    if (!contactId || !userId) {
      return {
        error: new ValidationError('Contact ID and user ID are required'),
        success: false
      }
    }

    const url = `${this.config.contactServiceUrl}/contact/${encodeURIComponent(contactId)}?user_id=${userId}`
    return this.makeRequest<ContactResponse>(url)
  }

  /**
   * Lookup contact ID by phone number
   */
  async lookupContactIdByPhone(phoneNumber: string, userId: string): Promise<ServiceResponse<any>> {
    if (!phoneNumber || !userId) {
      return {
        error: new ValidationError('Phone number and user ID are required'),
        success: false
      }
    }

    const url = `${this.config.contactServiceUrl}/contact/lookup/${encodeURIComponent(phoneNumber)}?user_id=${userId}`
    return this.makeRequest(url)
  }

  /**
   * Get all contacts with pagination and filtering
   */
  async getAllContacts(userId: string, options: {
    limit?: number
    offset?: number
    leadStatus?: string
    createdAfter?: string
    updatedAfter?: string
  } = {}): Promise<ServiceResponse<{ contacts: ContactResponse[], pagination: any }>> {
    if (!userId) {
      return {
        error: new ValidationError('User ID is required'),
        success: false
      }
    }

    const url = new URL(`${this.config.contactServiceUrl}/contacts`)
    url.searchParams.append('user_id', userId)

    if (options.limit) url.searchParams.append('limit', options.limit.toString())
    if (options.offset) url.searchParams.append('offset', options.offset.toString())
    if (options.leadStatus) url.searchParams.append('lead_status', options.leadStatus)
    if (options.createdAfter) url.searchParams.append('created_after', options.createdAfter)
    if (options.updatedAfter) url.searchParams.append('updated_after', options.updatedAfter)

    return this.makeRequest<{ contacts: ContactResponse[], pagination: any }>(url.toString())
  }

  /**
   * Update contact lead status
   */
  async updateLeadStatus(request: LeadStatusUpdateRequest): Promise<ServiceResponse<any>> {
    if (!request.contact_id || !request.lead_status) {
      return {
        error: new ValidationError('Contact ID and lead status are required'),
        success: false
      }
    }

    const url = `${this.config.contactServiceUrl}/contact/lead-status`
    return this.makeRequest(url, 'POST', request)
  }

  /**
   * Get lead status history for a contact
   */
  async getLeadStatusHistory(phoneNumber: string): Promise<ServiceResponse<LeadStatusHistory[]>> {
    if (!phoneNumber) {
      return {
        error: new ValidationError('Phone number is required'),
        success: false
      }
    }

    const url = `${this.config.contactServiceUrl}/contact/${encodeURIComponent(phoneNumber)}/lead-status-history`
    return this.makeRequest<LeadStatusHistory[]>(url)
  }

  // Communications Service Methods

  /**
   * Get active agent for a phone number
   */
  async getActiveAgent(phoneNumber: string): Promise<ServiceResponse<{ agent_id: string | null }>> {
    if (!phoneNumber) {
      return {
        error: new ValidationError('Phone number is required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/agent/active/${encodeURIComponent(phoneNumber)}`
    return this.makeRequest<{ agent_id: string | null }>(url)
  }

  /**
   * Activate agent for a contact
   */
  async activateAgent(request: AgentActivationRequest): Promise<ServiceResponse<any>> {
    if (!request.phone_number || !request.agent_id) {
      return {
        error: new ValidationError('Phone number and agent ID are required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/agent/activate`
    return this.makeRequest(url, 'POST', request)
  }

  /**
   * Deactivate agent for a contact
   */
  async deactivateAgent(phoneNumber: string): Promise<ServiceResponse<any>> {
    if (!phoneNumber) {
      return {
        error: new ValidationError('Phone number is required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/agent/deactivate/${encodeURIComponent(phoneNumber)}`
    return this.makeRequest(url, 'DELETE')
  }

  /**
   * Send message via communications service
   */
  async sendMessage(request: MessageRequest): Promise<ServiceResponse<any>> {
    if (!request.to || !request.message) {
      return {
        error: new ValidationError('Recipient and message are required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/message/send`
    return this.makeRequest(url, 'POST', request)
  }

  /**
   * Get conversation history for a specific agent and phone number
   */
  async getConversationByAgentAndPhone(agentId: string, phoneNumber: string, limit: number = 50): Promise<ServiceResponse<any>> {
    if (!agentId || !phoneNumber) {
      return {
        error: new ValidationError('Agent ID and phone number are required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/conversation/${encodeURIComponent(agentId)}/${encodeURIComponent(phoneNumber)}?limit=${limit}`
    return this.makeRequest(url)
  }

  /**
   * Get all conversations for a specific agent
   */
  async getConversationsByAgent(agentId: string, limit: number = 50): Promise<ServiceResponse<any>> {
    if (!agentId) {
      return {
        error: new ValidationError('Agent ID is required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/conversation/${encodeURIComponent(agentId)}/?limit=${limit}`
    return this.makeRequest(url)
  }

  /**
   * Get conversation history for a phone number (DEPRECATED - use getConversationByAgentAndPhone)
   */
  async getConversation(phoneNumber: string, limit: number = 50): Promise<ServiceResponse<any>> {
    if (!phoneNumber) {
      return {
        error: new ValidationError('Phone number is required'),
        success: false
      }
    }

    const url = `${this.config.communicationsServiceUrl}/conversation/${encodeURIComponent(phoneNumber)}?limit=${limit}`
    return this.makeRequest(url)
  }

  // Analytics Service Methods

  /**
   * Analyze conversation
   */
  async analyzeConversation(contactId: string, messages: any[], agentId?: string): Promise<ServiceResponse<any>> {
    if (!contactId || !messages || messages.length === 0) {
      return {
        error: new ValidationError('Contact ID and messages are required'),
        success: false
      }
    }

    const url = `${this.config.analyticsServiceUrl}/analyze`
    return this.makeRequest(url, 'POST', {
      contact_id: contactId,
      messages,
      agent_id: agentId
    })
  }

  /**
   * Get analysis history for a contact
   */
  async getAnalysisHistory(contactId: string, limit: number = 10): Promise<ServiceResponse<any[]>> {
    if (!contactId) {
      return {
        error: new ValidationError('Contact ID is required'),
        success: false
      }
    }

    const url = `${this.config.analyticsServiceUrl}/analysis/${encodeURIComponent(contactId)}?limit=${limit}`
    return this.makeRequest<any[]>(url)
  }

  // Health check methods

  /**
   * Check health of all microservices
   */
  async checkHealth(): Promise<Record<string, boolean>> {
    const services = [
      { name: 'agent', url: `${this.config.agentServiceUrl}/health` },
      { name: 'communications', url: `${this.config.communicationsServiceUrl}/health` },
      { name: 'contact', url: `${this.config.contactServiceUrl}/health` },
      { name: 'analytics', url: `${this.config.analyticsServiceUrl}/health` }
    ]

    const healthStatus: Record<string, boolean> = {}

    await Promise.all(
      services.map(async (service) => {
        try {
          const response = await fetch(service.url)
          healthStatus[service.name] = response.ok
        } catch (error) {
          healthStatus[service.name] = false
        }
      })
    )

    return healthStatus
  }
}

// Export singleton instance
export const microservicesClient = new MicroservicesClient()
