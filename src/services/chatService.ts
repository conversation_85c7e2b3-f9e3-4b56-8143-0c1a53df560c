import { backendConfig } from '@/config/backend'

export interface ChatMessage {
  id: string
  text: string
  sender: 'user' | 'assistant'
  timestamp: Date
}

export interface ChatConnection {
  connect(assistantId: string): Promise<WebSocket>
  disconnect(): void
  sendMessage(message: string): void
  onMessage(callback: (message: string) => void): void
  onConnect(callback: () => void): void
  onDisconnect(callback: () => void): void
  onError(callback: (error: Error) => void): void
}

export class BolnaChatService implements ChatConnection {
  private websocket: WebSocket | null = null
  private messageCallback?: (message: string) => void
  private connectCallback?: () => void
  private disconnectCallback?: () => void
  private errorCallback?: (error: Error) => void

  async connect(assistantId: string): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      try {
        // Get WebSocket URL from backend config
        const baseUrl = backendConfig.microservices.agentService || 'ws://localhost:8080'
        const wsUrl = baseUrl.replace('http', 'ws') + `/chat/v1/${assistantId}`

        this.websocket = new WebSocket(wsUrl)

        this.websocket.onopen = () => {
          this.connectCallback?.()
          resolve(this.websocket!)
        }

        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            if (data.message) {
              this.messageCallback?.(data.message)
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
            this.messageCallback?.(event.data)
          }
        }

        this.websocket.onclose = () => {
          this.disconnectCallback?.()
        }

        this.websocket.onerror = (error) => {
          const errorObj = new Error('WebSocket connection error')
          this.errorCallback?.(errorObj)
          reject(errorObj)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  disconnect(): void {
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
  }

  sendMessage(message: string): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({ message }))
    } else {
      throw new Error('WebSocket is not connected')
    }
  }

  onMessage(callback: (message: string) => void): void {
    this.messageCallback = callback
  }

  onConnect(callback: () => void): void {
    this.connectCallback = callback
  }

  onDisconnect(callback: () => void): void {
    this.disconnectCallback = callback
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback
  }
}

// Factory function to get the appropriate chat service
export function getChatService(): ChatConnection {
  return new BolnaChatService()
}
