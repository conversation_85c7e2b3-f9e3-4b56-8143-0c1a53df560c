import type { ServiceResponse } from './core/types'
import { ServiceError, NetworkError, ValidationError } from './core/types'
import type { Contact, HubSpotConfig } from '@/store/useContactsStore'
import { microservicesClient } from './microservices/microservicesClient'
import { useAuthStore } from '@/store/useAuthStore'

// Contact Service Configuration
const CONTACT_SERVICE_URL = import.meta.env.VITE_CONTACT_SERVICE_URL || 'http://localhost:8081'

/**
 * HubSpot API response types
 */
export interface HubSpotContact {
  id: string
  properties: {
    firstname?: string
    lastname?: string
    email?: string
    phone?: string
    company?: string
    lifecyclestage?: string
    hs_lead_status?: string
    lastmodifieddate?: string
  }
  createdAt: string
  updatedAt: string
  archived: boolean
}

export interface HubSpotContactsResponse {
  results: HubSpotContact[]
  paging?: {
    next?: {
      after: string
      link: string
    }
  }
}

export interface HubSpotContactsRequest {
  limit?: number
  after?: string
  properties?: string[]
  associations?: string[]
  leadStatus?: string
  createdAfter?: string
  updatedAfter?: string
}

// Lead Status Types
export interface LeadStatusOption {
  key: string
  label: string
}

export interface LeadStatusHistory {
  id: string
  phone_number: string
  old_status?: string
  new_status: string
  old_status_label?: string
  new_status_label?: string
  reason: string
  timestamp: string
  hubspot_contact_id?: string
}

export interface LeadStatusUpdateRequest {
  contact_id: string
  lead_status: string
  hubspot_api_key?: string
  user_id: string
  reason?: string
}

/**
 * HubSpot service for managing contact operations using Firebase callable functions
 */
export class HubSpotService {
  constructor() {
    // Firebase callable functions handle CORS automatically
    // No need for HTTP client or base URL configuration
  }

  /**
   * Fetch contacts from HubSpot CRM via Contact Service
   */
  async fetchContacts(config: HubSpotConfig, options: HubSpotContactsRequest = {}): Promise<ServiceResponse<Contact[]>> {
    try {
      if (!config.apiKey) {
        throw new ValidationError('HubSpot API key is required')
      }

      // Get current user ID from auth store
      const authStore = useAuthStore()
      const userId = authStore.user?.uid

      if (!userId) {
        throw new ValidationError('User not authenticated')
      }

      // Use microservices client to fetch contacts
      const response = await microservicesClient.getAllContacts(userId, {
        limit: options.limit,
        offset: options.after ? parseInt(options.after) : undefined,
        leadStatus: options.leadStatus,
        createdAfter: options.createdAfter,
        updatedAfter: options.updatedAfter
      })

      if (!response.success) {
        throw response.error || new ServiceError('Failed to fetch contacts')
      }

      // Map contact service response to Contact interface
      const contactsData = response.data?.contacts || []
      const contacts: Contact[] = contactsData.map((contactData: any) => ({
        id: contactData.contact_id || contactData.hubspot_id || contactData.phone_number,
        name: `${contactData.first_name || ''} ${contactData.last_name || ''}`.trim() || 'Unknown',
        phone: contactData.phone_number,
        email: contactData.email,
        company: contactData.company,
        source: 'hubspot',
        hubspotId: contactData.hubspot_id,
        leadStatus: contactData.lead_status,
        leadStatusLabel: this.getLeadStatusLabel(contactData.lead_status),
        properties: {
          lifecycle_stage: contactData.lifecycle_stage,
          created_at: contactData.created_at,
          last_modified: contactData.last_modified,
          contact_id: contactData.contact_id
        },
        createdAt: contactData.created_at,
        lastActivity: contactData.last_modified || new Date().toISOString()
      }))

      return {
        data: contacts,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Error fetching contacts:', error)

      return {
        error: error instanceof ServiceError ? error : new ServiceError(error.message || 'Failed to fetch contacts'),
        success: false
      }
    }
  }

  /**
   * Helper method to get lead status label
   */
  private getLeadStatusLabel(status?: string): string {
    const statusLabels: Record<string, string> = {
      'NEW': 'Nuevo',
      'OPEN': 'Abierto',
      'IN_PROGRESS': 'En progreso',
      'OPEN_DEAL': 'Negocio abierto',
      'UNQUALIFIED': 'No calificado',
      'ATTEMPTED_TO_CONTACT': 'Intento de contacto',
      'CONNECTED': 'Conectado',
      'BAD_TIMING': 'Mal momento'
    }

    return statusLabels[status || 'NEW'] || 'Nuevo'
  }

  /**
   * Configure HubSpot integration for a specific user via Contact Service
   */
  async configureHubSpot(config: HubSpotConfig, userId: string): Promise<ServiceResponse<any>> {
    try {
      if (!config.apiKey) {
        throw new ValidationError('HubSpot API key is required')
      }

      if (!userId) {
        throw new ValidationError('User ID is required')
      }

      const url = `${CONTACT_SERVICE_URL}/hubspot/configure`
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          api_key: config.apiKey,
          enabled: config.enabled ?? true,
          sync_frequency: 300,
          properties: {
            portal_id: config.portalId,
            lead_pipeline: config.leadPipeline,
            deal_pipeline: config.dealPipeline,
            contact_owner: config.contactOwner,
            auto_create_contacts: config.autoCreateContacts,
            auto_create_deals: config.autoCreateDeals,
            sync_activities: config.syncActivities
          }
        })
      })

      const responseData = await response.json()

      if (!response.ok) {
        throw new ServiceError(
          responseData.detail || responseData.error || 'Failed to configure HubSpot for user',
          'HUBSPOT_USER_CONFIG_ERROR'
        )
      }

      return {
        data: responseData,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Error configuring HubSpot for user:', error)

      return {
        error: error instanceof ServiceError ? error : new ServiceError(error.message || 'Failed to configure HubSpot for user'),
        success: false
      }
    }
  }

  /**
   * Test HubSpot API connection using Contact Service
   */
  async testConnection(config: HubSpotConfig, userId?: string): Promise<ServiceResponse<boolean>> {
    try {
      if (!config.apiKey) {
        throw new ValidationError('HubSpot API key is required')
      }

      // Get user ID if not provided
      if (!userId) {
        const authStore = useAuthStore()
        userId = authStore.user?.uid
        if (!userId) {
          throw new ValidationError('User not authenticated')
        }
      }

      // Configure HubSpot via user-specific Contact Service endpoint
      const configResponse = await this.configureHubSpot(config, userId)

      if (!configResponse.success) {
        throw configResponse.error || new ServiceError('Failed to configure HubSpot')
      }

      return {
        data: true,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Connection test failed:', error)

      let serviceError: ServiceError

      if (error instanceof ServiceError) {
        serviceError = error
      } else if (error.code === 'functions/unauthenticated') {
        serviceError = new ValidationError('Authentication required for HubSpot integration')
      } else if (error.message?.includes('UNAUTHORIZED')) {
        serviceError = new ValidationError('Invalid HubSpot API key')
      } else if (error.message?.includes('FORBIDDEN')) {
        serviceError = new ValidationError('Insufficient permissions for HubSpot API')
      } else {
        serviceError = new NetworkError('Failed to connect to HubSpot API', error)
      }

      return {
        error: serviceError,
        success: false
      }
    }
  }

  /**
   * Get contact by phone number via Contact Service
   */
  async getContactByPhone(phoneNumber: string): Promise<ServiceResponse<Contact | null>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      // Get current user ID from auth store
      const authStore = useAuthStore()
      const userId = authStore.user?.uid

      if (!userId) {
        throw new ValidationError('User not authenticated')
      }

      const response = await microservicesClient.getContactByPhone(phoneNumber, userId)

      if (!response.success) {
        if (response.error?.code === 'CONTACT_NOT_FOUND') {
          return {
            data: null,
            success: true
          }
        }
        throw response.error || new ServiceError('Failed to get contact')
      }

      // Map contact service response to Contact interface
      const contactData = response.data
      if (!contactData) {
        return {
          data: null,
          success: true
        }
      }

      const contact: Contact = {
        id: contactData.contact_id || contactData.hubspot_id || contactData.phone_number,
        name: `${contactData.first_name || ''} ${contactData.last_name || ''}`.trim() || 'Unknown',
        phone: contactData.phone_number,
        email: contactData.email,
        company: contactData.company,
        source: 'hubspot',
        hubspotId: contactData.hubspot_id,
        leadStatus: contactData.lead_status,
        properties: contactData.properties,
        lastActivity: new Date().toISOString() // TODO: Get actual last activity from contact service
      }

      return {
        data: contact,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Error getting contact by phone:', error)

      return {
        error: error instanceof ServiceError ? error : new ServiceError(error.message || 'Failed to get contact'),
        success: false
      }
    }
  }

  /**
   * Get available lead status options
   */
  async getLeadStatusOptions(): Promise<ServiceResponse<LeadStatusOption[]>> {
    try {
      // Return valid HubSpot API values with labels
      const options: LeadStatusOption[] = [
        { key: 'NEW', label: 'Nuevo' },
        { key: 'OPEN', label: 'Abierto' },
        { key: 'IN_PROGRESS', label: 'En progreso' },
        { key: 'OPEN_DEAL', label: 'Negocio abierto' },
        { key: 'UNQUALIFIED', label: 'No calificado' },
        { key: 'ATTEMPTED_TO_CONTACT', label: 'Intento de contacto' },
        { key: 'CONNECTED', label: 'Conectado' },
        { key: 'BAD_TIMING', label: 'Mal momento' }
      ]

      return {
        data: options,
        success: true
      }
    } catch (error: any) {
      console.error('❌ [HubSpotService] Error getting lead status options:', error)
      return {
        error: new ServiceError('Failed to get lead status options'),
        success: false
      }
    }
  }

  /**
   * Update lead status for a contact via Contact Service
   */
  async updateLeadStatus(request: LeadStatusUpdateRequest): Promise<ServiceResponse<any>> {
    try {
      if (!request.contact_id || !request.lead_status) {
        throw new ValidationError('Contact ID and lead status are required')
      }

      if (!request.user_id) {
        throw new ValidationError('User ID is required for lead status updates')
      }

      // Use Contact Service microservice for lead status updates
      const response = await microservicesClient.updateLeadStatus({
        contact_id: request.contact_id,
        user_id: request.user_id,
        lead_status: request.lead_status,
        reason: request.reason || 'Manual update from dashboard'
      })

      if (!response.success) {
        throw response.error || new ServiceError('Failed to update lead status', 'LEAD_STATUS_UPDATE_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Error updating lead status:', error)

      let serviceError: ServiceError
      if (error instanceof ServiceError || error instanceof ValidationError) {
        serviceError = error
      } else {
        serviceError = new ServiceError('Failed to update lead status', 'LEAD_STATUS_UPDATE_ERROR')
      }

      return {
        error: serviceError,
        success: false
      }
    }
  }

  /**
   * Get lead status history for a contact via Firebase Functions
   */
  async getLeadStatusHistory(phoneNumber: string): Promise<ServiceResponse<LeadStatusHistory[]>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      // Use Contact Service microservice for lead status history
      const response = await microservicesClient.getLeadStatusHistory(phoneNumber)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get lead status history', 'LEAD_STATUS_HISTORY_ERROR')
      }

      return {
        data: response.data || [],
        success: true
      }

    } catch (error: any) {
      console.error('❌ [HubSpotService] Error getting lead status history:', error)

      let serviceError: ServiceError
      if (error instanceof ServiceError || error instanceof ValidationError) {
        serviceError = error
      } else {
        serviceError = new ServiceError('Failed to get lead status history', 'LEAD_STATUS_HISTORY_ERROR')
      }

      return {
        error: serviceError,
        success: false
      }
    }
  }
}

/**
 * Factory function to create HubSpot service instance
 */
export function createHubSpotService(): HubSpotService {
  return new HubSpotService()
}

/**
 * Create HubSpot service for development
 */
export function createHubSpotServiceForDev(): HubSpotService {
  // Firebase callable functions handle CORS automatically
  // No additional configuration needed
  return new HubSpotService()
}

/**
 * Default HubSpot service instance
 */
export const hubspotService = createHubSpotService()
