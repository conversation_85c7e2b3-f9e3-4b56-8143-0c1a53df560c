import type { Assistant } from '@/types/domain'

/**
 * Abstract interface for assistant service operations
 * This interface defines the contract that both Firebase and Bolna adapters must implement
 */
export interface IAssistantService {
  createAssistant(userId: string, assistantType: string, companyName: string): Promise<Assistant | undefined>
  updateAssistant(assistantId: string, assistantData: Assistant): Promise<boolean>
  deleteAssistant(assistantId: string): Promise<boolean>
  getAssistantDetails(assistantId: string): Promise<Assistant | undefined>
  fetchAssistants(userId: string): Promise<Assistant[] | undefined>
}

/**
 * Common error types for service operations
 */
export class ServiceError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message)
    this.name = 'ServiceError'
  }
}

export class NetworkError extends ServiceError {
  constructor(message: string, originalError?: any) {
    super(message, 'NETWORK_ERROR', undefined, originalError)
    this.name = 'NetworkError'
  }
}

export class ValidationError extends ServiceError {
  constructor(message: string, originalError?: any) {
    super(message, 'VALIDATION_ERROR', 400, originalError)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends ServiceError {
  constructor(message: string, originalError?: any) {
    super(message, 'NOT_FOUND', 404, originalError)
    this.name = 'NotFoundError'
  }
}

/**
 * HTTP client interface for making requests
 */
export interface HttpClient {
  get<T>(url: string, config?: RequestConfig): Promise<T>
  post<T>(url: string, data?: any, config?: RequestConfig): Promise<T>
  put<T>(url: string, data?: any, config?: RequestConfig): Promise<T>
  delete<T>(url: string, config?: RequestConfig): Promise<T>
}

export interface RequestConfig {
  headers?: Record<string, string>
  timeout?: number
  retries?: number
}

/**
 * Response wrapper for consistent error handling
 */
export interface ServiceResponse<T> {
  data?: T
  error?: ServiceError
  success: boolean
}
