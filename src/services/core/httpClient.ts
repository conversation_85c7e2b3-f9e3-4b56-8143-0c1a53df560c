import type { HttpClient, RequestConfig } from './types'
import { NetworkError, ValidationError } from './types'

/**
 * HTTP client implementation for making requests to REST APIs
 */
export class RestHttpClient implements HttpClient {
  private baseUrl: string
  private defaultTimeout: number
  private defaultRetries: number

  constructor(baseUrl: string, timeout = 30000, retries = 3) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // Remove trailing slash
    this.defaultTimeout = timeout
    this.defaultRetries = retries
  }

  async get<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>('GET', url, undefined, config)
  }

  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>('POST', url, data, config)
  }

  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>('PUT', url, data, config)
  }

  async delete<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>('DELETE', url, undefined, config)
  }

  private async request<T>(
    method: string,
    url: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`
    const timeout = config?.timeout || this.defaultTimeout
    const retries = config?.retries || this.defaultRetries

    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers
      },
      signal: AbortSignal.timeout(timeout)
    }

    if (data && (method === 'POST' || method === 'PUT')) {
      requestOptions.body = JSON.stringify(data)
    }

    return this.executeWithRetry(fullUrl, requestOptions, retries)
  }

  private async executeWithRetry<T>(
    url: string,
    options: RequestInit,
    retries: number
  ): Promise<T> {
    let lastError: Error | undefined

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, options)

        if (!response.ok) {
          throw new ValidationError(
            `HTTP ${response.status}: ${response.statusText}`,
            { status: response.status, statusText: response.statusText }
          )
        }

        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          return await response.json()
        } else {
          return await response.text() as T
        }
      } catch (error: any) {
        lastError = error

        // Don't retry on validation errors (4xx)
        if (error instanceof ValidationError) {
          throw error
        }

        // Don't retry on the last attempt
        if (attempt === retries) {
          break
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw new NetworkError(
      `Request failed after ${retries + 1} attempts: ${lastError?.message}`,
      lastError
    )
  }
}

/**
 * Factory function to create HTTP client instances
 */
export function createHttpClient(baseUrl: string, timeout?: number, retries?: number): HttpClient {
  return new RestHttpClient(baseUrl, timeout, retries)
}
