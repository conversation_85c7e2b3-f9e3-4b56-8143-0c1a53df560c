import type { HttpsCallable } from 'firebase/functions'

export async function safeCallable<T = any>(
  callable: HttpsCallable<any, any>,
  payload: any,
  validator?: (data: any) => boolean,
  errorMsg = 'Invalid response'
): Promise<T | undefined> {
  try {
    const res = await callable(payload)
    if (!res || typeof res.data !== 'object') throw new Error(errorMsg)
    if (validator && !validator(res.data)) throw new Error(errorMsg)
    return res.data as T
  } catch (error: any) {
    console.error(`❌ [${callable.name}] Error:`, error)
  }
}
