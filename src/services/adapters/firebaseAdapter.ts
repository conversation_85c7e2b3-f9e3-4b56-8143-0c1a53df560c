import type { IAssistantService } from '../core/types'
import type { Assistant } from '@/types/domain'
import { ServiceError } from '../core/types'
import { functions } from '@/plugins/firebase'
import { httpsCallable } from 'firebase/functions'
import { mapApiResponseToAssistant, mapAssistantToApiRequest } from '../mappers/assistantMapper'
import { safeCallable } from '../core/firebaseCall'

/**
 * Firebase Functions adapter implementation
 * This wraps the existing Firebase functions calls in the IAssistantService interface
 */
export class FirebaseAssistantAdapter implements IAssistantService {
  private createAssistantCallable = httpsCallable(functions, 'create_assistant_http')
  private updateAssistantCallable = httpsCallable(functions, 'update_assistant_http')
  private deleteAssistantCallable = httpsCallable(functions, 'delete_assistant_http')
  private getAssistantDetailsCallable = httpsCallable(functions, 'get_assistant_details_http')
  private fetchAssistantsCallable = httpsCallable(functions, 'fetch_assistants_http')

  async createAssistant(userId: string, assistantType: string, companyName: string): Promise<Assistant | undefined> {
    try {
      const data = await safeCallable(this.createAssistantCallable, {
        user_id: userId,
        assistant_type: assistantType,
        company_name: companyName
      })
      return data && mapApiResponseToAssistant(data)
    } catch (error: any) {
      console.error('❌ [FirebaseAdapter] Create assistant error:', error)
      throw new ServiceError('Failed to create assistant', 'CREATE_FAILED', undefined, error)
    }
  }

  async updateAssistant(assistantId: string, assistantData: Assistant): Promise<boolean> {
    try {
      const data = await safeCallable<{ status: string }>(this.updateAssistantCallable, {
        assistant_id: assistantId,
        assistant_data: mapAssistantToApiRequest(assistantData)
      }, d => 'status' in d)
      return data?.status === 'success'
    } catch (error: any) {
      console.error('❌ [FirebaseAdapter] Update assistant error:', error)
      throw new ServiceError('Failed to update assistant', 'UPDATE_FAILED', undefined, error)
    }
  }

  async deleteAssistant(assistantId: string): Promise<boolean> {
    try {
      const data = await safeCallable<{ status: string }>(
        this.deleteAssistantCallable, 
        { assistant_id: assistantId }, 
        d => 'status' in d
      )
      return data?.status === 'success'
    } catch (error: any) {
      console.error('❌ [FirebaseAdapter] Delete assistant error:', error)
      throw new ServiceError('Failed to delete assistant', 'DELETE_FAILED', undefined, error)
    }
  }

  async getAssistantDetails(assistantId: string): Promise<Assistant | undefined> {
    try {
      const data = await safeCallable(this.getAssistantDetailsCallable, { assistant_id: assistantId })
      return data && mapApiResponseToAssistant(data)
    } catch (error: any) {
      console.error('❌ [FirebaseAdapter] Get assistant details error:', error)
      throw new ServiceError('Failed to get assistant details', 'GET_FAILED', undefined, error)
    }
  }

  async fetchAssistants(userId: string): Promise<Assistant[] | undefined> {
    try {
      const data = await safeCallable(
        this.fetchAssistantsCallable, 
        { user_id: userId }, 
        d => Array.isArray(d.assistants)
      )
      return data?.assistants?.map(mapApiResponseToAssistant)
    } catch (error: any) {
      console.error('❌ [FirebaseAdapter] Fetch assistants error:', error)
      throw new ServiceError('Failed to fetch assistants', 'FETCH_FAILED', undefined, error)
    }
  }
}
