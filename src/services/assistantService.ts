import type { Assistant } from '@/types/domain'
import { getAssistantService } from './serviceFactory'

// Get the appropriate service instance based on configuration
const assistantService = getAssistantService()

export async function createAssistant(userId: string, assistantType: string, companyName: string): Promise<Assistant | undefined> {
  return assistantService.createAssistant(userId, assistantType, companyName)
}

export async function updateAssistant(assistantId: string, assistantData: Assistant): Promise<boolean> {
  return assistantService.updateAssistant(assistantId, assistantData)
}

export async function deleteAssistant(assistantId: string): Promise<boolean> {
  return assistantService.deleteAssistant(assistantId)
}

export async function getAssistantDetails(assistantId: string): Promise<Assistant | undefined> {
  return assistantService.getAssistantDetails(assistantId)
}

export async function fetchAssistants(userId: string): Promise<Assistant[] | undefined> {
  return assistantService.fetchAssistants(userId)
}
