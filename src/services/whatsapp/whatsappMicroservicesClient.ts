/**
 * WhatsApp Microservices Client
 */
import type { ServiceResponse } from '../core/types'
import { ServiceError, ValidationError } from '../core/types'
import { microservicesClient } from '../microservices/microservicesClient'
import { useAuthStore } from '@/store/useAuthStore'

// WhatsApp-specific types
interface WhatsAppMessage {
  id: string
  from: string
  to: string
  body: string
  direction: 'inbound' | 'outbound'
  timestamp: string
  type: 'text' | 'media'
  status?: string
}

interface ActiveAgent {
  phoneNumber: string
  agentId: string
  activatedAt: Date
  status: 'active' | 'inactive'
  metadata?: Record<string, any>
}

interface ConversationAnalysis {
  contactId: string
  leadStatus?: string
  confidence: number
  keyInsights: string[]
  sentiment: string
  intent?: string
  nextActions: string[]
  analysisTimestamp: string
}

/**
 * WhatsApp service using microservices architecture
 */
export class WhatsAppMicroservicesClient {

  /**
   * Get active agent for a phone number
   */
  async getActiveAgent(phoneNumber: string): Promise<ServiceResponse<{ agentId: string | null }>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      const response = await microservicesClient.getActiveAgent(phoneNumber)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get active agent', 'GET_ACTIVE_AGENT_ERROR')
      }

      return {
        data: { agentId: response.data?.agent_id || null },
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting active agent:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get active agent', 'GET_ACTIVE_AGENT_ERROR'),
        success: false
      }
    }
  }

  /**
   * Set active agent for a phone number
   */
  async setActiveAgent(phoneNumber: string, agentId: string, initialMessage?: string): Promise<ServiceResponse<any>> {
    try {
      if (!phoneNumber || !agentId) {
        throw new ValidationError('Phone number and agent ID are required')
      }

      // Get current user ID from auth store
      const authStore = useAuthStore()
      const userId = authStore.user?.uid

      if (!userId) {
        throw new ValidationError('User not authenticated')
      }

      const response = await microservicesClient.activateAgent({
        phone_number: phoneNumber,
        agent_id: agentId,
        user_id: userId,
        initial_message: initialMessage
      })

      if (!response.success) {
        throw response.error || new ServiceError('Failed to activate agent', 'ACTIVATE_AGENT_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error setting active agent:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to set active agent', 'SET_ACTIVE_AGENT_ERROR'),
        success: false
      }
    }
  }

  /**
   * Remove active agent for a phone number
   */
  async removeActiveAgent(phoneNumber: string): Promise<ServiceResponse<{ removedAgentId: string | null }>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      const response = await microservicesClient.deactivateAgent(phoneNumber)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to deactivate agent', 'DEACTIVATE_AGENT_ERROR')
      }

      return {
        data: { removedAgentId: response.data?.agent_id || null },
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error removing active agent:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to remove active agent', 'REMOVE_ACTIVE_AGENT_ERROR'),
        success: false
      }
    }
  }

  /**
   * Send WhatsApp message
   */
  async sendMessage(to: string, message: string, fromNumber?: string): Promise<ServiceResponse<{ messageId: string }>> {
    try {
      if (!to || !message) {
        throw new ValidationError('Recipient and message are required')
      }

      const response = await microservicesClient.sendMessage({
        to,
        message,
        from_number: fromNumber
      })

      if (!response.success) {
        throw response.error || new ServiceError('Failed to send message', 'SEND_MESSAGE_ERROR')
      }

      return {
        data: { messageId: response.data?.message_id || 'unknown' },
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error sending message:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to send message', 'SEND_MESSAGE_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get contact information by phone number
   */
  async getContactByPhone(phoneNumber: string): Promise<ServiceResponse<any>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      // Get current user ID from auth store
      const authStore = useAuthStore()
      const userId = authStore.user?.uid

      if (!userId) {
        throw new ValidationError('User not authenticated')
      }

      const response = await microservicesClient.getContactByPhone(phoneNumber, userId)

      if (!response.success) {
        // If contact not found, return null instead of error
        if (response.error?.code === 'NOT_FOUND') {
          return {
            data: null,
            success: true
          }
        }
        throw response.error || new ServiceError('Failed to get contact', 'GET_CONTACT_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting contact:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get contact', 'GET_CONTACT_ERROR'),
        success: false
      }
    }
  }

  /**
   * Analyze conversation for lead status
   */
  async analyzeConversation(contactId: string, messages: any[], agentId?: string): Promise<ServiceResponse<ConversationAnalysis>> {
    try {
      if (!contactId || !messages || messages.length === 0) {
        throw new ValidationError('Contact ID and messages are required')
      }

      const response = await microservicesClient.analyzeConversation(contactId, messages, agentId)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to analyze conversation', 'ANALYZE_CONVERSATION_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error analyzing conversation:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to analyze conversation', 'ANALYZE_CONVERSATION_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get lead status options
   */
  getLeadStatusOptions(): { value: string; label: string }[] {
    return [
      { value: 'NEW', label: 'Nuevo' },
      { value: 'OPEN', label: 'Abierto' },
      { value: 'IN_PROGRESS', label: 'En progreso' },
      { value: 'OPEN_DEAL', label: 'Negocio abierto' },
      { value: 'UNQUALIFIED', label: 'No calificado' },
      { value: 'ATTEMPTED_TO_CONTACT', label: 'Intento de contacto' },
      { value: 'CONNECTED', label: 'Conectado' },
      { value: 'BAD_TIMING', label: 'Mal momento' }
    ]
  }

  /**
   * Update lead status manually
   */
  async updateLeadStatus(phoneNumber: string, leadStatus: string): Promise<ServiceResponse<any>> {
    try {
      if (!phoneNumber || !leadStatus) {
        throw new ValidationError('Phone number and lead status are required')
      }

      // Get current user ID from auth store
      const authStore = useAuthStore()
      const userId = authStore.user?.uid

      if (!userId) {
        throw new ValidationError('User not authenticated')
      }

      const response = await microservicesClient.updateLeadStatus({
        contact_id: phoneNumber,
        user_id: userId,
        lead_status: leadStatus,
        reason: 'Manual update from WhatsApp interface'
      })

      if (!response.success) {
        throw response.error || new ServiceError('Failed to update lead status', 'UPDATE_LEAD_STATUS_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error updating lead status:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to update lead status', 'UPDATE_LEAD_STATUS_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get lead status history for a contact
   */
  async getLeadStatusHistory(phoneNumber: string): Promise<ServiceResponse<any[]>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      const response = await microservicesClient.getLeadStatusHistory(phoneNumber)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get lead status history', 'GET_LEAD_STATUS_HISTORY_ERROR')
      }

      return {
        data: response.data || [],
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting lead status history:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get lead status history', 'GET_LEAD_STATUS_HISTORY_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get conversation history for a specific agent and phone number
   */
  async getConversationByAgentAndPhone(agentId: string, phoneNumber: string, limit: number = 50): Promise<ServiceResponse<any>> {
    try {
      if (!agentId || !phoneNumber) {
        throw new ValidationError('Agent ID and phone number are required')
      }

      const response = await microservicesClient.getConversationByAgentAndPhone(agentId, phoneNumber, limit)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get conversation', 'GET_CONVERSATION_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting conversation by agent and phone:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get conversation', 'GET_CONVERSATION_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get all conversations for a specific agent
   */
  async getConversationsByAgent(agentId: string, limit: number = 50): Promise<ServiceResponse<any>> {
    try {
      if (!agentId) {
        throw new ValidationError('Agent ID is required')
      }

      const response = await microservicesClient.getConversationsByAgent(agentId, limit)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get conversations', 'GET_CONVERSATIONS_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting conversations by agent:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get conversations', 'GET_CONVERSATIONS_ERROR'),
        success: false
      }
    }
  }

  /**
   * Get conversation history for a phone number (DEPRECATED - use getConversationByAgentAndPhone)
   */
  async getConversation(phoneNumber: string, limit: number = 50): Promise<ServiceResponse<any>> {
    try {
      if (!phoneNumber) {
        throw new ValidationError('Phone number is required')
      }

      const response = await microservicesClient.getConversation(phoneNumber, limit)

      if (!response.success) {
        throw response.error || new ServiceError('Failed to get conversation', 'GET_CONVERSATION_ERROR')
      }

      return {
        data: response.data,
        success: true
      }

    } catch (error: any) {
      console.error('❌ [WhatsAppMicroservicesClient] Error getting conversation:', error)

      if (error instanceof ServiceError || error instanceof ValidationError) {
        return {
          error,
          success: false
        }
      }

      return {
        error: new ServiceError('Failed to get conversation', 'GET_CONVERSATION_ERROR'),
        success: false
      }
    }
  }
}

// Export singleton instance
export const whatsappMicroservicesClient = new WhatsAppMicroservicesClient()
