import type { IAssistantService } from './core/types'
import { logBackendConfig } from '@/config/backend'
import { FirebaseAssistantAdapter } from './adapters/firebaseAdapter'

/**
 * Service factory for creating Firebase Functions service instances
 */
class ServiceFactory {
  private assistantServiceInstance: IAssistantService | null = null

  constructor() {
    // Log configuration on initialization for debugging
    if (import.meta.env.DEV) {
      logBackendConfig()
    }
  }

  /**
   * Get the assistant service instance (singleton pattern)
   * Only Firebase Functions are supported for assistants
   */
  getAssistantService(): IAssistantService {
    if (!this.assistantServiceInstance) {
      this.assistantServiceInstance = new FirebaseAssistantAdapter()
    }
    return this.assistantServiceInstance
  }

  /**
   * Reset the service instance (useful for testing)
   */
  reset(): void {
    this.assistantServiceInstance = null
  }

  /**
   * Check if Firebase Functions are available
   */
  async healthCheck(): Promise<boolean> {
    try {
      // For Firebase, we assume it's available if the service can be created
      this.getAssistantService()
      return true
    } catch (error) {
      console.error('❌ Firebase Functions health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const serviceFactory = new ServiceFactory()

// Export convenience functions
export function getAssistantService(): IAssistantService {
  return serviceFactory.getAssistantService()
}

export async function checkServiceHealth(): Promise<boolean> {
  return serviceFactory.healthCheck()
}
