<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  signInWithEmailAndPassword,
  setPersistence,
  browserLocalPersistence,
  browserSessionPersistence,
  signInWithPopup
} from 'firebase/auth';
import { auth, googleProvider } from '@/plugins/firebase';
import { useForm, useField } from 'vee-validate';
import { object, string } from 'yup';

const router = useRouter();
const remember = ref(false);

const schema = object({
  email: string().email('Correo inválido').required('El email es obligatorio'),
  password: string().required('La contraseña es obligatoria'),
});

const { handleSubmit } = useForm({ validationSchema: schema });

// Desestructurar useField para obtener value y errorMessage directamente
const { value: emailValue, errorMessage: emailError } = useField<string>('email');
const { value: passwordValue, errorMessage: passwordError } = useField<string>('password');

const onSubmit = handleSubmit(async (values) => {
  try {
    await setPersistence(auth, remember.value ? browserLocalPersistence : browserSessionPersistence);
    await signInWithEmailAndPassword(auth, values.email, values.password);
    router.push('/');
  } catch (error: any) {
    alert(error.message);
  }
});

const loginWithGoogle = async () => {
  try {
    await setPersistence(auth, remember.value ? browserLocalPersistence : browserSessionPersistence);
    await signInWithPopup(auth, googleProvider);
    router.push('/');
  } catch (error: any) {
    alert(error.message);
  }
};
</script>

<template>
  <form @submit.prevent="onSubmit">
    <v-row>
      <v-col cols="12">
        <v-label>Email</v-label>
        <v-text-field v-model="emailValue" :error-messages="emailError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <v-label>Contraseña</v-label>
        <v-text-field type="password" v-model="passwordValue" :error-messages="passwordError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <div class="d-flex align-center">
          <v-checkbox v-model="remember" hide-details color="primary">
            <template #label>Recordar este dispositivo</template>
          </v-checkbox>
          <div class="ml-auto">
            <RouterLink to="/recuperar" class="text-primary text-decoration-none">Olvidé mi contraseña</RouterLink>
          </div>
        </div>
      </v-col>

      <v-col cols="12">
        <v-btn type="submit" color="primary" block>Login</v-btn>
      </v-col>

      <v-col cols="12">
        <v-btn
          @click="loginWithGoogle"
          class="google-btn"
          block
          variant="outlined"
        >
          <v-img
            src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
            alt="Google"
            width="20"
            height="20"
            class="mr-3"
            cover
          />
          Entrar con Google
        </v-btn>
            </v-col>
    </v-row>
  </form>
</template>

<style scoped>
.google-btn {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #dadce0;
  font-weight: 500;
  text-transform: none;
  transition: box-shadow 0.2s ease-in-out;
}

.google-btn:hover {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
</style>
