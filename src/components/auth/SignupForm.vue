<script setup lang="ts">
import { useRouter } from 'vue-router';
import { createUserWithEmailAndPassword, signInWithPopup } from 'firebase/auth';
import { auth, googleProvider } from '@/plugins/firebase';
import { useField, useForm } from 'vee-validate';
import * as yup from 'yup';

const router = useRouter();

const schema = yup.object({
  name: yup.string().required('El nombre es obligatorio'),
  email: yup.string().email('Correo inválido').required('El email es obligatorio'),
  password: yup.string().min(6, 'Mínimo 6 caracteres').required('La contraseña es obligatoria'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Las contraseñas no coinciden')
    .required('Repite la contraseña'),
  acceptTerms: yup.bool().oneOf([true], 'Debes aceptar los términos'),
});

const { handleSubmit } = useForm({ validationSchema: schema });

const { value: nameValue, errorMessage: nameError } = useField<string>('name');
const { value: emailValue, errorMessage: emailError } = useField<string>('email');
const { value: passwordValue, errorMessage: passwordError } = useField<string>('password');
const { value: confirmPasswordValue, errorMessage: confirmPasswordError } = useField<string>('confirmPassword');

const onSubmit = handleSubmit(async (values) => {
  try {
    await createUserWithEmailAndPassword(auth, values.email, values.password);
    router.push('/');
  } catch (error: any) {
    alert(error.message);
  }
});

const signupWithGoogle = async () => {
  try {
    await signInWithPopup(auth, googleProvider);
    router.push('/');
  } catch (error: any) {
    alert(error.message);
  }
};
</script>

<template>
  <form @submit.prevent="onSubmit">
    <v-row>
      <v-col cols="12">
        <v-label>Nombre</v-label>
        <v-text-field v-model="nameValue" :error-messages="nameError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <v-label>Email</v-label>
        <v-text-field v-model="emailValue" :error-messages="emailError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <v-label>Contraseña</v-label>
        <v-text-field type="password" v-model="passwordValue" :error-messages="passwordError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <v-label>Repite la contraseña</v-label>
        <v-text-field type="password" v-model="confirmPasswordValue" :error-messages="confirmPasswordError" hide-details="auto" variant="outlined" density="compact" />
      </v-col>

      <v-col cols="12">
        <v-btn type="submit" color="primary" block>Crear cuenta</v-btn>
      </v-col>

      <v-col cols="12">
        <v-btn @click="signupWithGoogle" color="secondary" block>Registrarse con Google</v-btn>
      </v-col>

      <v-col cols="12" class="px-10 text-center">
        <span>
          Al registrarte aceptas los <a href="https://synapsesea.com/terminos" target="_blank" rel="noopener noreferrer" class="text-primary">Términos y Condiciones</a> y la <a href="https://synapsesea.com/privacidad" target="_blank" rel="noopener noreferrer" class="text-primary">Política de Privacidad</a>
        </span>
      </v-col>
    </v-row>
  </form>
</template>
