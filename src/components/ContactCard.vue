<template>
  <v-card variant="outlined" class="h-100">
    <v-card-text>
      <div class="d-flex align-center mb-3">
        <v-avatar size="40" :color="isActive ? 'success' : 'grey'" class="mr-3">
          <span class="text-white font-weight-bold">{{ contact.name.charAt(0) }}</span>
        </v-avatar>
        <div class="flex-grow-1">
          <h4 class="text-subtitle-1 font-weight-bold">{{ contact.name }}</h4>
          <p class="text-caption text-medium-emphasis mb-0">{{ contact.phone }}</p>
        </div>
        <v-chip :color="isActive ? 'success' : 'grey'" size="small" variant="tonal">
          {{ isActive ? 'Activo' : 'Inactivo' }}
        </v-chip>
      </div>

      <!-- Agent Info for Active Contacts -->
      <div v-if="isActive && agentName" class="mb-3">
        <p class="text-caption text-medium-emphasis mb-1">Asistente:</p>
        <p class="text-body-2 font-weight-medium">{{ agentName }}</p>
      </div>

      <!-- Company Info for Inactive Contacts -->
      <div v-if="!isActive" class="mb-3">
        <p class="text-caption text-medium-emphasis mb-1">Empresa:</p>
        <p class="text-body-2">{{ contact.company || 'No especificada' }}</p>
      </div>

      <!-- Lead Status Section -->
      <div v-if="showLeadStatus" class="mb-3">
        <p class="text-caption text-medium-emphasis mb-1">Estado del Lead:</p>
        <div class="d-flex align-center gap-2">
          <v-chip
            :color="getLeadStatusColor(contact.leadStatus)"
            size="small"
            variant="tonal"
          >
            {{ getLeadStatusLabel(contact.leadStatus) }}
          </v-chip>

          <v-menu>
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                size="x-small"
                variant="text"
                icon
                :loading="updatingStatus"
              >
                <Icon icon="solar:pen-broken" width="14" />
              </v-btn>
            </template>

            <v-list density="compact">
              <v-list-item
                v-for="option in leadStatusOptions"
                :key="option.key"
                :value="option.key"
                @click="updateStatus(option.key)"
              >
                <v-list-item-title>{{ option.label }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="isActive" class="d-flex gap-2 mb-3">
        <v-btn
          size="small"
          variant="outlined"
          color="primary"
          @click="$emit('toggle-messages', contact)"
        >
          <Icon icon="solar:chat-dots-broken" class="mr-1" width="16" />
          {{ showMessages ? 'Ocultar' : 'Ver' }} Chat
        </v-btn>

        <v-btn
          size="small"
          variant="outlined"
          color="error"
          @click="$emit('deactivate', contact)"
        >
          <Icon icon="solar:stop-broken" class="mr-1" width="16" />
          Desactivar
        </v-btn>
      </div>

      <v-btn
        v-else
        color="primary"
        variant="flat"
        size="small"
        block
        @click="$emit('activate', contact)"
      >
        <Icon icon="solar:play-broken" class="mr-1" width="16" />
        Activar Asistente
      </v-btn>

      <!-- Collapsible Message Panel for Active Contacts -->
      <v-expand-transition>
        <div v-if="isActive && showMessages" class="mt-4">
          <v-divider class="mb-3" />
          <div class="message-panel">
            <h5 class="text-subtitle-2 mb-2">Conversación</h5>
            <div class="messages-container" style="max-height: 200px; overflow-y: auto;">
              <div
                v-for="message in messages || []"
                :key="message.sid"
                class="message-item mb-2"
              >
                <div
                  :class="[
                    'message-bubble pa-2 rounded',
                    message.direction === 'inbound'
                      ? 'bg-grey-lighten-4 align-self-start'
                      : 'bg-primary text-white align-self-end ml-auto'
                  ]"
                  style="max-width: 80%; word-wrap: break-word;"
                >
                  <p class="text-body-2 mb-1">{{ message.body }}</p>
                  <p class="text-caption opacity-70">
                    {{ new Date(message.timestamp).toLocaleTimeString() }}
                  </p>
                </div>
              </div>

              <!-- Empty state for no messages -->
              <div v-if="!messages || messages.length === 0" class="text-center py-4">
                <Icon icon="solar:chat-dots-broken" class="mb-2 text-medium-emphasis" width="32" />
                <p class="text-body-2 text-medium-emphasis">No hay mensajes aún</p>
              </div>
            </div>
          </div>
        </div>
      </v-expand-transition>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useContactsStore } from '@/store'
import type { Contact } from '@/store/useContactsStore'

interface Props {
  contact: Contact
  showLeadStatus?: boolean
  isActive?: boolean
  agentName?: string
  showMessages?: boolean
  messages?: Array<{
    sid: string
    body: string
    direction: string
    timestamp: string
    from: string
    to: string
    status: string
  }>
}

interface Emits {
  (e: 'activate', contact: Contact): void
  (e: 'deactivate', contact: Contact): void
  (e: 'update-lead-status', contact: Contact, status: string): void
  (e: 'toggle-messages', contact: Contact): void
}

const props = withDefaults(defineProps<Props>(), {
  showLeadStatus: false,
  isActive: false,
  agentName: '',
  showMessages: false,
  messages: () => []
})

const emit = defineEmits<Emits>()

const contactsStore = useContactsStore()
const updatingStatus = ref(false)

// Computed properties
const leadStatusOptions = computed(() => contactsStore.leadStatusOptions)

// Methods
function getLeadStatusLabel(status?: string): string {
  if (!status) return 'Nuevo'
  return contactsStore.getLeadStatusLabel(status)
}

function getLeadStatusColor(status?: string): string {
  const colorMap: Record<string, string> = {
    'NEW': 'blue',
    'OPEN': 'green',
    'IN_PROGRESS': 'orange',
    'OPEN_DEAL': 'purple',
    'UNQUALIFIED': 'grey',
    'ATTEMPTED_TO_CONTACT': 'yellow',
    'CONNECTED': 'teal',
    'BAD_TIMING': 'red'
  }

  return colorMap[status || 'NEW'] || 'blue'
}

async function updateStatus(newStatus: string) {
  if (newStatus === props.contact.leadStatus) return

  try {
    updatingStatus.value = true
    emit('update-lead-status', props.contact, newStatus)
  } catch (error) {
    console.error('Error updating lead status:', error)
  } finally {
    updatingStatus.value = false
  }
}
</script>
