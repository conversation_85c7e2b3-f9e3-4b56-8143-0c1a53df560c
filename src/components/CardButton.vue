<script setup lang="ts">
import { Icon } from '@iconify/vue'

const emit = defineEmits(['click']);
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  subtitle: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
});

function onClick() {
  emit('click');
}
</script>

<template>
  <v-card
    class="card-hover d-flex flex-column align-center justify-center pa-6 text-center position-relative"
    elevation="0"
    @click="onClick"
  >
    <Icon :icon="icon" class="mb-4 text-primary" width="40" />
    <h2 class="text-subtitle-1 font-weight-bold mb-1">{{ title }}</h2>
    <p class="text-body-2 text-medium-emphasis">{{ subtitle }}</p>

    <div class="badge-slot">
      <slot name="badge" />
    </div>
  </v-card>
</template>

<style scoped>
.card-hover {
  width: 100%;
  max-width: 260px;
  height: 260px;
  border-radius: 12px;
  border: 1px solid rgb(var(--v-theme-borderColor));
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.card-hover:hover {
  background-color: rgb(var(--v-theme-lightprimary));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  cursor: pointer;
}

.badge-slot {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}
</style>
