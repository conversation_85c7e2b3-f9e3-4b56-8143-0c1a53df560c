<script setup>
import CardButton from '@/components/CardButton.vue';

defineProps({
  cards: Array,
})

defineEmits(['select'])
</script>

<template>
  <div class="d-flex flex-column justify-center align-center text-center flex-grow-1">
    <div class="mb-10">
      <h1 class="text-h5 font-weight-bold mb-2">Crear nuevo asistente</h1>
      <p class="text-body-1 text-medium-emphasis">
        Elige el tipo de asistente que quieres crear
      </p>
    </div>

    <v-row class="w-100 justify-center">
      <v-col
        v-for="(card, index) in cards"
        :key="index"
        cols="12"
        sm="6"
        md="4"
        class="d-flex justify-center"
      >
        <CardButton
          :title="card.title"
          :subtitle="card.subtitle"
          :icon="card.icon"
          @click="() => $emit('select', card.type)"
        />
      </v-col>
    </v-row>
  </div>
</template>
