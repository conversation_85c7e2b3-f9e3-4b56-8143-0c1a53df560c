<script setup>
const props = defineProps({ item: Object });
</script>

<template>
    <!-- Empty header acts as a separator -->
    <v-divider v-if="!props.item.header || props.item.header === ''" class="my-4 mx-4" />
    <!-- Regular header -->
    <v-list-subheader v-else color="darkText" class="smallCap text-uppercase text-subtitle-2 mt-5 font-weight-bold mini-text">{{ props.item.header}}</v-list-subheader>
</template>
