
import MainRoutes from '@/router/MainRoutes';

export interface menu {
    header?: string;
    title?: string;
    icon?: any;
    to?: string;
    chip?: string;
    BgColor?: string;
    chipBgColor?: string;
    chipColor?: string;
    chipVariant?: string;
    chipIcon?: string;
    children?: menu[];
    disabled?: boolean;
    type?: string;
    subCaption?: string;
}

// Helper function to create menu item from route
const createMenuItem = (route: any): menu => ({
  title: route.meta?.title || route.name,
  to: route.path,
  icon: route.meta?.icon,
  chip: route.meta?.chip,
  BgColor: route.meta?.BgColor,
  chipBgColor: route.meta?.chipBgColor,
  chipColor: route.meta?.chipColor,
  chipVariant: route.meta?.chipVariant,
  chipIcon: route.meta?.chipIcon,
  disabled: route.meta?.disabled,
  subCaption: route.meta?.subCaption,
});

// Helper function to find route by name
const findRoute = (name: string) => MainRoutes.children.find(route => route.name === name);

const sidebarItem: menu[] = [
  // Main navigation
  createMenuItem(findRoute('dashboard')!),
  createMenuItem(findRoute('my-assistants-layout')!),
  createMenuItem(findRoute('marketplace')!),
  createMenuItem(findRoute('test-assistants')!),

  // Separator
  { header: '' },

  // Integrations section
  createMenuItem(findRoute('integrations-layout')!),
  createMenuItem(findRoute('contacts')!),
  createMenuItem(findRoute('conversations')!),
  createMenuItem(findRoute('phone-numbers')!),

  // Business Data section
  createMenuItem(findRoute('business-data-layout')!),

  // Separator
  { header: '' },

  // Upgrade section
  createMenuItem(findRoute('upgrade-plan')!),
  createMenuItem(findRoute('profile-settings')!),
];

export default sidebarItem;
