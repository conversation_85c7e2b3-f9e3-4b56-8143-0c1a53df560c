<script setup lang="ts">
import { RouterLink } from 'vue-router';
import LogoImage from '@/assets/images/logos/synapy_logo.png';
import LogoCollapsed from '@/assets/images/logos/favicon.svg';

defineProps({
  collapsed: Boolean,
})
</script>
<template>
    <div class="logo">
        <RouterLink to="/">
            <img v-if="collapsed" :src="LogoCollapsed" alt="home" style="width: 100%; height: auto;" />
            <img v-else :src="LogoImage" alt="home" style="width: 100%; height: auto;" />
        </RouterLink>
    </div>
</template>