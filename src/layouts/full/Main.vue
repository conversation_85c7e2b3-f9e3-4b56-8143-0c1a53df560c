<script setup lang="ts">
import { ref, shallowRef, computed } from 'vue';
import { useRoute } from 'vue-router';
import sidebarItems from './vertical-sidebar/sidebarItem';
import NavGroup from './vertical-sidebar/NavGroup/index.vue';
import NavItem from './vertical-sidebar/NavItem/index.vue';
import Logo from './logo/Logo.vue';
import { useNavigationStore, useAuthStore } from '@/store';

const DRAWER_WIDTH = 256;
const DRAWER_RAIL_IDTH = 56;

const route = useRoute()

const store = useNavigationStore()
const auth = useAuthStore();

const sidebarMenu = shallowRef(sidebarItems);

const forcedRail = computed(() => route.meta?.rail);
const drawerWidth = computed(() => store.isRail ? DRAWER_RAIL_IDTH : DRAWER_WIDTH)

function toggleRail() {
  store.setRail(!store.isRail);
}
</script>

<template>
  <v-navigation-drawer
    permanent
    class="leftSidebar"
    :rail="forcedRail || store.isRail"
    :width="DRAWER_WIDTH"
    :rail-width="DRAWER_RAIL_IDTH"
  >
    <div :class="[store.isRail ? 'pa-2 mb-12' : 'pa-4']">
        <Logo :collapsed="store.isRail" />
    </div>

    <div class="scrollnavbar">
      <v-list nav>
          <template v-for="(item, i) in sidebarMenu">
              <NavGroup :item="item" v-if="item.header" :key="item.title" />

              <NavItem v-else
                class="leftPadding"
                :title="item.title"
                :to="item.to"
                :disabled="item.disabled"
                :icon="item.icon"
                :BgColor="item.BgColor"
                :subCaption="item.subCaption"
                :chip="item.chip"
                :chipColor="item.chipColor"
                :chipBgColor="item.chipBgColor"
                :chipVariant="item.chipVariant"
                :chipIcon="item.chipIcon"
              />
          </template>
      </v-list>
    </div>


  </v-navigation-drawer>

  <v-btn
    v-if="!forcedRail"
    icon
    size="x-small"
    elevation="4"
    color="white"
    class="rail-toggle-btn"
    @click="toggleRail"
    :style="{ left: `${drawerWidth - 16}px` }"
  >
    <v-icon>{{ store.isRail ? 'mdi-chevron-right' : 'mdi-chevron-left' }}</v-icon>
  </v-btn>
</template>

<style scoped>
.rail-toggle-btn {
  position: absolute;
  top: 64px;
  z-index: 2000;
  transition: left 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  /* Vuetify drawer transition default */
}
</style>
