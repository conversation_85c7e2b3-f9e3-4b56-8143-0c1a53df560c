import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from '@/store'
import {
  createAssistant,
  updateAssistant,
  deleteAssistant,
  getAssistantDetails,
  fetchAssistants
} from '@/services/assistantService'
import { checkServiceHealth } from '@/services/serviceFactory'
import type { Assistant } from '@/types/domain'

export const useAssistantsStore = defineStore('assistant', () => {
  const assistants = ref<Assistant[]>([])
  const selectedAssistantId = ref<string | null>(null)
  const isLoading = ref(false)
  const hasFetched = ref(false)
  const lastError = ref<string | null>(null)

  const selectedAssistant = computed(() => {
    return assistants.value.find(a => a.id === selectedAssistantId.value) || null
  })

  function setSelectedAssistantId(id: string | null) {
    selectedAssistantId.value = id
  }

  function clearError() {
    lastError.value = null
  }

  async function fetchAllAssistants() {
    if (hasFetched.value) return

    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) {
      const error = '[fetchAllAssistants] No user ID found.'
      lastError.value = error
      throw new Error(error)
    }

    isLoading.value = true
    clearError()
    try {
      const data = await fetchAssistants(userId)
      if (!data) throw new Error('Failed to fetch assistants.')

      assistants.value = data
      if (data.length > 0) {
        selectedAssistantId.value = data[0].id
      }
      hasFetched.value = true
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch assistants'
      lastError.value = errorMessage
      console.error(`❌ Fetch assistants error:`, err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function createNewAssistant(type: string, companyName: string) {
    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) throw new Error('[createNewAssistant] No user ID found.')

    try {
      const assistant = await createAssistant(userId, type, companyName)
      if (!assistant?.id) throw new Error('Assistant creation failed: Missing ID.')

      assistants.value.push(assistant)
      selectedAssistantId.value = assistant.id
      return assistant
    } catch (err) {
      throw err
    }
  }

  async function fetchAssistantDetailsById(id: string) {
    try {
      const assistant = await getAssistantDetails(id)
      if (!assistant) throw new Error('Failed to fetch assistant details.')

      const index = assistants.value.findIndex(a => a.id === id)
      if (index !== -1) {
        assistants.value[index] = { ...assistants.value[index], ...assistant }
      } else {
        assistants.value.push(assistant)
      }

      return assistant
    } catch (err) {
      throw err
    }
  }

  async function updateAssistantById(assistantId: string, data: Assistant) {
    try {
      const success = await updateAssistant(assistantId, data)
      if (!success) throw new Error('Failed to update assistant.')

      const index = assistants.value.findIndex(a => a.id === assistantId)
      if (index !== -1) {
        assistants.value[index] = { ...assistants.value[index], ...data }
      }
    } catch (err) {
      throw err
    }
  }

  async function deleteAssistantById(assistantId: string) {
    try {
      const success = await deleteAssistant(assistantId)
      if (!success) throw new Error('Failed to delete assistant.')

      assistants.value = assistants.value.filter(a => a.id !== assistantId)
      if (selectedAssistantId.value === assistantId) {
        selectedAssistantId.value = assistants.value.length > 0 ? assistants.value[0].id : null
      }
    } catch (err) {
      throw err
    }
  }

  return {
    assistants,
    selectedAssistantId,
    selectedAssistant,
    isLoading,
    hasFetched,
    lastError,
    fetchAllAssistants,
    createNewAssistant,
    fetchAssistantDetailsById,
    updateAssistantById,
    deleteAssistantById,
    setSelectedAssistantId,
    clearError,
  }
})
