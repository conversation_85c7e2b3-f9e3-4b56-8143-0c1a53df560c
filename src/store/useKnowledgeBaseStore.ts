import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from '@/store'
import type { KnowledgeBase } from '@/types/domain'
import {
  fetchKnowledgeBases,
  createKnowledgeBase,
  addKnowledgeBaseSources,
  deleteKnowledgeBaseSource,
  deleteKnowledgeBase,
  getKnowledgeBaseDetails,
  type KnowledgeBaseFormData,
  type AddSourcesFormData
} from '@/services/knowledgeBaseService'

export const useKnowledgeBasesStore = defineStore('knowledgeBase', () => {
  const knowledgeBases = ref<KnowledgeBase[]>([])
  const selectedKBId = ref<string | null>(null)
  const isLoading = ref(false)
  const hasFetched = ref(false)

  const selectedKnowledgeBase = computed(() =>
    knowledgeBases.value.find(kb => kb.id === selectedKBId.value) || null
  )

  function setSelectedKBId(id: string | null) {
    selectedKBId.value = id
  }

  async function fetchAllKnowledgeBases() {
    if (hasFetched.value) return

    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) throw new Error('[fetchAllKnowledgeBases] No user ID found.')

    isLoading.value = true
    try {
      const data = await fetchKnowledgeBases(userId)
      if (!data) throw new Error('Failed to fetch knowledge bases.')

      knowledgeBases.value = data
      if (data.length > 0) selectedKBId.value = data[0].id
      hasFetched.value = true
    } finally {
      isLoading.value = false
    }
  }

  async function createNewKnowledgeBase(formData: KnowledgeBaseFormData) {
    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) throw new Error('[createNewKnowledgeBase] No user ID found.')

    try {
      const kb = await createKnowledgeBase(userId, formData)
      if (!kb?.id) throw new Error('Creation failed: missing ID.')

      knowledgeBases.value.push(kb)
      selectedKBId.value = kb.id
      return kb
    } catch (err) {
      throw err
    }
  }

  async function fetchKnowledgeBaseDetailsById(id: string) {
    try {
      const kb = await getKnowledgeBaseDetails(id)
      if (!kb) throw new Error('Failed to fetch knowledge base details.')

      const index = knowledgeBases.value.findIndex(k => k.id === id)
      if (index !== -1) {
        knowledgeBases.value[index] = { ...knowledgeBases.value[index], ...kb }
      } else {
        knowledgeBases.value.push(kb)
      }

      return kb
    } catch (err) {
      throw err
    }
  }

  async function addKnowledgeBaseSourcesById(kbId: string, formData: AddSourcesFormData) {
    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) throw new Error('[addKnowledgeBaseSourcesById] No user ID found.')

    try {
      const updated = await addKnowledgeBaseSources(userId, kbId, formData)
      if (!updated) throw new Error('Failed to add sources to knowledge base.')

      const index = knowledgeBases.value.findIndex(k => k.id === kbId)
      if (index !== -1) {
        knowledgeBases.value[index] = { ...knowledgeBases.value[index], ...updated }
      }

      return updated
    } catch (err) {
      throw err
    }
  }

  async function deleteKnowledgeBaseSourceById(kbId: string, sourceId: string) {
    try {
      const updated = await deleteKnowledgeBaseSource(kbId, sourceId)
      if (!updated) throw new Error('Failed to delete source from knowledge base.')

      const index = knowledgeBases.value.findIndex(k => k.id === kbId)
      if (index !== -1) {
        knowledgeBases.value[index] = { ...knowledgeBases.value[index], ...updated }
      }

      return updated
    } catch (err) {
      throw err
    }
  }

  async function deleteKnowledgeBaseById(kbId: string) {
    try {
      const success = await deleteKnowledgeBase(kbId)
      if (!success) throw new Error('Failed to delete knowledge base.')

      knowledgeBases.value = knowledgeBases.value.filter(k => k.id !== kbId)
      if (selectedKBId.value === kbId) {
        selectedKBId.value = knowledgeBases.value.length > 0 ? knowledgeBases.value[0].id : null
      }
    } catch (err) {
      throw err
    }
  }

  return {
    knowledgeBases,
    selectedKBId,
    selectedKnowledgeBase,
    isLoading,
    hasFetched,
    setSelectedKBId,
    fetchAllKnowledgeBases,
    createNewKnowledgeBase,
    fetchKnowledgeBaseDetailsById,
    addKnowledgeBaseSourcesById,
    deleteKnowledgeBaseSourceById,
    deleteKnowledgeBaseById
  }
})
