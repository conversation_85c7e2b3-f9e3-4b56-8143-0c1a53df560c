import { collection, getDoc, updateDoc, setDoc, doc } from 'firebase/firestore'
import { db } from '@/plugins/firebase'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useAuthStore } from '@/store'

export const useProfileStore = defineStore('profile', () => {
  const companyName = ref<string | null>(null)
  const isLoading = ref(false)

  async function fetchCompanyName() {
    const authStore = useAuthStore()
    const userId = authStore.user?.uid
    if (!userId) return

    isLoading.value = true
    try {
      const profileDoc = await _getUserProfileDoc(userId)
      companyName.value = profileDoc?.data()?.companyName || null
    } catch (error) {
      console.error('❌ Error fetching company name:', error)
      companyName.value = null
    } finally {
      isLoading.value = false
    }
  }

  async function updateCompanyName(newName: string) {
  const authStore = useAuthStore()
  const userId = authStore.user?.uid
  if (!userId) return

  try {
    const existingDoc = await _getUserProfileDoc(userId)
    const profileCollection = collection(db, 'profile')

    if (existingDoc) {
      await updateDoc(existingDoc.ref, {
        companyName: newName,
        updatedAt: new Date()
      })
    } else {
      await setDoc(doc(profileCollection, userId), {
        userId,
        companyName: newName,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }

    companyName.value = newName
  } catch (error) {
    console.error('❌ Error updating company name:', error)
    throw error
  }
}

  return {
    companyName,
    isLoading,
    fetchCompanyName,
    updateCompanyName,
  }
})

async function _getUserProfileDoc(userId: string) {
  try {
    const profileCollection = collection(db, 'profile')

    const docRef = doc(profileCollection, userId)
    const docSnapshot = await getDoc(docRef)

    return docSnapshot.exists() ? docSnapshot : null
  } catch (error) {
    console.warn('⚠️ User Profile could not be fetched from DB:', error)
    return null
  }
}
