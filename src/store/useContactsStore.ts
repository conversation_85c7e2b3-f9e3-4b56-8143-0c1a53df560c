import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from '@/store'
import { db } from '@/plugins/firebase'
import {
  doc,
  getDoc,
  getDocs,
  query,
  where,
  collection,
} from 'firebase/firestore'
import { hubspotService } from '@/services/hubspotService'
import { whatsappMicroservicesClient } from '@/services/whatsapp/whatsappMicroservicesClient'

export interface Contact {
  id: string
  name: string
  phone: string
  email?: string
  company?: string
  source: 'hubspot' | 'manual'
  hubspotId?: string
  lastActivity?: string
  properties?: Record<string, any>
  leadStatus?: string
  leadStatusLabel?: string
  createdAt?: string
}

export interface HubSpotConfig {
  apiKey: string
  portalId: string
  accessToken?: string
  refreshToken?: string
  leadPipeline?: string
  dealPipeline?: string
  contactOwner?: string
  autoCreateContacts: boolean
  autoCreateDeals: boolean
  syncActivities: boolean
  enabled: boolean
}

export interface WhatsAppAgent {
  contactId: string
  agentId: string
  isActive: boolean
  welcomeMessage?: string
  activatedAt?: string
  lastMessageAt?: string
}

export const useContactsStore = defineStore('contacts', () => {
  const contacts = ref<Contact[]>([])
  const hubspotConfig = ref<HubSpotConfig | null>(null)
  const whatsappAgents = ref<WhatsAppAgent[]>([])
  const isLoading = ref(false)
  const lastError = ref<string | null>(null)

  // Lead status state
  const leadStatusOptions = ref<Array<{key: string, label: string}>>([])
  const leadStatusHistory = ref<Record<string, any[]>>({}) // phone_number -> history array

  const authStore = useAuthStore()

  // Computed properties
  const hubspotContacts = computed(() =>
    contacts.value.filter(contact => contact.source === 'hubspot')
  )

  const activeWhatsAppContacts = computed(() =>
    whatsappAgents.value.filter(agent => agent.isActive)
  )

  const getContactById = computed(() => (id: string) =>
    contacts.value.find(contact => contact.id === id)
  )

  const getWhatsAppAgentByContact = computed(() => (contactId: string) =>
    whatsappAgents.value.find(agent => agent.contactId === contactId)
  )

  // Lead status computed properties
  const contactsByLeadStatus = computed(() => {
    const grouped: Record<string, Contact[]> = {}

    contacts.value.forEach(contact => {
      const status = contact.leadStatus || 'NEW'
      if (!grouped[status]) {
        grouped[status] = []
      }
      grouped[status].push(contact)
    })

    return grouped
  })

  const contactsWithLeadStatus = computed(() =>
    contacts.value.filter(contact => contact.leadStatus)
  )

  const getContactsByStatus = computed(() => (status: string) =>
    contacts.value.filter(contact => contact.leadStatus === status)
  )

  const getLeadStatusLabel = computed(() => (statusKey: string) => {
    const option = leadStatusOptions.value.find(opt => opt.key === statusKey)
    return option?.label || statusKey
  })

  // Helper function to get user document path
  function getUserDocPath() {
    const userId = authStore.user?.uid
    if (!userId) throw new Error('User not authenticated')
    return `users/${userId}`
  }

  // HubSpot Configuration Management
  async function saveHubSpotConfig(config: HubSpotConfig) {
    try {
      isLoading.value = true
      lastError.value = null

      // Get current user ID
      const userId = authStore.user?.uid
      if (!userId) {
        throw new Error('User not authenticated')
      }

      // Save configuration via backend service
      const response = await hubspotService.configureHubSpot(config, userId)

      if (!response.success) {
        throw response.error || new Error('Failed to save HubSpot configuration')
      }

      // Update local state only after successful backend save
      hubspotConfig.value = config

      console.log('✅ HubSpot configuration saved successfully via backend')

      // Optionally reload the configuration to ensure consistency with backend
      // This ensures the local state matches exactly what's stored in the backend
      await loadHubSpotConfig()

    } catch (error) {
      console.error('❌ Error saving HubSpot config:', error)
      lastError.value = error instanceof Error ? error.message : 'Failed to save HubSpot configuration'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Helper function to map backend data to frontend HubSpotConfig format
  function mapBackendDataToHubSpotConfig(backendData: any): HubSpotConfig | null {
    if (!backendData) return null

    // Handle both old format (direct config) and new backend format
    const properties = backendData.properties || {}

    return {
      apiKey: backendData.api_key || backendData.apiKey || '',
      portalId: properties.portal_id || backendData.portalId || '',
      accessToken: backendData.accessToken || '',
      refreshToken: backendData.refreshToken || '',
      leadPipeline: properties.lead_pipeline || backendData.leadPipeline || '',
      dealPipeline: properties.deal_pipeline || backendData.dealPipeline || '',
      contactOwner: properties.contact_owner || backendData.contactOwner || '',
      autoCreateContacts: properties.auto_create_contacts ?? backendData.autoCreateContacts ?? true,
      autoCreateDeals: properties.auto_create_deals ?? backendData.autoCreateDeals ?? false,
      syncActivities: properties.sync_activities ?? backendData.syncActivities ?? true,
      enabled: backendData.enabled ?? false
    }
  }

  async function loadHubSpotConfig() {
    try {
      isLoading.value = true
      lastError.value = null

      const userDocPath = getUserDocPath()
      const configDoc = doc(db, userDocPath, 'integrations', 'hubspot')
      const docSnap = await getDoc(configDoc)

      if (docSnap.exists()) {
        const data = docSnap.data()
        hubspotConfig.value = mapBackendDataToHubSpotConfig(data)
      } else {
        hubspotConfig.value = null
        console.warn('📭 No HubSpot configuration found')
      }

    } catch (error) {
      console.error('❌ Error loading HubSpot config:', error)
      lastError.value = 'Failed to load HubSpot configuration'
    } finally {
      isLoading.value = false
    }
  }

  // HubSpot Contacts Management
  async function fetchHubSpotContacts() {
    if (!hubspotConfig.value?.enabled || !hubspotConfig.value.apiKey) {
      console.warn('HubSpot not configured or disabled')
      return
    }

    // Check if user is authenticated
    const authStore = useAuthStore()
    if (!authStore.user) {
      console.warn('User not authenticated for HubSpot integration')
      lastError.value = 'Authentication required for HubSpot integration'
      return
    }

    try {
      isLoading.value = true
      lastError.value = null

      // Fetch contacts from HubSpot API via Contact Service
      const response = await hubspotService.fetchContacts(hubspotConfig.value)

      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to fetch HubSpot contacts')
      }

      const hubspotContacts = response.data

      // Replace existing HubSpot contacts in local state
      contacts.value = contacts.value.filter(c => c.source !== 'hubspot')
      contacts.value.push(...hubspotContacts)

      console.log(`✅ Successfully fetched ${hubspotContacts.length} contacts from HubSpot`)

    } catch (error) {
      console.error('❌ Error fetching HubSpot contacts:', error)
      lastError.value = error instanceof Error ? error.message : 'Failed to fetch HubSpot contacts'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Test HubSpot API connection
  async function testHubSpotConnection() {
    if (!hubspotConfig.value?.apiKey) {
      throw new Error('HubSpot API key is required')
    }

    // Check if user is authenticated
    if (!authStore.user) {
      throw new Error('Authentication required for HubSpot integration')
    }

    try {
      isLoading.value = true
      lastError.value = null

      // Use the user-specific test connection method
      const userId = authStore.user.uid
      const response = await hubspotService.testConnection(hubspotConfig.value, userId)

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to connect to HubSpot')
      }

      return true

    } catch (error) {
      console.error('❌ Error testing HubSpot connection:', error)
      lastError.value = error instanceof Error ? error.message : 'Failed to test HubSpot connection'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // WhatsApp Agent Management
  async function activateWhatsAppAgent(contactId: string, agentId: string, welcomeMessage?: string) {
    try {
      isLoading.value = true
      lastError.value = null

      const contact = contacts.value.find(c => c.id === contactId)
      if (!contact) {
        throw new Error('Contact not found')
      }

      // Call Communications Service to activate agent
      const response = await whatsappMicroservicesClient.setActiveAgent(
        contact.phone,
        agentId,
        welcomeMessage
      )

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to activate WhatsApp agent')
      }

      // Update local state
      const existingIndex = whatsappAgents.value.findIndex(a => a.contactId === contactId && a.agentId === agentId)
      const agentData: WhatsAppAgent = {
        contactId,
        agentId,
        isActive: true,
        welcomeMessage,
        activatedAt: new Date().toISOString()
      }

      if (existingIndex >= 0) {
        whatsappAgents.value[existingIndex] = agentData
      } else {
        whatsappAgents.value.push(agentData)
      }
    } catch (error) {
      console.error('❌ Error activating WhatsApp agent:', error)
      lastError.value = 'Failed to activate WhatsApp agent'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  async function deactivateWhatsAppAgent(contactId: string, agentId: string) {
    try {
      isLoading.value = true
      lastError.value = null

      const contact = contacts.value.find(c => c.id === contactId)
      if (!contact) {
        throw new Error('Contact not found')
      }

      console.log(`Contact found: ${contact.phone}`) // TODO: Remove debug log

      // Call Communications Service to deactivate agent
      const response = await whatsappMicroservicesClient.removeActiveAgent(contact.phone)

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to deactivate WhatsApp agent')
      }

      // Update local state
      const index = whatsappAgents.value.findIndex(a => a.contactId === contactId && a.agentId === agentId)
      if (index >= 0) {
        whatsappAgents.value[index].isActive = false
      }
    } catch (error) {
      console.error('❌ Error deactivating WhatsApp agent:', error)
      lastError.value = 'Failed to deactivate WhatsApp agent'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  async function loadWhatsAppAgents(contactId?: string, agentId?: string) {
    try {
      isLoading.value = true
      lastError.value = null

      const colRef = collection(db, 'active_assistants')
      const filters: any[] = []
      if (contactId) filters.push(where('contact_id', '==', contactId))
      if (agentId) filters.push(where('agent_id', '==', agentId))

      const q = filters.length > 0 ? query(colRef, ...filters) : colRef
      const snapshot = await getDocs(q)

      whatsappAgents.value = snapshot.docs.map(docSnap => {
        const data = docSnap.data()
        return {
          contactId: data.contact_id,
          agentId: data.agent_id,
          isActive: data.status === 'active',
          activatedAt: data.created_at,
          lastMessageAt: data.updated_at,
        } as WhatsAppAgent
      })
    } catch (error: any) {
      console.error('❌ Error loading WhatsApp agents:', error)
      lastError.value = error.message || 'Failed to load WhatsApp agents'
    } finally {
      isLoading.value = false
    }
  }

  // Initialize store
  async function initialize() {
    try {
      await Promise.all([
        loadHubSpotConfig(),
        loadWhatsAppAgents(),
        loadLeadStatusOptions()
      ])

      // If HubSpot is configured and enabled, fetch contacts
      if (hubspotConfig.value?.enabled) {
        await fetchHubSpotContacts()
      }

    } catch (error) {
      console.error('❌ Error initializing contacts store:', error)
    }
  }

  function clearError() {
    lastError.value = null
  }

  // Contact retrieval functions
  async function getContactByPhone(phoneNumber: string): Promise<Contact | null> {
    try {
      isLoading.value = true

      // First check if we already have the contact locally
      const existingContact = contacts.value.find(c => c.phone === phoneNumber)
      if (existingContact) {
        return existingContact
      }

      // Fetch from contact service
      const response = await hubspotService.getContactByPhone(phoneNumber)
      if (response.success && response.data) {
        const contact = response.data

        // Add to local contacts if not already present
        if (!contacts.value.find(c => c.id === contact.id)) {
          contacts.value.push(contact)
        }

        return contact
      }

      return null
    } catch (error) {
      console.error('❌ Error getting contact by phone:', error)
      lastError.value = error instanceof Error ? error.message : 'Failed to get contact'
      return null
    } finally {
      isLoading.value = false
    }
  }

  // Lead status functions
  async function loadLeadStatusOptions() {
    try {
      const response = await hubspotService.getLeadStatusOptions()
      if (response.success && response.data) {
        leadStatusOptions.value = response.data
      }
    } catch (error) {
      console.error('❌ Error loading lead status options:', error)
    }
  }

  async function updateContactLeadStatus(contactId: string, leadStatus: string) {
    try {
      isLoading.value = true
      lastError.value = null

      const userId = authStore.user?.uid
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const updateRequest = {
        contact_id: contactId,
        lead_status: leadStatus,
        user_id: userId,
        hubspot_api_key: hubspotConfig.value?.apiKey
      }

      const response = await hubspotService.updateLeadStatus(updateRequest)

      if (response.success) {
        // Update local contact data to reflect the backend change
        const contact = contacts.value.find(c => c.id === contactId)
        if (contact) {
          contact.leadStatus = leadStatus
          contact.leadStatusLabel = leadStatusOptions.value.find(opt => opt.key === leadStatus)?.label
        }

        console.log(`✅ Lead status updated for contact ${contactId}: ${leadStatus}`)
      } else {
        throw new Error(response.error?.message || 'Failed to update lead status')
      }

    } catch (error: any) {
      console.error('❌ Error updating lead status:', error)
      lastError.value = error.message || 'Failed to update lead status'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  async function getContactLeadStatusHistory(phoneNumber: string) {
    try {
      const response = await hubspotService.getLeadStatusHistory(phoneNumber)

      if (response.success && response.data) {
        leadStatusHistory.value[phoneNumber] = response.data
        return response.data
      } else {
        throw new Error(response.error?.message || 'Failed to get lead status history')
      }

    } catch (error: any) {
      console.error('❌ Error getting lead status history:', error)
      throw error
    }
  }

  function getContactsSortedByCreationDate(ascending: boolean = false) {
    return [...contacts.value].sort((a, b) => {
      const dateA = new Date(a.createdAt || a.lastActivity || '1970-01-01')
      const dateB = new Date(b.createdAt || b.lastActivity || '1970-01-01')
      return ascending ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime()
    })
  }

  function getContactsSortedByLastActivity(ascending: boolean = false) {
    return [...contacts.value].sort((a, b) => {
      const dateA = new Date(a.lastActivity || a.createdAt || '1970-01-01')
      const dateB = new Date(b.lastActivity || b.createdAt || '1970-01-01')
      return ascending ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime()
    })
  }

  function filterContactsByLeadStatus(statuses: string[]) {
    if (statuses.length === 0) return contacts.value
    return contacts.value.filter(contact =>
      contact.leadStatus && statuses.includes(contact.leadStatus)
    )
  }

  return {
    // State
    contacts,
    hubspotConfig,
    whatsappAgents,
    isLoading,
    lastError,
    leadStatusOptions,
    leadStatusHistory,

    // Computed
    hubspotContacts,
    activeWhatsAppContacts,
    getContactById,
    getWhatsAppAgentByContact,
    contactsByLeadStatus,
    contactsWithLeadStatus,
    getContactsByStatus,
    getLeadStatusLabel,

    // Actions
    saveHubSpotConfig,
    loadHubSpotConfig,
    fetchHubSpotContacts,
    testHubSpotConnection,
    activateWhatsAppAgent,
    deactivateWhatsAppAgent,
    initialize,
    clearError,

    // Contact actions
    getContactByPhone,

    // Lead status actions
    loadLeadStatusOptions,
    updateContactLeadStatus,
    getContactLeadStatusHistory,
    getContactsSortedByCreationDate,
    getContactsSortedByLastActivity,
    filterContactsByLeadStatus
  }
})
