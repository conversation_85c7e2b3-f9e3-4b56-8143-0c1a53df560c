@use 'sass:math';
@use 'sass:map';
@use 'sass:meta';
@use 'vuetify/lib/styles/tools/functions' as *;

// Custom Variables
// colors
$white: #fff !default;

// cards
$card-title-size: 18px !default;
$body-font-family: "Manrope", sans-serif !default;
$border-radius-root: 8px;
$btn-letter-spacing: 0 !default;
// Global Shadowsss
$box-shadow: 0px 2px 4px -1px #afb6c933;

// Global Radius as per breakeven point

@forward 'vuetify/settings' with (
    $color-pack: false !default,
    // Global font size and border radius
    $font-size-root: 1rem,
    $border-radius-root: $border-radius-root,
    $body-font-family: $body-font-family,
    $heading-font-family: $body-font-family !default,
    // 👉 Typography
    $typography:
        (
            //36px
            'h1': (
                'size': 2.25rem,
                'weight': 600,
                'line-height': 3.75rem,
                'font-family': inherit
            ),
            //30px
            'h2': (
                'size': 1.875rem,
                'weight': 600,
                'line-height': 2.25rem,
                'font-family': inherit
            ),
            //22px
            'h3': (
                'size': 1.375rem,
                'weight': 600,
                'line-height': 2rem,
                'font-family': inherit
            ),
            //20px
            'h4': (
                'size': 1.25rem,
                'weight': 600,
                'line-height': 1.6rem,
                'font-family': inherit
            ),
            //18px
            'h5': (
                'size': 1.125rem,
                'weight': 600,
                'line-height': 1.6rem,
                'font-family': inherit
            ),
            //16px
            'h6': (
                'size': 1rem,
                'weight': 600,
                'line-height': 1.2rem,
                'font-family': inherit
            ),
            //15px
            'subtitle-1': (
                'size': 0.94rem,
                'weight': 400,
                'line-height': 1.1rem,
                'font-family': inherit
            ),
            //13px
            'subtitle-2': (
                'size': 0.81rem,
                'weight': 400,
                'line-height': 1rem,
                'font-family': inherit
            ),
            //14px
            'body-1': (
                'size': 0.875rem,
                'weight': 400,
                'font-family': inherit,
                'letter-spacing':0rem
            ),
            //12px
            'body-2': (
                'size': 0.75rem,
                'weight': 400,
                'font-family': inherit,
                'letter-spacing':0rem
            ),
            //13px
            'button': (
                'size': 0.81rem,
                'weight': 600,
                'font-family': inherit,
                'text-transform': capitalize
            ),
            //14px
            'caption': (
                'size':0.875rem,
                'weight': 400,
                'font-family': inherit,
                'letter-spacing':0rem
            ),
            //12px
            'overline': (
                'size': 0.75rem,
                'weight': 400,
                'font-family': inherit,
                'letter-spacing':0rem
            )
        )
        !default,
    // 👉 Button
    $button-border-radius: 8px !default,
    $button-text-letter-spacing: 0 !default,
    $button-text-transform: capitalize,
    $button-elevation: (
        'default': 0,
        'hover': 4,
        'active': 8
    )
    !default,

    // 👉 Tooltip
    $tooltip-background-color: #212121 !default,
    $tooltip-text-color: rgb(var(--v-theme-on-primary)) !default,
    $tooltip-font-size: 0.75rem !default,
    $tooltip-border-radius: 4px !default,
    $tooltip-padding: 4px 8px !default,

    // 👉 Rounded
    $rounded:
        (
            0: 0,
            'sm': $border-radius-root - 4,
            null: $border-radius-root,
            'md': $border-radius-root + 4,
            'lg': $border-radius-root * 2,
            'xl': $border-radius-root + 8,
            'pill': 9999px,
            'circle': 50%
        ),

    // 👉 Card
    $card-elevation: 10 !default,
    $card-title-line-height: 1.6 !default,
    $card-text-padding: 24px !default,
    $card-item-padding: 30px 30px !default,
    $card-actions-padding: 10px 24px 24px !default,
    $card-subtitle-opacity: 1 !default,
);