.v-field--variant-outlined .v-field__outline__start.v-locale--is-ltr,
.v-locale--is-ltr .v-field--variant-outlined .v-field__outline__start {
    border-radius: $border-radius-root 0 0 $border-radius-root;
}

.v-field--variant-outlined .v-field__outline__end.v-locale--is-ltr,
.v-locale--is-ltr .v-field--variant-outlined .v-field__outline__end {
    border-radius: 0 $border-radius-root $border-radius-root 0;
}

.v-field {
    font-size: 14px !important;
    color:  rgba(var(--v-theme-textPrimary));
}

// select outlined
.v-field--variant-outlined .v-field__outline__start,
.v-field--variant-outlined .v-field__outline__notch::before,
.v-field--variant-outlined .v-field__outline__notch::after,
.v-field--variant-outlined .v-field__outline__end {
    opacity: 1;
}



.v-text-field input {
    font-size: 0.875rem;
  }
  .v-field__outline {
    color: rgb(var(--v-theme-borderColor));
    --v-field-border-opacity: 1 !important;
  }
  .input {
    .v-field--variant-outlined {
      background-color: rgba(0, 0, 0, 0.025);
    }
  }
  