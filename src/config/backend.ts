/**
 * Backend configuration for the hybrid architecture
 * - Firebase Functions: Assistant and Knowledge Base operations
 * - Microservices: Agent, Communications, Contacts, and Analytics operations
 */

export interface BackendConfig {
  [x: string]: any
  timeout?: number
  retries?: number
  microservices: Record<string, string>
}

// Microservices URLs
const microservicesConfig = {
  agentService: import.meta.env.VITE_AGENT_SERVICE_URL || 'http://localhost:8080',
  communicationsService: import.meta.env.VITE_COMMUNICATIONS_SERVICE_URL || 'http://localhost:8083',
  contactService: import.meta.env.VITE_CONTACT_SERVICE_URL || 'http://localhost:8081',
  analyticsService: import.meta.env.VITE_ANALYTICS_SERVICE_URL || 'http://localhost:8082'
}

// Default configuration
const DEFAULT_CONFIG: BackendConfig = {
  timeout: 30000,
  retries: 3,
  microservices: microservicesConfig,
}

export const backendConfig = DEFAULT_CONFIG

// Logging utility for debugging
export function logBackendConfig(): void {
  console.log('🔧 Backend Configuration:', {
    timeout: backendConfig.timeout,
    retries: backendConfig.retries,
    microservices: backendConfig.microservices
  })
}
