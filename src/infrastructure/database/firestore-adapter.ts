/**
 * Firestore implementation of the DocumentDatabase interface.
 * This adapter provides Google Cloud Firestore functionality while keeping business logic decoupled.
 */

import { Firestore, DocumentReference, CollectionReference, Query, WriteBatch, Transaction } from '@google-cloud/firestore'
import type {
  DocumentDatabase,
  DatabaseTransaction,
  DatabaseBatch,
  SubcollectionOperations,
  QueryOptions,
  FilterOperator,
  DatabaseHealth,
} from '../../core/interfaces/database'
import {
  DatabaseConnectionError,
  DatabaseTransactionError,
  DatabaseValidationError
} from '../../core/interfaces/database'

export class FirestoreAdapter implements DocumentDatabase {
  private firestore: Firestore
  private connected: boolean = false

  constructor(private config: { projectId?: string; credentials?: any; options?: any }) {
    this.firestore = new Firestore({
      projectId: config.projectId,
      keyFilename: config.credentials,
      ...config.options
    })
  }

  async connect(): Promise<void> {
    try {
      // Test connection by getting a dummy document
      await this.firestore.collection('_health_check').limit(1).get()
      this.connected = true
    } catch (error) {
      this.connected = false
      throw new DatabaseConnectionError('Failed to connect to Firestore', error as Error)
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.firestore.terminate()
      this.connected = false
    } catch (error) {
      throw new DatabaseConnectionError('Failed to disconnect from Firestore', error as Error)
    }
  }

  isConnected(): boolean {
    return this.connected
  }

  async getHealth(): Promise<DatabaseHealth> {
    try {
      const startTime = Date.now()
      await this.firestore.collection('_health_check').limit(1).get()
      const latency = Date.now() - startTime

      return {
        status: 'healthy',
        latency,
        metadata: {
          projectId: this.config.projectId,
          connected: this.connected
        }
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        error: (error as Error).message,
        metadata: {
          projectId: this.config.projectId,
          connected: this.connected
        }
      }
    }
  }

  async get<T>(collection: string, id: string): Promise<T | null> {
    try {
      const doc = await this.firestore.collection(collection).doc(id).get()
      if (!doc.exists) {
        return null
      }
      return { id: doc.id, ...doc.data() } as T
    } catch (error) {
      throw new DatabaseValidationError(`Failed to get document ${id} from ${collection}`, error as Error)
    }
  }

  async set<T>(collection: string, id: string, data: T): Promise<void> {
    try {
      await this.firestore.collection(collection).doc(id).set(data as any)
    } catch (error) {
      throw new DatabaseValidationError(`Failed to set document ${id} in ${collection}`, error as Error)
    }
  }

  async update<T>(collection: string, id: string, data: Partial<T>): Promise<void> {
    try {
      await this.firestore.collection(collection).doc(id).update(data as any)
    } catch (error) {
      throw new DatabaseValidationError(`Failed to update document ${id} in ${collection}`, error as Error)
    }
  }

  async delete(collection: string, id: string): Promise<void> {
    try {
      await this.firestore.collection(collection).doc(id).delete()
    } catch (error) {
      throw new DatabaseValidationError(`Failed to delete document ${id} from ${collection}`, error as Error)
    }
  }

  async query<T>(collection: string, options?: QueryOptions): Promise<T[]> {
    try {
      let query: Query = this.firestore.collection(collection)

      if (options?.filters) {
        for (const filter of options.filters) {
          query = query.where(filter.field, this.mapFilterOperator(filter.operator), filter.value)
        }
      }

      if (options?.orderBy) {
        for (const order of options.orderBy) {
          query = query.orderBy(order.field, order.direction)
        }
      }

      if (options?.limit) {
        query = query.limit(options.limit)
      }

      if (options?.offset) {
        query = query.offset(options.offset)
      }

      const snapshot = await query.get()
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as T))
    } catch (error) {
      throw new DatabaseValidationError(`Failed to query collection ${collection}`, error as Error)
    }
  }

  async count(collection: string, options?: QueryOptions): Promise<number> {
    try {
      let query: Query = this.firestore.collection(collection)

      if (options?.filters) {
        for (const filter of options.filters) {
          query = query.where(filter.field, this.mapFilterOperator(filter.operator), filter.value)
        }
      }

      const snapshot = await query.count().get()
      return snapshot.data().count
    } catch (error) {
      throw new DatabaseValidationError(`Failed to count documents in ${collection}`, error as Error)
    }
  }

  batch(): DatabaseBatch {
    return new FirestoreBatchAdapter(this.firestore.batch(), this.firestore)
  }

  async transaction<T>(operation: (tx: DatabaseTransaction) => Promise<T>): Promise<T> {
    try {
      return await this.firestore.runTransaction(async (firestoreTransaction) => {
        const tx = new FirestoreTransactionAdapter(firestoreTransaction, this.firestore)
        return await operation(tx)
      })
    } catch (error) {
      throw new DatabaseTransactionError('Transaction failed', error as Error)
    }
  }

  subcollection(parentCollection: string, parentId: string, subcollection: string): SubcollectionOperations {
    return new FirestoreSubcollectionAdapter(
      this.firestore.collection(parentCollection).doc(parentId).collection(subcollection)
    )
  }

  private mapFilterOperator(operator: FilterOperator): any {
    const operatorMap: Record<FilterOperator, string> = {
      'eq': '==',
      'ne': '!=',
      'gt': '>',
      'gte': '>=',
      'lt': '<',
      'lte': '<=',
      'in': 'in',
      'not-in': 'not-in',
      'contains': 'array-contains',
      'starts-with': '>=', // Requires special handling
      'ends-with': '>=', // Requires special handling
    }

    return operatorMap[operator] || '=='
  }
}

class FirestoreBatchAdapter implements DatabaseBatch {
  constructor(private batch: WriteBatch, private firestore: Firestore) {}

  set<T>(collection: string, id: string, data: T): DatabaseBatch {
    const docRef = this.firestore.collection(collection).doc(id)
    this.batch.set(docRef, data as any)
    return this
  }

  update<T>(collection: string, id: string, data: Partial<T>): DatabaseBatch {
    const docRef = this.firestore.collection(collection).doc(id)
    this.batch.update(docRef, data as any)
    return this
  }

  delete(collection: string, id: string): DatabaseBatch {
    const docRef = this.firestore.collection(collection).doc(id)
    this.batch.delete(docRef)
    return this
  }

  async commit(): Promise<void> {
    try {
      await this.batch.commit()
    } catch (error) {
      throw new DatabaseTransactionError('Batch commit failed', error as Error)
    }
  }
}

class FirestoreTransactionAdapter implements DatabaseTransaction {
  constructor(private transaction: Transaction, private firestore: Firestore) {}

  async commit(): Promise<void> {
    // Firestore transactions are automatically committed
  }

  async rollback(): Promise<void> {
    // Firestore transactions are automatically rolled back on error
    throw new Error('Transaction rollback')
  }

  async get<T>(collection: string, id: string): Promise<T | null> {
    try {
      const docRef = this.firestore.collection(collection).doc(id)
      const doc = await this.transaction.get(docRef)
      if (!doc.exists) {
        return null
      }
      return { id: doc.id, ...doc.data() } as T
    } catch (error) {
      throw new DatabaseTransactionError(`Failed to get document ${id} from ${collection}`, error as Error)
    }
  }

  async set<T>(collection: string, id: string, data: T): Promise<void> {
    try {
      const docRef = this.firestore.collection(collection).doc(id)
      this.transaction.set(docRef, data as any)
    } catch (error) {
      throw new DatabaseTransactionError(`Failed to set document ${id} in ${collection}`, error as Error)
    }
  }

  async update<T>(collection: string, id: string, data: Partial<T>): Promise<void> {
    try {
      const docRef = this.firestore.collection(collection).doc(id)
      this.transaction.update(docRef, data as any)
    } catch (error) {
      throw new DatabaseTransactionError(`Failed to update document ${id} in ${collection}`, error as Error)
    }
  }

  async delete(collection: string, id: string): Promise<void> {
    try {
      const docRef = this.firestore.collection(collection).doc(id)
      this.transaction.delete(docRef)
    } catch (error) {
      throw new DatabaseTransactionError(`Failed to delete document ${id} from ${collection}`, error as Error)
    }
  }
}

class FirestoreSubcollectionAdapter implements SubcollectionOperations {
  constructor(private collection: CollectionReference) {}

  async get<T>(id: string): Promise<T | null> {
    try {
      const doc = await this.collection.doc(id).get()
      if (!doc.exists) {
        return null
      }
      return { id: doc.id, ...doc.data() } as T
    } catch (error) {
      throw new DatabaseValidationError(`Failed to get subcollection document ${id}`, error as Error)
    }
  }

  async set<T>(id: string, data: T): Promise<void> {
    try {
      await this.collection.doc(id).set(data as any)
    } catch (error) {
      throw new DatabaseValidationError(`Failed to set subcollection document ${id}`, error as Error)
    }
  }

  async update<T>(id: string, data: Partial<T>): Promise<void> {
    try {
      await this.collection.doc(id).update(data as any)
    } catch (error) {
      throw new DatabaseValidationError(`Failed to update subcollection document ${id}`, error as Error)
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.collection.doc(id).delete()
    } catch (error) {
      throw new DatabaseValidationError(`Failed to delete subcollection document ${id}`, error as Error)
    }
  }

  async query<T>(options?: QueryOptions): Promise<T[]> {
    try {
      let query: Query = this.collection

      if (options?.filters) {
        for (const filter of options.filters) {
          const operator = this.mapFilterOperator(filter.operator)
          query = query.where(filter.field, operator, filter.value)
        }
      }

      if (options?.orderBy) {
        for (const order of options.orderBy) {
          query = query.orderBy(order.field, order.direction)
        }
      }

      if (options?.limit) {
        query = query.limit(options.limit)
      }

      const snapshot = await query.get()
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as T))
    } catch (error) {
      throw new DatabaseValidationError('Failed to query subcollection', error as Error)
    }
  }

  async add<T>(data: T): Promise<string> {
    try {
      const docRef = await this.collection.add(data as any)
      return docRef.id
    } catch (error) {
      throw new DatabaseValidationError('Failed to add document to subcollection', error as Error)
    }
  }

  private mapFilterOperator(operator: FilterOperator): any {
    const operatorMap: Record<FilterOperator, string> = {
      'eq': '==',
      'ne': '!=',
      'gt': '>',
      'gte': '>=',
      'lt': '<',
      'lte': '<=',
      'in': 'in',
      'not-in': 'not-in',
      'contains': 'array-contains',
      'starts-with': '>=',
      'ends-with': '>=',
    }

    return operatorMap[operator] || '=='
  }
}
