const AuthRoutes = {
    path: '/auth',
    component: () => import('@/layouts/blank/BlankLayout.vue'),
    meta: {
        requiresAuth: false
    },
    children: [
        {
            name: 'login',
            path: '/auth/login',
            component: () => import('@/views/auth/Login.vue')
        },
        {
            name: 'signup',
            path: '/auth/signup',
            component: () => import('@/views/auth/Signup.vue')
        },
    ]
};

export default AuthRoutes;
