import { createRouter, createWebHistory } from 'vue-router';
import { onAuthStateChanged } from 'firebase/auth';
import { auth } from '@/plugins/firebase';
import MainRoutes from './MainRoutes';
import AuthRoutes from './AuthRoutes';

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/:pathMatch(.*)*',
            component: () => import('@/views/auth/Error.vue')
        },
        MainRoutes,
        AuthRoutes
    ]
});

router.beforeEach((to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  const unsubscribe = onAuthStateChanged(auth, (user) => {
    unsubscribe();
    if (requiresAuth && !user) {
      next({ name: "login" });
    } else {
      next();
    }
  });
});

// let isAuthenticated: boolean | null = null;

// router.beforeEach(async (to, from, next) => {
//   // Espera a que Firebase determine el estado de autenticación
//   if (isAuthenticated === null) {
//     await new Promise<void>((resolve) => {
//       const unsubscribe = onAuthStateChanged(auth, (user) => {
//         isAuthenticated = !!user;
//         unsubscribe();
//         resolve();
//       });
//     });
//   }

//   // Si requiere auth y no está logueado, redirige a login
//   if (to.meta.requiresAuth && !isAuthenticated) {
//     return next('/login');
//   }

//   // Si ya está logueado e intenta ir a login/signup, redirige a home
//   if ((to.path === '/login' || to.path === '/signup') && isAuthenticated) {
//     return next('/');
//   }

//   next();
// });

export default router;
