const MainRoutes = {
  path: '/',
  meta: {
    requiresAuth: true
  },
  redirect: '/dashboard',
  component: () => import('@/layouts/full/FullLayout.vue'),
  children: [
    {
      name: 'dashboard',
      path: '/dashboard',
      meta: {
        title: 'Dashboard',
        icon: 'solar:home-2-broken'
      },
      component: () => import('@/views/pages/dashboard/index.vue'),
    },
    {
      name: 'my-assistants-layout',
      path: '/my-assistants',
      meta: {
        title: 'My Assistants',
        icon: 'solar:user-speak-broken'
      },
      component: () => import('@/views/pages/assistants/index.vue'),
      children: [
        {
          name: 'my-assistants',
          path: '',
          component: () => import('@/views/pages/assistants/dashboard.vue'),
        }
      ],
    },
    {
      name: 'marketplace',
      path: '/marketplace',
      meta: {
        title: 'Marketplace',
        icon: 'solar:shop-broken'
      },
      component: () => import('@/views/pages/marketplace/index.vue'),
    },
    {
      name: 'test-assistants',
      path: '/test-assistants',
      meta: {
        title: 'Test Assistants',
        icon: 'solar:microphone-broken'
      },
      component: () => import('@/views/pages/test-assistants/index.vue'),
    },

    {
      name: 'integrations-layout',
      path: '/integrations',
      meta: {
        title: 'Integrations',
        icon: 'solar:link-broken'
      },
      component: () => import('@/views/pages/integrations/index.vue'),
      children: [
        {
          name: 'integrations',
          path: '',
          component: () => import('@/views/pages/integrations/dashboard.vue'),
        },
        {
          name: 'integrations-hubspot',
          path: 'hubspot',
          component: () => import('@/views/pages/integrations/hubspot.vue'),
        },
      ],
    },
    {
      name: 'contacts',
      path: '/contacts',
      meta: {
        title: 'Contacts',
        icon: 'solar:users-group-rounded-broken'
      },
      component: () => import('@/views/pages/contacts/index.vue'),
    },
    {
      name: 'conversations',
      path: '/conversations',
      meta: {
        title: 'Conversations',
        icon: 'solar:chat-round-dots-broken'
      },
      component: () => import('@/views/pages/conversations/index.vue'),
    },
    {
      name: 'phone-numbers',
      path: '/phone-numbers',
      meta: {
        title: 'Phone Numbers',
        icon: 'solar:phone-broken'
      },
      component: () => import('@/views/pages/phone-numbers/index.vue'),
    },
    {
      name: 'business-data-layout',
      path: '/business-data',
      meta: {
        title: 'Business Data',
        icon: 'solar:database-broken'
      },
      component: () => import('@/views/pages/knowledge/index.vue'),
      children: [
        {
          name: 'business-data',
          path: '',
          component: () => import('@/views/pages/knowledge/dashboard.vue'),
        },
      ],
    },
    {
      name: 'upgrade-plan',
      path: '/upgrade-plan',
      meta: {
        title: 'Upgrade Plan',
        icon: 'solar:star-broken',
        chipColor: 'primary',
        chip: 'Pro'
      },
      component: () => import('@/views/pages/upgrade-plan/index.vue'),
    },
    {
      name: 'profile-settings',
      path: '/profile-settings',
      meta: {
        title: 'Profile / Settings',
        icon: 'solar:settings-broken'
      },
      component: () => import('@/views/pages/profile-settings/index.vue'),
    },
  ]
};

export default MainRoutes;
