# 🧠 Synapy Dashboard

AI-powered voice assistant management platform built with Vue 3, TypeScript, and Firebase.

## 🚀 Quick Start

```bash
# Clone and install
git clone https://github.com/synapse-sea/synapy-dashboard.git
cd synapy-dashboard
npm install

# Configure environment
cp .env.example .env
# Add your Firebase credentials

# Run locally
npm run dev
```

## 🔧 Environment Variables

### Firebase Configuration
```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_PROJECT_ID=synapy
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### Microservices Configuration
```env
# Agent Service - AI agent management
VITE_AGENT_SERVICE_URL=http://localhost:8080

# Communications Service - WhatsApp/Twilio
VITE_COMMUNICATIONS_SERVICE_URL=http://localhost:8083

# Contact Service - HubSpot integration
VITE_CONTACT_SERVICE_URL=http://localhost:8081

# Analytics Service - Conversation analysis
VITE_ANALYTICS_SERVICE_URL=http://localhost:8082
```

## 🏗️ Platform Architecture

Synapy is built on a modern **event-driven microservices architecture** designed for scalability, reliability, and cost optimization. The platform enables real-time AI-powered conversations through WhatsApp while maintaining robust integrations with CRM systems.

### 🎯 Architecture Highlights

- **🔄 Event-Driven**: Asynchronous communication via Google Pub/Sub
- **🚀 Cloud-Native**: Auto-scaling microservices on Google Cloud Run
- **💰 Cost-Optimized**: Pay-per-use model with intelligent scaling
- **🔒 Multi-Tenant**: User-specific data isolation and configurations
- **🤖 AI-Powered**: Real-time conversation analysis and lead scoring

### 🏢 System Components

#### Frontend Layer
- **Web Application**: Vue 3 + TypeScript + Vuetify
- **Authentication**: Firebase Auth with multi-provider support
- **Hosting**: Firebase Hosting with global CDN
- **State Management**: Pinia for reactive state management

#### Core Microservices
- **🧠 Agent Service** (Critical): AI conversation processing and agent management
- **💬 Communications Service**: WhatsApp/Twilio integration and conversation management
- **👥 Contact Service**: HubSpot integration and contact management with user isolation
- **📊 Analytics Service**: AI-powered conversation analysis and lead scoring
- **🔔 Notification Service**: Multi-channel notifications and alerts

#### Data & Infrastructure
- **Database**: Firestore with user-specific collections
- **Message Broker**: Google Pub/Sub for event-driven communication
- **Storage**: Firebase Storage + Cloud Storage for assets and reports
- **Monitoring**: Cloud Monitoring, Logging, and Tracing

### 🔄 Key Data Flows

#### Real-Time Message Processing
```
WhatsApp → Twilio → Communications Service → Agent Service (AI) → Response → WhatsApp
                                    ↓
                            Analytics Service (async analysis)
                                    ↓
                            Lead Status Updates → HubSpot
```

#### User-Specific Data Architecture
```
users/{userId}/
├── integrations/hubspot/     # User-specific HubSpot configuration
├── contacts/{phone}/         # User-specific contact data
└── conversations/{id}/       # User conversation history
```

### 📚 Detailed Documentation

For comprehensive technical details, implementation guides, and visual diagrams:

- **[📋 Complete Platform Architecture](./docs/PLATFORM_ARCHITECTURE.md)** - Detailed system design, components, and data flows
- **[📊 Architecture Diagrams](./docs/ARCHITECTURE_DIAGRAMS.md)** - Visual representations with Mermaid diagrams
- **[🔌 API Documentation](./docs/API_DOCUMENTATION.md)** - Complete API reference for all microservices

### 🚀 Migration Strategy

The platform is transitioning from Firebase Functions to microservices:
- **✅ Phase 1**: WhatsApp and HubSpot operations (Complete)
- **🔄 Phase 2**: Assistant and Knowledge Base operations (In Progress)
- **📅 Phase 3**: Complete Firebase Functions deprecation (Planned)

## 📱 Integrations

### HubSpot CRM
- Contact management via Contact Service microservice
- Lead status tracking and automation
- Real-time synchronization

### WhatsApp Business
- AI-powered conversations via Communications Service
- Agent activation/deactivation
- Real-time message processing

## 🚀 Deployment

**Automatic**: Push to `main` branch triggers GitHub Actions deployment

**Manual**:
```bash
# Frontend
npm run build
firebase deploy --only hosting

# Functions
cd functions
firebase deploy --only functions
```

[Detailed Instructions](./docs/DEPLOYMENT_INSTRUCTIONS.md)

## 🧪 Development

```bash
# Start frontend
npm run dev

# Start Firebase emulators
firebase emulators:start

# Run tests
npm run test

# Lint & format
npm run lint
npm run format
```

## 📂 Project Structure

```
├── src/
│   ├── components/     # Vue components
│   ├── views/         # Page components
│   ├── store/         # Pinia stores
│   ├── services/      # API services
│   └── types/         # TypeScript types
├── functions/         # Firebase Functions
├── docs/             # Documentation
└── dist/             # Build output
```

## 🔐 Required APIs

Enable these Google Cloud APIs:
- Firebase Management API
- Cloud Functions API
- Firestore API
- Cloud Build API
- IAM API

```bash
gcloud services enable firebase.googleapis.com cloudfunctions.googleapis.com firestore.googleapis.com cloudbuild.googleapis.com iam.googleapis.com --project=synapy
```

## 🛠️ Troubleshooting

**Function deployment errors**: Check service account permissions
**CORS issues**: Verify Firebase Functions proxy configuration
**Authentication errors**: Check Firebase Auth settings

## 📚 Documentation

### 🏗️ Architecture & Design
- **[Platform Architecture](./docs/PLATFORM_ARCHITECTURE.md)** - Complete system architecture and design principles
- **[Architecture Diagrams](./docs/ARCHITECTURE_DIAGRAMS.md)** - Visual system representations with Mermaid diagrams
- **[API Documentation](./docs/API_DOCUMENTATION.md)** - Complete API reference for all microservices

### 🔧 Setup & Integration
- [HubSpot Integration](./docs/HUBSPOT_INTEGRATION.md) - CRM integration setup and configuration
- [WhatsApp Setup](./docs/WHATSAPP_SETUP.md) - WhatsApp Business API configuration
- [Deployment Guide](./docs/DEPLOYMENT_INSTRUCTIONS.md) - Production deployment instructions

## 🧠 Author

**Juan Carlos González Cabrero**
[Synapse Sea](https://synapsesea.com)
