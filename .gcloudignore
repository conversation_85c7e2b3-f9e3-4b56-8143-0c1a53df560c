# This file specifies files that are *not* uploaded to Google Cloud
# when using gcloud builds submit

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.pyc

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git files
.git/
.gitignore

# Logs and temporary files
*.log
logs/
*.tmp

# Test files and coverage
.pytest_cache/
.coverage
htmlcov/
.tox/

# Documentation
docs/
*.md
!README.md

# Local development files
local_setup/.env
local_setup/ngrok-config.yml

# Agent data (large files)
agent_data/

# Build artifacts
build/
dist/
*.egg-info/

# Don't exclude these important directories for production build
!cloud_deployment/
!local_setup/presets/
!bolna/
