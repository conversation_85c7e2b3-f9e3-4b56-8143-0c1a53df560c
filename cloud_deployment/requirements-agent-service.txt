# Agent Service Requirements
# Core AI agent management and conversation processing

# Core web framework dependencies
fastapi==0.108.0
uvicorn==0.22.0
python-dotenv==1.0.0
requests==2.31.0
pydantic==2.5.3
websockets==15.0.1
aiohttp==3.9.1
aiofiles==23.2.1
python-dateutil==2.8.2
python-multipart==0.0.6
daily-python==0.9.1

# AI/LLM dependencies (core functionality)
litellm==1.65.0
openai==1.66.1
tiktoken>=0.6.0
numpy>=1.24.0,<2.0.0

# Audio processing (essential for voice agents)
pydub==0.25.1
torchaudio==2.0.1
scipy==1.11.4
azure-cognitiveservices-speech==1.38.0

# Vector database and semantic routing (for RAG agents)
llama_index==0.10.65
llama-index-vector-stores-lancedb==0.1.7
lancedb>=0.12.0
semantic-router>=0.0.40
fastembed>=0.3.0

# Text processing
nltk>=3.8.2
sentence-transformers==3.0.1

# Database and storage
pymongo==4.8.0
google-cloud-firestore==2.13.1
google-cloud-storage==2.10.0
google-auth==2.25.2
aiobotocore==2.9.0

# Performance optimization
uvloop==0.19.0

# Communications API
twilio==8.9.0
plivo==4.47.0
