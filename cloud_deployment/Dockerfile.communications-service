# Communications Service Dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better layer caching
COPY cloud_deployment/requirements-communications-service.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY cloud_deployment/services/ /app/services/

# Expose port
EXPOSE 8080

# Start application
CMD ["uvicorn", "services.communications_service:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1", "--log-level", "info"]
