# Production Environment Configuration Template
# Copy this file to production.env and fill in your actual values
#
# Usage: cp config/production.env.template config/production.env
# Then edit config/production.env with your API keys and settings

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Google Cloud Configuration (REQUIRED)
GOOGLE_CLOUD_PROJECT=
FIRESTORE_DATABASE=(default)

# Storage Configuration
STORAGE_BACKEND=auto
BUCKET_NAME_PROMPTS=
BUCKET_NAME_RECORDINGS=

# OpenAI Configuration (REQUIRED for AI conversations)
OPENAI_API_KEY=

# Communications Provider Configuration (REQUIRED for messaging)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
TWILIO_WHATSAPP_NUMBER=

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Additional AI Services (Optional but recommended)
DEEPGRAM_AUTH_TOKEN=your-deepgram-token      # For speech-to-text
ELEVENLABS_API_KEY=your-elevenlabs-key       # For text-to-speech
ANTHROPIC_API_KEY=your-anthropic-api-key     # Alternative LLM provider

# Microservices Configuration
DATABASE_TYPE=firestore
LOG_LEVEL=info
EXTRACTION_PROMPT_GENERATION_MODEL=gpt-3.5-turbo
WHISPER_URL=https://api.openai.com/v1/audio/transcriptions
AMBIENT_NOISE_PRESETS_DIR=presets/ambient_noise

# Service URLs (Auto-populated during deployment - do not edit manually)
AGENT_SERVICE_URL=
COMMUNICATIONS_SERVICE_URL=
CONTACT_SERVICE_URL=
ANALYTICS_SERVICE_URL=

# Database Collections
AGENTS_COLLECTION=
CONVERSATIONS_COLLECTION=
ACTIVE_AGENTS_COLLECTION=
LEAD_STATUS_HISTORY_COLLECTION=

# Optional: Service Configuration
# ENABLE_CACHING=true
# ENABLE_ANALYTICS=true

# =============================================================================
# INSTRUCTIONS
# =============================================================================
#
# 1. Copy this file: cp config/production.env.template config/production.env
# 2. Edit config/production.env with your actual API keys and values
# 3. Required fields must be filled in for deployment to work
# 4. Optional fields can be left commented out or empty
# 5. Never commit config/production.env to version control (it contains secrets)
#
# Get API keys from:
# - Google Cloud: https://console.cloud.google.com/
# - OpenAI: https://platform.openai.com/api-keys
# - Twilio: https://console.twilio.com/
# - HubSpot: https://app.hubspot.com/settings/integrations/api-key
# - Deepgram: https://console.deepgram.com/
# - ElevenLabs: https://elevenlabs.io/app/settings/api-keys
