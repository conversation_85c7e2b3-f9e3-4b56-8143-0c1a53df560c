# Communications Service Requirements
# WhatsApp/Twilio integration and message routing

# Core web framework dependencies
fastapi==0.108.0
uvicorn==0.22.0
python-dotenv==1.0.0
requests==2.31.0
pydantic==2.5.3
websockets==15.0.1
python-multipart==0.0.6
python-dateutil==2.8.2

# Telephony providers
twilio==8.9.0
plivo==4.47.0

# Database and storage (for message persistence)
google-cloud-firestore==2.13.1
google-cloud-storage==2.10.0
google-auth==2.25.2

# Performance optimization
uvloop==0.19.0

# Pub/Sub for event-driven architecture
google-cloud-pubsub==2.18.4
