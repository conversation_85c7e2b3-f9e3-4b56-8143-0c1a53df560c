#!/bin/bash

# Google Cloud IAM Setup Script for Synapy Microservices
# Configures proper permissions and access policies for Cloud Run services
# Following official documentation: https://cloud.google.com/functions/docs/securing/managing-access-iam

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions with timestamps
log_info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"; }
log_error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"; }

# Default values
PROJECT_ID=""
REGION="us-central1"
SERVICE=""
VERBOSE=false

# Function to show usage
show_usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --project <id>      Google Cloud Project ID (required)"
  echo "  --service <name>    Service to configure: agent-service, communications-service, contact-service, analytics-service, or all (optional)"
  echo "  --region <region>   Google Cloud Region (default: us-central1)"
  echo "  --verbose           Enable verbose logging"
  echo "  --help              Show this help message"
  echo ""
  echo "Description:"
  echo "  Sets up IAM permissions for Synapy microservices to allow public access."
  echo "  This script configures the necessary permissions for unauthenticated access"
  echo "  to the health endpoints and service APIs."
  echo "  If no service is specified, all services will be configured."
  echo ""
  echo "Examples:"
  echo "  $0 --project my-project-id --service communications-service"
  echo "  $0 --project my-project-id --service all"
  echo "  $0 --project my-project-id --region europe-west1 --verbose"
}

# Function to parse command line arguments
parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --project)
        PROJECT_ID="$2"
        shift 2
        ;;
      --service)
        SERVICE="$2"
        shift 2
        ;;
      --region)
        REGION="$2"
        shift 2
        ;;
      --verbose)
        VERBOSE=true
        shift
        ;;
      --help|-h)
        show_usage
        exit 0
        ;;
      *)
        log_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
  done

  # Validate required parameters
  if [[ -z "$PROJECT_ID" ]]; then
    log_error "Project ID is required. Use --project <project-id>"
    show_usage
    exit 1
  fi

  # Validate service name if provided
  if [[ -n "$SERVICE" ]]; then
    case "$SERVICE" in
      "agent-service"|"communications-service"|"contact-service"|"analytics-service"|"all") ;;
      *)
        log_error "Invalid service: $SERVICE. Must be: agent-service, communications-service, contact-service, analytics-service, or all"
        show_usage
        exit 1
        ;;
    esac
  fi
}

# Function to check prerequisites
check_prerequisites() {
  log_info "Checking prerequisites for IAM setup..."

  # Check gcloud CLI
  if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI not found. Install from: https://cloud.google.com/sdk/docs/install"
    exit 1
  fi

  # Check authentication
  if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Not authenticated with gcloud. Run: gcloud auth login"
    exit 1
  fi

  # Verify project access
  if ! gcloud projects describe "$PROJECT_ID" &>/dev/null; then
    log_error "Cannot access project '$PROJECT_ID'. Check project ID and permissions."
    exit 1
  fi

  # Check required APIs are enabled
  local required_apis=("run.googleapis.com" "cloudbuild.googleapis.com" "iam.googleapis.com" "firestore.googleapis.com")
  for api in "${required_apis[@]}"; do
    if ! gcloud services list --enabled --project="$PROJECT_ID" --filter="name:$api" --format="value(name)" | grep -q "$api"; then
      log_warning "API $api is not enabled. Enabling now..."
      gcloud services enable "$api" --project="$PROJECT_ID"
      log_success "Enabled API: $api"
    else
      [[ "$VERBOSE" == "true" ]] && log_info "API already enabled: $api"
    fi
  done

  log_success "Prerequisites check completed"
}

# Function to check if current user has IAM admin permissions
check_iam_permissions() {
  log_info "Checking IAM permissions..."

  # Test if we can list IAM policies (basic permission check)
  if gcloud projects get-iam-policy "$PROJECT_ID" --format="value(bindings[0].role)" &>/dev/null; then
    log_info "IAM permissions verified"
    return 0
  else
    log_warning "Limited IAM permissions detected"
    return 1
  fi
}

# Function to setup Firestore permissions for a service
setup_firestore_permissions() {
  local service_name="$1"

  log_info "Setting up Firestore permissions for $service_name..."

  # Check if we have IAM permissions first
  if ! check_iam_permissions; then
    log_warning "Insufficient IAM permissions to configure Firestore roles"
    log_info "💡 Firestore permissions may need to be configured manually by a project owner"
    log_info "💡 The services use the default compute service account which should already have necessary permissions"
    log_info "💡 If you encounter database access issues, ensure the compute service account has:"
    log_info "   - Cloud Datastore User (roles/datastore.user)"
    log_info "   - Firebase Admin SDK Administrator Service Agent (roles/firebase.adminsdk.serviceAgent)"
    return 0  # Don't fail deployment for this
  fi

  # Get the service account used by the Cloud Run service
  local service_account
  service_account=$(gcloud run services describe "$service_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --format="value(spec.template.spec.serviceAccountName)" 2>/dev/null)

  # If no custom service account is set, use the default Compute Engine service account
  if [[ -z "$service_account" ]]; then
    local project_number
    project_number=$(gcloud projects describe "$PROJECT_ID" --format="value(projectNumber)")
    service_account="$<EMAIL>"
    log_info "Using default Compute Engine service account: $service_account"
  else
    log_info "Using custom service account: $service_account"
  fi

  # Check if service account already has necessary permissions
  local has_datastore_permission=false

  # Check existing IAM policy for the service account
  if gcloud projects get-iam-policy "$PROJECT_ID" --flatten="bindings[].members" --format="table(bindings.role)" --filter="bindings.members:serviceAccount:$service_account" 2>/dev/null | grep -q "roles/datastore.user"; then
    has_datastore_permission=true
    log_info "✅ Service account already has datastore.user role"
  fi

  # Try to add the datastore role if missing
  local roles_to_add=()
  if [[ "$has_datastore_permission" == "false" ]]; then
    roles_to_add+=("roles/datastore.user")
  fi

  if [[ ${#roles_to_add[@]} -eq 0 ]]; then
    log_success "✅ Required Firestore permissions are already configured"
    return 0
  fi

  # Add missing Firestore permissions to the service account
  local failed_roles=()
  for role in "${roles_to_add[@]}"; do
    log_info "Adding role $role to service account $service_account..."

    if gcloud projects add-iam-policy-binding "$PROJECT_ID" \
      --member="serviceAccount:$service_account" \
      --role="$role" \
      --quiet 2>/dev/null; then
      log_success "Successfully added role $role"
    else
      log_warning "Failed to add role $role (may already exist or insufficient permissions)"
      failed_roles+=("$role")
    fi
  done

  # Report on failed roles
  if [[ ${#failed_roles[@]} -gt 0 ]]; then
    log_warning "⚠️  Some Firestore roles could not be configured: ${failed_roles[*]}"
    log_info "💡 These roles may need to be configured manually by a project owner"
    log_info "💡 Service account: $service_account"
    log_info "💡 Required roles: ${failed_roles[*]}"
    log_info "💡 The services should still work if the compute service account has default permissions"
  else
    log_success "✅ All required Firestore permissions configured successfully"
  fi

  # Enable Firestore API if not already enabled
  if ! gcloud services list --enabled --project="$PROJECT_ID" --filter="name:firestore.googleapis.com" --format="value(name)" | grep -q "firestore.googleapis.com"; then
    log_info "Enabling Firestore API..."
    if gcloud services enable firestore.googleapis.com --project="$PROJECT_ID" 2>/dev/null; then
      log_success "Enabled Firestore API"
    else
      log_warning "Failed to enable Firestore API (may already be enabled or insufficient permissions)"
    fi
  else
    [[ "$VERBOSE" == "true" ]] && log_info "Firestore API already enabled"
  fi

  log_success "Firestore permissions setup completed for $service_name"
}

# Function to setup IAM permissions for a service
setup_service_iam() {
  local service_name="$1"

  log_info "Setting up IAM permissions for service: $service_name"

  # Check if service exists
  if ! gcloud run services describe "$service_name" --project="$PROJECT_ID" --region="$REGION" &>/dev/null; then
    log_warning "Service '$service_name' not found in region '$REGION'. Skipping IAM setup."
    return 0
  fi

  # Check if service already has public access
  if gcloud run services get-iam-policy "$service_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --format="value(bindings[].members[])" 2>/dev/null | grep -q "allUsers"; then
    log_info "Service $service_name already has public access configured"
  else
    # Add public access permission (allUsers invoker role)
    log_info "Adding public access permission for $service_name..."

    if gcloud run services add-iam-policy-binding "$service_name" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --member="allUsers" \
      --role="roles/run.invoker" \
      --quiet 2>/dev/null; then
      log_success "Successfully configured public access for $service_name"
    else
      log_warning "Failed to configure public access for $service_name"
      log_info "💡 This may need to be configured manually or the service may already be public"
      # Don't fail deployment for this - service might still work
    fi
  fi

  # Configure Firestore permissions for services that need database access
  case "$service_name" in
    "agent-service"|"communications-service"|"contact-service")
      setup_firestore_permissions "$service_name"
      ;;
  esac

  # Verify the policy was applied
  if [[ "$VERBOSE" == "true" ]]; then
    log_info "Verifying IAM policy for $service_name..."
    if gcloud run services get-iam-policy "$service_name" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --format="table(bindings.role,bindings.members.flatten())" 2>/dev/null; then
      log_info "IAM policy verification completed"
    else
      log_warning "Could not verify IAM policy (insufficient permissions)"
    fi
  fi

  return 0  # Always return success to not block deployment
}

# Function to test service accessibility
test_service_access() {
  local service_name="$1"

  log_info "Testing public access for service: $service_name"

  # Get service URL
  local service_url
  service_url=$(gcloud run services describe "$service_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --format="value(status.url)" 2>/dev/null)

  if [[ -z "$service_url" ]]; then
    log_warning "Could not retrieve URL for service $service_name"
    return 1
  fi

  log_info "Testing health endpoint: $service_url/health"

  # Test health endpoint with timeout
  if curl -s -f --max-time 10 "$service_url/health" >/dev/null 2>&1; then
    log_success "✅ Service $service_name is publicly accessible"
    log_info "🌐 Service URL: $service_url"
  else
    log_warning "⚠️  Service $service_name may not be fully accessible yet (this can take a few minutes)"
    log_info "🌐 Service URL: $service_url"
    log_info "💡 Try testing manually: curl $service_url/health"
  fi
}

# Main function
main() {
  log_info "Starting IAM setup for Synapy microservices"

  # Parse command line arguments
  parse_args "$@"

  # Display configuration
  log_info "Configuration:"
  log_info "  Project ID: $PROJECT_ID"
  log_info "  Region: $REGION"
  log_info "  Service: ${SERVICE:-all}"
  log_info "  Verbose: $VERBOSE"

  # Check prerequisites
  check_prerequisites

  # Determine services to configure
  local services=()
  if [[ -z "$SERVICE" || "$SERVICE" == "all" ]]; then
    services=("agent-service" "communications-service" "contact-service" "analytics-service")
    log_info "Configuring IAM for all services"
  else
    services=("$SERVICE")
    log_info "Configuring IAM for specific service: $SERVICE"
  fi

  # Setup IAM for each service
  local failed_services=()
  local success_count=0

  for service in "${services[@]}"; do
    if setup_service_iam "$service"; then
      ((success_count++))
    else
      failed_services+=("$service")
    fi
  done

  # Report results (always report success since we handle errors gracefully)
  if [[ ${#services[@]} -eq 1 ]]; then
    log_success "✅ IAM setup completed for ${services[0]}"
  else
    log_success "✅ IAM setup completed for $success_count/${#services[@]} services"
  fi

  # Report any issues as warnings, not errors
  if [[ ${#failed_services[@]} -gt 0 ]]; then
    log_warning "⚠️  Some IAM configurations may need manual setup for: ${failed_services[*]}"
    log_info "💡 Services should still be functional, but may require manual IAM configuration"
  fi

  # Test service accessibility
  log_info "Testing service accessibility..."
  for service in "${services[@]}"; do
    test_service_access "$service"
  done

  log_success "🎉 IAM setup and verification completed!"
  log_info "💡 Services should be publicly accessible within a few minutes"

  # Provide manual setup instructions if needed
  if [[ ${#failed_services[@]} -gt 0 ]]; then
    echo ""
    log_info "📋 Manual IAM Setup Instructions (if needed):"
    log_info "   1. Go to Google Cloud Console → Cloud Run"
    log_info "   2. Select your service → Security tab"
    log_info "   3. Click 'Add Principal' → Enter 'allUsers'"
    log_info "   4. Select role 'Cloud Run Invoker' → Save"
    log_info "   5. For database services, also add Firestore roles to the service account"
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
