#!/bin/bash

# Docker Build Script for Synapy Microservices
# Builds Docker images for local development and testing

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUD_DEPLOYMENT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$CLOUD_DEPLOYMENT_DIR")"

# Default values
SERVICE=""
TAG="latest"
VERBOSE=false

# Function to show usage
show_usage() {
  echo "Docker Build Script for Synapy Microservices"
  echo "======================================"
  echo ""
  echo "Usage: $0 --service <service> [options]"
  echo ""
  echo "Options:"
  echo "  --service <name>    Service to build: agent-service, communications-service, contact-service, analytics-service, or all (required)"
  echo "  --tag <tag>         Docker image tag (default: latest)"
  echo "  --verbose           Enable verbose logging"
  echo "  --help              Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 --service agent-service"
  echo "  $0 --service all --tag dev"
  echo "  $0 --service communications-service --verbose"
}

# Function to parse command line arguments
parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --service)
        SERVICE="$2"
        shift 2
        ;;
      --tag)
        TAG="$2"
        shift 2
        ;;
      --verbose)
        VERBOSE=true
        shift
        ;;
      --help|-h)
        show_usage
        exit 0
        ;;
      *)
        log_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
  done

  # Validate required parameters
  if [[ -z "$SERVICE" ]]; then
    log_error "Service is required. Use --service <service-name>"
    show_usage
    exit 1
  fi

  # Validate service name
  case "$SERVICE" in
    "agent-service"|"communications-service"|"contact-service"|"analytics-service"|"all") ;;
    *)
      log_error "Invalid service: $SERVICE. Must be: agent-service, communications-service, contact-service, analytics-service, or all"
      show_usage
      exit 1
      ;;
  esac
}

# Function to build a single service
build_service() {
  local service_name="$1"

  log_info "Building Docker image for: $service_name"

  # Determine Dockerfile
  local dockerfile=""
  case "$service_name" in
    "agent-service")
      dockerfile="Dockerfile.agent-service"
      ;;
    "communications-service")
      dockerfile="Dockerfile.communications-service"
      ;;
    "contact-service")
      dockerfile="Dockerfile.contact-service"
      ;;
    "analytics-service")
      dockerfile="Dockerfile.analytics-service"
      ;;
    *)
      log_error "Unknown service configuration: $service_name"
      return 1
      ;;
  esac

  # Validate Dockerfile exists
  local dockerfile_path="$CLOUD_DEPLOYMENT_DIR/$dockerfile"
  if [[ ! -f "$dockerfile_path" ]]; then
    log_error "Dockerfile not found: $dockerfile_path"
    return 1
  fi

  # Generate image name
  local image_name="synapy-$service_name:$TAG"

  log_info "Building image: $image_name"
  log_info "Using Dockerfile: $dockerfile"
  log_info "Build context: $PROJECT_ROOT"

  # Build Docker image
  local build_args=()
  if [[ "$VERBOSE" == "true" ]]; then
    build_args+=("--progress=plain")
  fi

  if docker build \
    "${build_args[@]}" \
    -f "$dockerfile_path" \
    -t "$image_name" \
    "$PROJECT_ROOT"; then

    log_success "Successfully built image: $image_name"

    # Show image info
    local image_size=$(docker images --format "table {{.Size}}" "$image_name" | tail -n 1)
    log_info "Image size: $image_size"

    return 0
  else
    log_error "Failed to build Docker image for $service_name"
    return 1
  fi
}

# Main function
main() {
  log_info "Starting Docker build process"

  # Parse command line arguments
  parse_args "$@"

  # Display configuration
  log_info "Build Configuration:"
  log_info "  Service: $SERVICE"
  log_info "  Tag: $TAG"
  log_info "  Verbose: $VERBOSE"

  # Check Docker is available
  if ! command -v docker &> /dev/null; then
    log_error "Docker not found. Please install Docker."
    exit 1
  fi

  # Determine services to build
  local services=()
  if [[ "$SERVICE" == "all" ]]; then
    services=("agent-service" "communications-service" "contact-service" "analytics-service")
  else
    services=("$SERVICE")
  fi

  # Build services
  local failed_services=()
  for service in "${services[@]}"; do
    if ! build_service "$service"; then
      failed_services+=("$service")
    fi
  done

  # Report results
  if [[ ${#failed_services[@]} -eq 0 ]]; then
    log_success "🎉 All Docker images built successfully!"

    # List built images
    log_info "Built images:"
    for service in "${services[@]}"; do
      local image_name="synapy-$service:$TAG"
      log_info "  $image_name"
    done
  else
    log_error "❌ Build failed for services: ${failed_services[*]}"
    exit 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
