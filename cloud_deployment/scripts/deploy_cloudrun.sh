#!/bin/bash

# Cloud Run Deployment Script for Synapy Microservices
# Handles building Docker images and deploying to Google Cloud Run
# Focused on deployment operations with comprehensive logging

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Default values
PROJECT_ID=""
REGION="us-central1"
SERVICE=""
VERBOSE=false
FORCE_DEPLOY=false

# Script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUD_DEPLOYMENT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$CLOUD_DEPLOYMENT_DIR")"

# Function to show usage
show_usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --project <id>      Google Cloud Project ID (required)"
  echo "  --service <name>    Service to deploy: agent-service, communications-service, contact-service, analytics-service, or all (required)"
  echo "  --region <region>   Google Cloud Region (default: us-central1)"
  echo "  --verbose           Enable verbose logging"
  echo "  --force             Force deployment even if service is up-to-date"
  echo "  --help              Show this help message"
  echo ""
  echo "Description:"
  echo "  Builds Docker images using Cloud Build and deploys services to Cloud Run."
  echo "  Handles environment variable configuration and service settings."
  echo ""
  echo "Examples:"
  echo "  $0 --project my-project-id --service agent-service"
  echo "  $0 --project my-project-id --service all --verbose"
}

# Function to parse command line arguments
parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --project)
        PROJECT_ID="$2"
        shift 2
        ;;
      --service)
        SERVICE="$2"
        shift 2
        ;;
      --region)
        REGION="$2"
        shift 2
        ;;
      --verbose)
        VERBOSE=true
        shift
        ;;
      --force)
        FORCE_DEPLOY=true
        shift
        ;;
      --help|-h)
        show_usage
        exit 0
        ;;
      *)
        log_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
  done

  # Validate required parameters
  if [[ -z "$PROJECT_ID" ]]; then
    log_error "Project ID is required. Use --project <project-id>"
    show_usage
    exit 1
  fi

  if [[ -z "$SERVICE" ]]; then
    log_error "Service is required. Use --service <service-name>"
    show_usage
    exit 1
  fi

  # Validate service name
  case "$SERVICE" in
    "agent-service"|"communications-service"|"contact-service"|"analytics-service"|"all") ;;
    *)
      log_error "Invalid service: $SERVICE. Must be: agent-service, communications-service, contact-service, analytics-service, or all"
      show_usage
      exit 1
      ;;
  esac
}

# Function to check prerequisites
check_prerequisites() {
  log_info "Checking deployment prerequisites..."

  # Check gcloud CLI
  if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI not found. Install from: https://cloud.google.com/sdk/docs/install"
    exit 1
  fi

  # Check authentication
  if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Not authenticated with gcloud. Run: gcloud auth login"
    exit 1
  fi

  # Verify project access
  if ! gcloud projects describe "$PROJECT_ID" &>/dev/null; then
    log_error "Cannot access project '$PROJECT_ID'. Check project ID and permissions."
    exit 1
  fi

  # Check required files exist
  local env_file="$CLOUD_DEPLOYMENT_DIR/config/production.env"
  if [[ ! -f "$env_file" ]]; then
    log_error "Environment file not found: $env_file"
    log_info "💡 Copy config/production.env.template to config/production.env and configure it"
    exit 1
  fi

  # Enable required APIs
  local required_apis=("run.googleapis.com" "cloudbuild.googleapis.com")
  for api in "${required_apis[@]}"; do
    if ! gcloud services list --enabled --project="$PROJECT_ID" --filter="name:$api" --format="value(name)" | grep -q "$api"; then
      log_info "Enabling required API: $api"
      gcloud services enable "$api" --project="$PROJECT_ID"
      log_success "Enabled API: $api"
    else
      [[ "$VERBOSE" == "true" ]] && log_info "API already enabled: $api"
    fi
  done

  log_success "Prerequisites check completed"
}

# Function to load environment variables
load_env_vars() {
  local env_file="$CLOUD_DEPLOYMENT_DIR/config/production.env"
  local env_vars=""

  [[ "$VERBOSE" == "true" ]] && log_info "Loading environment variables from $env_file" >&2

  while IFS= read -r line; do
    # Skip empty lines and comments
    [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue

    # Extract key=value pairs
    if [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
      local key="${BASH_REMATCH[1]// /}"
      local value="${BASH_REMATCH[2]}"

      # Remove quotes if present
      value="${value%\"}"
      value="${value#\"}"
      value="${value%\'}"
      value="${value#\'}"

      # Skip empty values
      [[ -n "$value" ]] && env_vars="$env_vars --set-env-vars $key=$value"
    fi
  done < "$env_file"

  echo "$env_vars"
}

# Function to create cloudbuild.yaml configuration with caching
create_cloudbuild_config() {
  local service_name="$1"
  local dockerfile="$2"
  local image_name="$3"
  local output_file="$4"

  log_info "Creating optimized cloudbuild configuration for $service_name"

  # Determine machine type based on service complexity
  local machine_type="E2_HIGHCPU_8"  # Default for most services (correct format)
  local timeout="2400s"  # Default timeout (30 minutes)

  # Create cache image name for layer caching
  local cache_image="gcr.io/$PROJECT_ID/$service_name:cache"

  cat > "$output_file" << EOF
steps:
  # Pull previous image for layer caching
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        docker pull $cache_image || echo "No cache image found, building from scratch"

  # Build with cache-from for faster builds
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'cloud_deployment/$dockerfile',
      '--cache-from', '$cache_image',
      '-t', '$image_name',
      '-t', '$cache_image',
      '.'
    ]

images:
  - '$image_name'
  - '$cache_image'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: $machine_type
  diskSizeGb: 100

timeout: $timeout
EOF

  [[ "$VERBOSE" == "true" ]] && log_info "Created optimized cloudbuild config: $output_file (machine: $machine_type, timeout: $timeout)"
}

# Function to check if service exists and get its status
check_service_status() {
  local service_name="$1"

  # Check if service exists
  if gcloud run services describe "$service_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --format="value(status.conditions[0].status)" &>/dev/null; then

    # Get service status
    local status
    status=$(gcloud run services describe "$service_name" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --format="value(status.conditions[0].status)" 2>/dev/null)

    if [[ "$status" == "True" ]]; then
      return 0  # Service exists and is ready
    else
      return 1  # Service exists but not ready
    fi
  else
    return 2  # Service doesn't exist
  fi
}

# Function to get current service image
get_current_service_image() {
  local service_name="$1"

  gcloud run services describe "$service_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --format="value(spec.template.spec.containers[0].image)" 2>/dev/null
}

# Function to calculate service content hash
calculate_service_hash() {
  local service_name="$1"
  local dockerfile="$2"

  local dockerfile_path="$CLOUD_DEPLOYMENT_DIR/$dockerfile"
  local service_files=()

  # Always include the Dockerfile
  service_files+=("$dockerfile_path")

  # Include service-specific files based on service name
  case "$service_name" in
    "agent-service")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/services/agent_service.py")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/requirements-agent-service.txt")
      ;;
    "communications-service")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/services/communications_service.py")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/requirements-communications-service.txt")
      ;;
    "contact-service")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/services/contact_service.py")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/requirements-contact-service.txt")
      ;;
    "analytics-service")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/services/analytics_service.py")
      service_files+=("$CLOUD_DEPLOYMENT_DIR/requirements-analytics-service.txt")
      ;;
  esac

  # Include shared files that all services depend on
  if [[ -d "$CLOUD_DEPLOYMENT_DIR/services/shared" ]]; then
    while IFS= read -r -d '' file; do
      service_files+=("$file")
    done < <(find "$CLOUD_DEPLOYMENT_DIR/services/shared" -name "*.py" -print0 2>/dev/null)
  fi

  # Calculate combined hash of all relevant files
  local combined_hash=""
  for file in "${service_files[@]}"; do
    if [[ -f "$file" ]]; then
      local file_hash
      file_hash=$(sha256sum "$file" 2>/dev/null | cut -d' ' -f1)
      combined_hash="${combined_hash}${file_hash}"
    fi
  done

  # Return the hash of the combined hashes
  echo -n "$combined_hash" | sha256sum | cut -d' ' -f1
}

# Function to check if deployment is needed
is_deployment_needed() {
  local service_name="$1"
  local dockerfile="$2"

  # Check service status
  local service_status
  check_service_status "$service_name"
  service_status=$?

  case $service_status in
    0)
      log_info "Service $service_name exists and is ready"

      # Calculate current service content hash
      local current_hash
      current_hash=$(calculate_service_hash "$service_name" "$dockerfile")

      # Check if we have a deployment marker with the same hash
      local marker_file="$CLOUD_DEPLOYMENT_DIR/.deployment_markers/${service_name}_${current_hash}"

      if [[ -f "$marker_file" ]]; then
        local marker_age
        marker_age=$(($(date +%s) - $(stat -c %Y "$marker_file" 2>/dev/null || stat -f %m "$marker_file" 2>/dev/null || echo 0)))

        # If marker is less than 30 minutes old, skip deployment
        if [[ $marker_age -lt 1800 ]]; then
          log_info "Service $service_name is up-to-date (deployed ${marker_age}s ago)"
          return 1  # No deployment needed
        else
          log_info "Service $service_name deployment marker is old (${marker_age}s), checking for updates"
        fi
      else
        log_info "Service content has changed since last deployment, redeployment needed"
      fi

      return 0  # Deployment needed
      ;;
    1)
      log_warning "Service $service_name exists but is not ready, redeployment needed"
      return 0  # Deployment needed
      ;;
    2)
      log_info "Service $service_name does not exist, deployment needed"
      return 0  # Deployment needed
      ;;
  esac
}

# Function to create deployment marker
create_deployment_marker() {
  local service_name="$1"
  local dockerfile="$2"

  # Calculate current service content hash
  local service_hash
  service_hash=$(calculate_service_hash "$service_name" "$dockerfile")

  local marker_dir="$CLOUD_DEPLOYMENT_DIR/.deployment_markers"
  mkdir -p "$marker_dir"

  local marker_file="$marker_dir/${service_name}_${service_hash}"
  touch "$marker_file"

  # Clean up old markers for this service
  find "$marker_dir" -name "${service_name}_*" ! -name "${service_name}_${service_hash}" -delete 2>/dev/null || true

  [[ "$VERBOSE" == "true" ]] && log_info "Created deployment marker: ${service_name}_${service_hash}"
}

# Function to deploy a single service
deploy_service() {
  local service_name="$1"

  log_info "Checking deployment status for service: $service_name"

  # Service configuration
  local dockerfile=""
  local memory=""
  local cpu=""
  local max_instances=""

  case "$service_name" in
    "agent-service")
      dockerfile="Dockerfile.agent-service"
      memory="2Gi"
      cpu="1"
      max_instances="10"
      ;;
    "communications-service")
      dockerfile="Dockerfile.communications-service"
      memory="1Gi"
      cpu="1"
      max_instances="5"
      ;;
    "contact-service")
      dockerfile="Dockerfile.contact-service"
      memory="1Gi"
      cpu="1"
      max_instances="5"
      ;;
    "analytics-service")
      dockerfile="Dockerfile.analytics-service"
      memory="1.5Gi"
      cpu="1"
      max_instances="8"
      ;;
    *)
      log_error "Unknown service configuration: $service_name"
      return 1
      ;;
  esac

  # Check if deployment is needed (unless forced)
  if [[ "$FORCE_DEPLOY" == "true" ]]; then
    log_info "🔄 Force deployment requested for service: $service_name"
  elif ! is_deployment_needed "$service_name" "$dockerfile"; then
    log_success "✅ Service $service_name is already up-to-date, skipping deployment"
    return 0
  fi

  log_info "Starting deployment for service: $service_name"

  # Validate Dockerfile exists
  local dockerfile_path="$CLOUD_DEPLOYMENT_DIR/$dockerfile"
  if [[ ! -f "$dockerfile_path" ]]; then
    log_error "Dockerfile not found: $dockerfile_path"
    return 1
  fi

  # Generate image name with timestamp
  local timestamp=$(date +%s)
  local image_name="gcr.io/$PROJECT_ID/$service_name:$timestamp"

  log_info "Building $service_name image..."

  # Create cloudbuild.yaml for this service
  local cloudbuild_file="$CLOUD_DEPLOYMENT_DIR/cloudbuild-$service_name.yaml"
  create_cloudbuild_config "$service_name" "$dockerfile" "$image_name" "$cloudbuild_file"

  # Build and push image using Cloud Build
  if gcloud builds submit \
    --project="$PROJECT_ID" \
    --config="$cloudbuild_file" \
    "$PROJECT_ROOT"; then
    log_success "Successfully built and pushed image: $image_name"
    # Clean up temporary cloudbuild file
    rm -f "$cloudbuild_file"
  else
    log_error "Failed to build Docker image for $service_name"
    # Clean up temporary cloudbuild file on failure too
    rm -f "$cloudbuild_file"
    return 1
  fi

  # Load environment variables
  local env_vars
  env_vars=$(load_env_vars)

  # Deploy to Cloud Run
  log_info "Deploying $service_name to Cloud Run..."

  # Cost optimization settings based on environment
  local min_instances="0"
  local concurrency="40"
  local cpu_throttling="--cpu-throttling"

  # Adjust for production environment for better availability
  if [[ "${NODE_ENV:-production}" == "production" ]]; then
    min_instances="1"  # Eliminate cold starts in production
    concurrency="80"   # Higher concurrency for production
  fi

  if gcloud run deploy "$service_name" \
    --image="$image_name" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --platform="managed" \
    --port="8080" \
    --memory="$memory" \
    --cpu="$cpu" \
    --min-instances="$min_instances" \
    --max-instances="$max_instances" \
    --concurrency="$concurrency" \
    --timeout="300" \
    --execution-environment="gen2" \
    $cpu_throttling \
    --allow-unauthenticated \
    --set-env-vars="STARTUP_TIMEOUT=300" \
    --labels="app=$service_name,version=v2,infrastructure=abstracted" \
    $env_vars \
    --quiet; then

    # Get service URL
    local service_url
    service_url=$(gcloud run services describe "$service_name" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --format="value(status.url)" 2>/dev/null)

    # Create deployment marker to track successful deployment
    create_deployment_marker "$service_name" "$dockerfile"

    log_success "✅ Successfully deployed $service_name"
    log_info "🌐 Service URL: $service_url"

    return 0
  else
    log_error "Failed to deploy $service_name to Cloud Run"
    return 1
  fi
}

# Main function
main() {
  log_info "Starting Cloud Run deployment for Synapy microservices"

  # Parse command line arguments
  parse_args "$@"

  # Display essential configuration
  log_info "Deploying $SERVICE to $PROJECT_ID ($REGION)"

  # Check prerequisites
  check_prerequisites

  # Determine services to deploy
  local services=()
  if [[ "$SERVICE" == "all" ]]; then
    services=("agent-service" "communications-service" "contact-service" "analytics-service")
  else
    services=("$SERVICE")
  fi

  # Deploy services
  local failed_services=()
  for service in "${services[@]}"; do
    if ! deploy_service "$service"; then
      failed_services+=("$service")
    fi
  done

  # Report results
  if [[ ${#failed_services[@]} -eq 0 ]]; then
    log_success "🎉 All services deployed successfully!"
  else
    log_error "❌ Deployment failed for services: ${failed_services[*]}"
    exit 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
