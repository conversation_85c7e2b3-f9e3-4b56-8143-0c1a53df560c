#!/bin/bash

# Docker Local Run Script for Synapy Microservices
# Runs Docker containers locally for development and testing

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Simplified logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUD_DEPLOYMENT_DIR="$(dirname "$SCRIPT_DIR")"

# Default values
SERVICE=""
PORT=""
TAG="latest"
DETACHED=false
VERBOSE=false

# Function to show usage
show_usage() {
  echo "Docker Local Run Script for Synapy Microservices"
  echo "=========================================="
  echo ""
  echo "Usage: $0 --service <service> [options]"
  echo ""
  echo "Options:"
  echo "  --service <name>    Service to run: agent-service, communications-service, contact-service, or analytics-service (required)"
  echo "  --port <port>       Port to expose (default: 8080 for agent-service, 8083 for communications-service, 8081 for contact-service, 8082 for analytics-service)"
  echo "  --tag <tag>         Docker image tag (default: latest)"
  echo "  --detached          Run container in background"
  echo "  --verbose           Enable verbose logging"
  echo "  --help              Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 --service agent-service"
  echo "  $0 --service communications-service --port 8083"
  echo "  $0 --service contact-service --detached"
}

# Function to parse command line arguments
parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --service)
        SERVICE="$2"
        shift 2
        ;;
      --port)
        PORT="$2"
        shift 2
        ;;
      --tag)
        TAG="$2"
        shift 2
        ;;
      --detached)
        DETACHED=true
        shift
        ;;
      --verbose)
        VERBOSE=true
        shift
        ;;
      --help|-h)
        show_usage
        exit 0
        ;;
      *)
        log_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
  done

  # Validate required parameters
  if [[ -z "$SERVICE" ]]; then
    log_error "Service is required. Use --service <service-name>"
    show_usage
    exit 1
  fi

  # Validate service name and set default port
  case "$SERVICE" in
    "agent-service")
      PORT="${PORT:-8080}"
      ;;
    "communications-service")
      PORT="${PORT:-8083}"
      ;;
    "contact-service")
      PORT="${PORT:-8081}"
      ;;
    "analytics-service")
      PORT="${PORT:-8082}"
      ;;
    *)
      log_error "Invalid service: $SERVICE. Must be: agent-service, communications-service, contact-service, or analytics-service"
      show_usage
      exit 1
      ;;
  esac
}

# Function to check prerequisites
check_prerequisites() {
  log_info "Checking prerequisites..."

  # Check Docker is available
  if ! command -v docker &> /dev/null; then
    log_error "Docker not found. Please install Docker."
    exit 1
  fi

  # Check environment file exists
  local env_file="$CLOUD_DEPLOYMENT_DIR/config/production.env"
  if [[ ! -f "$env_file" ]]; then
    log_error "Environment configuration file not found: $env_file"
    log_info "💡 Copy config/production.env.template to config/production.env and configure it"
    exit 1
  fi

  # Check if Docker image exists
  local image_name="synapy-$SERVICE:$TAG"
  if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image_name$"; then
    log_error "Docker image not found: $image_name"
    log_info "💡 Build the image first: ./cloud_deployment/scripts/docker_build.sh --service $SERVICE"
    exit 1
  fi

  log_success "Prerequisites check completed"
}

# Function to stop existing containers
stop_existing_containers() {
  local container_name="synapy-$SERVICE"

  # Stop and remove existing container if running
  if docker ps -a --format "{{.Names}}" | grep -q "^$container_name$"; then
    log_info "Stopping existing container: $container_name"
    docker stop "$container_name" >/dev/null 2>&1 || true
    docker rm "$container_name" >/dev/null 2>&1 || true
  fi
}

# Function to run the service
run_service() {
  local service_name="$SERVICE"
  local image_name="synapy-$service_name:$TAG"
  local container_name="synapy-$service_name"

  log_info "Starting Docker container for: $service_name"
  log_info "Image: $image_name"
  log_info "Port: $PORT"
  log_info "Container name: $container_name"

  # Stop existing containers
  stop_existing_containers

  # Prepare Docker run arguments
  local docker_args=(
    "run"
    "--name" "$container_name"
    "--rm"
    "-p" "$PORT:8080"
    "--env-file" "$CLOUD_DEPLOYMENT_DIR/config/production.env"
    "-e" "PORT=$PORT"
  )

  # Add Google Cloud credentials if available (optional for local development)
  if [[ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]] && [[ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
    log_info "Mounting Google Cloud credentials from: $GOOGLE_APPLICATION_CREDENTIALS"
    # Mount the credentials file to a standard location in the container
    docker_args+=("-v" "$GOOGLE_APPLICATION_CREDENTIALS:/app/service-account-key.json:ro")
    docker_args+=("-e" "GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json")
  else
    # Check if gcloud default credentials are available
    if command -v gcloud >/dev/null 2>&1; then
      local gcloud_config_dir="$HOME/.config/gcloud"
      if [[ -d "$gcloud_config_dir" ]]; then
        log_info "Mounting gcloud default credentials for local development"
        docker_args+=("-v" "$gcloud_config_dir:/home/<USER>/.config/gcloud:ro")
        docker_args+=("-e" "CLOUDSDK_CONFIG=/home/<USER>/.config/gcloud")
      fi
    fi

    log_info "No explicit service account key found - using default credential detection"
    log_info "For local development: run 'gcloud auth application-default login'"
    log_info "For production: Cloud Run will use built-in service account"
  fi

  # Add detached mode if requested
  if [[ "$DETACHED" == "true" ]]; then
    docker_args+=("-d")
  else
    docker_args+=("-it")
  fi

  # Add the image name
  docker_args+=("$image_name")

  # Run the container
  log_info "Running command: docker ${docker_args[*]}"

  if docker "${docker_args[@]}"; then
    if [[ "$DETACHED" == "true" ]]; then
      log_success "Container started in background"
      log_info "🌐 Service URL: http://localhost:$PORT"
      log_info "📋 Container name: $container_name"
      log_info "🔍 View logs: docker logs -f $container_name"
      log_info "🛑 Stop container: docker stop $container_name"
    else
      log_info "Container stopped"
    fi
    return 0
  else
    log_error "Failed to run Docker container for $service_name"
    return 1
  fi
}

# Function to show service status
show_status() {
  local container_name="synapy-$SERVICE"

  if docker ps --format "{{.Names}}" | grep -q "^$container_name$"; then
    log_success "Service $SERVICE is running"
    log_info "🌐 Service URL: http://localhost:$PORT"

    # Show container info
    docker ps --filter "name=$container_name" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
  else
    log_info "Service $SERVICE is not running"
  fi
}

# Main function
main() {
  log_info "Starting Docker local run process"

  # Parse command line arguments
  parse_args "$@"

  # Display configuration
  log_info "Run Configuration:"
  log_info "  Service: $SERVICE"
  log_info "  Port: $PORT"
  log_info "  Tag: $TAG"
  log_info "  Detached: $DETACHED"
  log_info "  Verbose: $VERBOSE"

  # Check prerequisites
  check_prerequisites

  # Run the service
  if run_service; then
    # Show final status
    if [[ "$DETACHED" == "true" ]]; then
      show_status
    fi
  else
    exit 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
