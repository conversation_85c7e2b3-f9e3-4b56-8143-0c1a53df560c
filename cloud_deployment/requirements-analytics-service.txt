# Analytics Service Requirements
# AI-powered conversation analysis and lead status detection

# Core web framework dependencies
fastapi==0.108.0
uvicorn==0.22.0
python-dotenv==1.0.0
requests==2.31.0
pydantic==2.5.3
python-multipart==0.0.6
python-dateutil==2.8.2

# AI/LLM dependencies for analysis
litellm==1.65.0
openai==1.66.1
tiktoken>=0.6.0
numpy>=1.24.0,<2.0.0

# Text processing and NLP
nltk>=3.8.2
sentence-transformers==3.0.1

# Database and storage
google-cloud-firestore==2.13.1
google-auth==2.25.2

# Performance optimization
uvloop==0.19.0

# Pub/Sub for event-driven architecture
google-cloud-pubsub==2.18.4

# Data analysis and processing
pandas==2.1.4
scikit-learn==1.3.2
