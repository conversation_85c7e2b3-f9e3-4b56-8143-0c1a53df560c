# Agents Service Dockerfile
# Stage 1: Build dependencies
FROM python:3.10-slim AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && \
  apt-get install -y --no-install-recommends \
  build-essential \
  gcc \
  python3-dev \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Upgrade pip and install build tools
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Copy requirements first for better layer caching
COPY cloud_deployment/requirements-agent-service.txt /app/requirements.txt

# Install Python dependencies to a local directory
RUN pip install --no-cache-dir --user -r requirements.txt

# Stage 2: Runtime image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install only runtime dependencies
RUN apt-get update && \
  apt-get install -y --no-install-recommends \
  curl \
  ffmpeg \
  libsndfile1 \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder stage
COPY --from=builder /root/.local /root/.local

# Make sure scripts in .local are usable
ENV PATH=/root/.local/bin:$PATH

# Install uvicorn separately for better caching
RUN pip install --no-cache-dir uvicorn

# Copy and install local bolna package without dependencies (dependencies already installed)
COPY bolna /app/bolna
COPY pyproject.toml /app/pyproject.toml
RUN pip install --no-cache-dir --no-deps -e .

# Copy application files
COPY cloud_deployment/services/ /app/services/
COPY cloud_deployment/presets /app/presets

    # Expose port
EXPOSE 8080

# Start application
CMD ["uvicorn", "services.agent_service:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1", "--log-level", "info"]
