# 🚀 **AI Virtual Assistant Platform - Cloud Deployment**

## 📋 **Overview**

This directory contains the event-driven microservices architecture for the AI Virtual Assistant Platform. The system is designed with **technology-agnostic abstractions**, **cost optimization**, and **horizontal scalability** as core principles.

## Architecture Overview

The platform follows an **event-driven microservices pattern** that ensures decoupling, scalability, and cost optimization through:

- **Microservices Core**: Independent, specialized services
- **Message Broker**: Google Pub/Sub for asynchronous communication
- **Database Abstraction**: Technology-agnostic data layer
- **Communication Abstraction**: Provider-agnostic messaging interface

## 🏗️ **Microservices Architecture**

### **Service Overview**

| Service | Port | Purpose | Resources | Scaling |
|---------|------|---------|-----------|---------|
| **agent-service** | 8080 | AI agent management & conversation processing | 2Gi memory, 1 CPU | Min 1, Max 10 |
| **communications-service** | 8083 | WhatsApp/Twilio integration (swappable) | 1Gi memory, 1 CPU | Min 0, Max 5 |
| **contact-service** | 8081 | HubSpot integration & contact management | 1Gi memory, 1 CPU | Min 0, Max 5 |
| **analytics-service** | 8082 | AI-powered conversation analysis | 1.5Gi memory, 1 CPU | Min 0, Max 8 |

### **Event-Driven Communication**

The services communicate through **Google Pub/Sub** topics:

```
message.received      → Analytics Service
message.sent          → Analytics Service
agent.activated       → Analytics Service
agent.deactivated     → Notification Service
lead.status.updated   → Contact Service, Notification Service
conversation.ended    → Analytics Service
```

### **Data Flow Example**

```
1. WhatsApp → Twilio → Communications Service webhook
2. Communications Service → Stores message → Publishes message.received event
3. Communications Service → Calls Agent Service webhook
4. Agent Service → Processes with AI → Returns response
5. Communications Service → Sends response via Twilio
6. Analytics Service → Processes message (async via Pub/Sub)
7. Analytics Service → Updates lead status if needed
8. Contact Service → Syncs with HubSpot
```

### **Technology Abstractions**

#### **Database Layer**
```python
# Technology-agnostic database interface
database = get_database()  # Currently Firestore, easily swappable
await database.get_document("contacts", contact_id)
await database.set_document("assistants", agent_id, data)
```

#### **Message Broker Layer**
```python
# Technology-agnostic message broker
message_broker = get_message_broker()  # Currently Pub/Sub, easily swappable
await message_broker.publish("message-events", event_data)
```

#### **Communication Provider Layer**
```python
# Technology-agnostic communication interface
comm_provider = TwilioProvider()  # Easily swappable with other providers
await comm_provider.send_message(to=phone, message=text)
```

## Quick Start

### Prerequisites

**For Local Development:**
- Docker installed and running
- `config/production.env` configured with your API keys
- Google Cloud authentication (see Google Cloud Setup below)

**For Cloud Deployment:**
- Google Cloud SDK installed and authenticated
- `config/production.env` configured with your API keys
- Google Cloud project with billing enabled

### Local Development Workflow

```bash
# 1. Configure environment
cp config/production.env.template config/production.env
# Edit production.env with your API keys

# 2. Build Docker images
./deploy.sh build all

# 3. Run services locally
./deploy.sh local agent-service                    # Run on port 8080
./deploy.sh local communications-service --port 8083  # Run on port 8083
./deploy.sh local contact-service --port 8081         # Run on port 8081
./deploy.sh local analytics-service --port 8082       # Run on port 8082

# 4. Test your services
curl http://localhost:8080/health  # Agent Service
curl http://localhost:8081/health  # Contact Service
curl http://localhost:8082/health  # Analytics Service
curl http://localhost:8083/health  # Communications Service

# Or use the automated health check script
python cloud_deployment/test_health_endpoints.py
```

### Cloud Deployment

```bash
# Deploy to Google Cloud Run
./deploy.sh deploy all --project your-project-id
```

## Commands Reference

### Build Commands
```bash
./deploy.sh build agent-service         # Build Agent service image
./deploy.sh build communications-service # Build Communications service image
./deploy.sh build contact-service       # Build Contact service image
./deploy.sh build analytics-service     # Build Analytics service image
./deploy.sh build all                   # Build all service images
```

### Local Development Commands
```bash
./deploy.sh local agent-service                    # Run Agent service (port 8080)
./deploy.sh local communications-service           # Run Communications service (port 8083)
./deploy.sh local contact-service                  # Run Contact service (port 8081)
./deploy.sh local analytics-service                # Run Analytics service (port 8082)
./deploy.sh local agent-service --port 8090        # Run on custom port
./deploy.sh local agent-service --detached         # Run in background
```

### Cloud Deployment Commands
```bash
./deploy.sh deploy agent-service --project my-project      # Deploy single service
./deploy.sh deploy all --project my-project                # Deploy all services
./deploy.sh deploy all --project my-project --verbose      # Deploy with debug logs
```

## Google Cloud Setup

### Firestore Database Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Enable Firestore API: Go to APIs & Services → Enable APIs → Search "Firestore" → Enable
4. Create Firestore database: Go to Firestore → Create database → Start in production mode → Choose region

### Authentication Setup
**Option 1: For Local Development (Recommended)**
```bash
# Install Google Cloud SDK, then authenticate
gcloud auth application-default login
```

**Option 2: Service Account (For Docker/Local Development)**
1. Go to IAM & Admin → Service Accounts → Create Service Account
2. Grant "Firestore User" role
3. Create key → Download JSON file
4. **Save the key file as `cloud_deployment/config/service-account-key.json`**
5. Or set environment variable: `export GOOGLE_APPLICATION_CREDENTIALS="/path/to/key.json"`

**Note**: For Docker deployment, place your service account key at `cloud_deployment/config/service-account-key.json` or set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable to point to your key file.

## Configuration

### Environment Variables

Edit `config/production.env` with your API keys:

```bash
# Core Infrastructure
GOOGLE_CLOUD_PROJECT=your-project-id
FIRESTORE_DATABASE=(default)
DATABASE_TYPE=firestore

# AI/LLM Configuration
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
EXTRACTION_PROMPT_GENERATION_MODEL=gpt-3.5-turbo

# Communications Provider (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# HubSpot Integration
HUBSPOT_API_KEY=your-hubspot-api-key

# Optional Services
DEEPGRAM_AUTH_TOKEN=your-deepgram-token
ELEVENLABS_API_KEY=your-elevenlabs-key

# Service Configuration
PORT=8080  # Default port (overridden per service)
LOG_LEVEL=info
```

### Service Ports

- **Agent Service**: Default port 8080
- **Communications Service**: Default port 8083
- **Contact Service**: Default port 8081
- **Analytics Service**: Default port 8082
- **Custom ports**: Use `--port` flag

## API Usage Examples

### Agent Service (AI Conversations)

```bash
# Health check
curl http://localhost:8080/health

# Create agent
curl -X POST http://localhost:8080/agent \
  -H "Content-Type: application/json" \
  -d '{
    "agent_config": {
      "agent_name": "Test Agent",
      "agent_type": "conversation",
      "tasks": []
    }
  }'

# List agents
curl http://localhost:8080/agents
```

### Twilio Service (Phone Integration)

```bash
# Health check
curl http://localhost:8081/health

# Webhook endpoint (used by Twilio)
# POST http://localhost:8081/webhook
```

## Architecture

### File Structure
```
cloud_deployment/
├── deploy.sh                          # Master deployment script
├── README.md                          # This file
├── requirements-agent-service.txt     # Python dependencies for Agent Service
├── requirements-communications.txt    # Python dependencies for Communications Service
├── requirements-contact.txt           # Python dependencies for Contact Service
├── requirements-analytics.txt         # Python dependencies for Analytics Service
├── Dockerfile.agent-service           # Docker image for Agent Service
├── Dockerfile.communications-service  # Docker image for Communications Service
├── Dockerfile.contact-service         # Docker image for Contact Service
├── Dockerfile.analytics-service       # Docker image for Analytics Service
├── config/
│   ├── production.env                 # Environment configuration
│   └── production.env.template
├── services/
│   ├── bolna_server_production.py
│   ├── twilio_server_production.py
│   └── firebase_mapper.py
└── scripts/
    ├── docker_build.sh                # Build Docker images
    ├── docker_run_local.sh            # Run containers locally
    ├── deploy_cloudrun.sh             # Deploy to Cloud Run
    └── setup_iam.sh                   # Configure IAM permissions
```

### Services

**Agent Service (`agent-service`)**
- AI conversation service and agent management
- Handles agent creation, management, and conversations
- WebSocket support for real-time communication
- Firestore integration for data persistence
- Comprehensive AI processing capabilities

**Communications Service (`communications-service`)**
- WhatsApp/Twilio integration and message routing
- Webhook handler for communication providers
- Lightweight service for message processing
- Provider-agnostic communication interface

**Contact Service (`contact-service`)**
- HubSpot integration and contact management
- Lead status tracking and synchronization
- Contact data management and validation

**Analytics Service (`analytics-service`)**
- AI-powered conversation analysis
- Lead status detection and insights
- Sentiment analysis and conversation summarization

### Service Endpoints

#### **Agent Service (Port 8080)**
- `GET /` - Service status
- `GET /health` - Health check with detailed status
- `GET /all` - List all agents
- `GET /agent/{agent_id}` - Get specific agent
- `POST /agent` - Create new agent
- `PUT /agent/{agent_id}` - Update agent
- `DELETE /agent/{agent_id}` - Delete agent
- `GET /server-info` - Server URL information
- `WebSocket /chat/v1/{agent_id}` - Real-time conversation

#### **Communications Service (Port 8083)**
- `GET /` - Service status
- `GET /health` - Health check with provider status
- `POST /webhook/whatsapp` - WhatsApp webhook handler
- `POST /send-message` - Send message via provider
- `GET /conversations/{contact_id}` - Get conversation history

#### **Contact Service (Port 8081)**
- `GET /` - Service status
- `GET /health` - Health check with HubSpot status
- `GET /contact/{phone_number}` - Get contact by phone
- `POST /contact/lead-status` - Update lead status
- `POST /sync-hubspot` - Sync with HubSpot

#### **Analytics Service (Port 8082)**
- `GET /` - Service status
- `GET /health` - Health check with AI client status
- `POST /analyze` - Analyze conversation
- `GET /insights/{contact_id}` - Get conversation insights

## Troubleshooting

### Common Issues

**Docker build fails:**
```bash
# Check Docker is running
docker --version
docker ps

# Clean Docker cache if needed
docker system prune -f
```

**Environment configuration missing:**
```bash
# Copy template and configure
cp config/production.env.template config/production.env
# Edit with your API keys
```

**Port already in use:**
```bash
# Use different port
./deploy.sh local agent-service --port 8082

# Or stop existing containers
docker stop $(docker ps -q --filter ancestor=synapy-agent-service)
```

**404 errors when testing locally:**
```bash
# Use localhost instead of 0.0.0.0
curl http://localhost:8080/health

# Check if server is running
docker ps
docker logs synapy-agent-service
```

**Cloud deployment authentication:**
```bash
# Authenticate with Google Cloud
gcloud auth login
gcloud config set project your-project-id
```

**Firestore authentication errors:**
```bash
# Set up application default credentials
gcloud auth application-default login

# Or verify service account key is set
echo $GOOGLE_APPLICATION_CREDENTIALS
```

### Debug Mode

Enable verbose logging for troubleshooting:
```bash
./deploy.sh build all --verbose
./deploy.sh local agent-service --verbose
./deploy.sh deploy all --project my-project --verbose
```

### Container Management

```bash
# List running containers
docker ps

# View container logs
docker logs synapy-agent-service
docker logs synapy-communications-service
docker logs synapy-contact-service
docker logs synapy-analytics-service

# Stop containers
docker stop synapy-agent-service
docker stop synapy-communications-service
docker stop synapy-contact-service
docker stop synapy-analytics-service

# Remove containers
docker rm synapy-agent-service
docker rm synapy-communications-service
docker rm synapy-contact-service
docker rm synapy-analytics-service
```

## Development Tips

1. **Use local Docker for testing** - Identical to production environment
2. **Build once, run multiple times** - Images persist until rebuilt
3. **Use detached mode for background services** - `--detached` flag
4. **Check logs regularly** - `docker logs -f container-name`
5. **Test both services together** - Run on different ports simultaneously

## Getting Help

- Check container logs: `docker logs synapy-agent-service`
- Verify environment: `cat config/production.env`
- Test connectivity: `curl http://localhost:8080/health`
- Use verbose mode: `./deploy.sh <command> --verbose`

For additional support, ensure your `config/production.env` is properly configured and Docker is running.
