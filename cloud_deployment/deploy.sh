#!/bin/bash

# Synapy Microservices Deployment Orchestrator
# Unified deployment script for both local Docker and Google Cloud Run deployment
# Provides streamlined deployment experience for development and production

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_header() { echo -e "${CYAN}🚀 $1${NC}"; }

# Default values
PROJECT_ID=""
REGION="us-central1"
VERBOSE=false
FORCE_DEPLOY=false

# Function to show usage
show_usage() {
  echo "Synapy Microservices Deployment Orchestrator"
  echo "============================="
  echo ""
  echo "Usage: $0 <command> [options]"
  echo ""
  echo "Commands:"
  echo "  build <service>   Build Docker images for local testing"
  echo "  local <service>   Run service locally in Docker container"
  echo "  deploy <service>  Deploy to Google Cloud Run"
  echo ""
  echo "Services:"
  echo "  agent-service         AI agent management service"
  echo "  communications-service WhatsApp/Twilio communication service"
  echo "  contact-service       HubSpot integration and contact management"
  echo "  analytics-service     AI-powered conversation analysis"
  echo "  all                   All services (for build and deploy commands)"
  echo ""
  echo "Options:"
  echo "  --project <id>      Google Cloud Project ID (deploy only)"
  echo "  --region <region>   Google Cloud Region (default: us-central1)"
  echo "  --port <port>       Port for local service (local only)"
  echo "  --detached          Run local container in background"
  echo "  --verbose           Enable verbose logging"
  echo "  --force             Force deployment even if service is up-to-date (deploy only)"
  echo "  --help, -h          Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 build all                                     # Build all Docker images"
  echo "  $0 local agent-service                           # Run agent service locally"
  echo "  $0 local communications-service --port 8083      # Run communications service on port 8083"
  echo "  $0 deploy all --project my-project               # Deploy all services to cloud"
  echo ""
  echo "Prerequisites:"
  echo "  Local development:"
  echo "    • Docker installed and running"
  echo "    • config/production.env file configured with API keys"
  echo "  Cloud deployment:"
  echo "    • Google Cloud SDK installed and authenticated"
  echo "    • config/production.env file configured with API keys"
  echo "    • Required GCP APIs enabled (handled automatically)"
}

# Function to get project ID from various sources
get_project_id() {
  local args_project="$1"

  # First check command line argument
  if [[ -n "$args_project" ]]; then
    echo "$args_project"
    return 0
  fi

  # Then check environment variables
  if [[ -n "$GOOGLE_CLOUD_PROJECT" ]]; then
    echo "$GOOGLE_CLOUD_PROJECT"
    return 0
  fi

  # Finally check production.env file
  local env_file="$SCRIPT_DIR/config/production.env"
  if [[ -f "$env_file" ]]; then
    local project_from_file
    project_from_file=$(grep "^GOOGLE_CLOUD_PROJECT=" "$env_file" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'")
    if [[ -n "$project_from_file" ]]; then
      echo "$project_from_file"
      return 0
    fi
  fi

  # No project ID found
  return 1
}

# Function to check prerequisites
check_prerequisites() {
  log_info "Checking deployment prerequisites..."

  # Check gcloud CLI
  if ! command -v gcloud &> /dev/null; then
    log_error "Google Cloud SDK not found"
    log_info "💡 Install from: https://cloud.google.com/sdk/docs/install"
    exit 1
  fi

  # Check authentication
  if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Not authenticated with Google Cloud"
    log_info "💡 Run: gcloud auth login"
    exit 1
  fi

  # Check environment file exists
  local env_file="$SCRIPT_DIR/config/production.env"
  if [[ ! -f "$env_file" ]]; then
    log_error "Environment configuration file not found: $env_file"
    log_info "💡 Copy config/production.env.template to config/production.env and configure it"
    exit 1
  fi

  # Validate project ID
  if [[ -z "$PROJECT_ID" ]]; then
    log_error "Project ID not found in any source"
    log_info "💡 Specify with --project flag or set GOOGLE_CLOUD_PROJECT environment variable"
    exit 1
  fi

  # Verify project access
  if ! gcloud projects describe "$PROJECT_ID" &>/dev/null; then
    log_error "Cannot access project '$PROJECT_ID'"
    log_info "💡 Check project ID and ensure you have access permissions"
    exit 1
  fi

  log_success "Prerequisites validation completed"
}

# Function to check if IAM setup is needed
is_iam_setup_needed() {
  local service="$1"

  # Check if IAM marker exists and is recent (less than 24 hours old)
  local iam_marker="$SCRIPT_DIR/.iam_markers/${service}_iam_configured"

  if [[ -f "$iam_marker" ]]; then
    local marker_age
    marker_age=$(($(date +%s) - $(stat -c %Y "$iam_marker" 2>/dev/null || stat -f %m "$iam_marker" 2>/dev/null || echo 0)))

    # If marker is less than 24 hours old, skip IAM setup
    if [[ $marker_age -lt 86400 ]]; then
      log_info "IAM configuration for $service is up-to-date (configured ${marker_age}s ago)"
      return 1  # No IAM setup needed
    fi
  fi

  # Check if service exists and has public access
  if gcloud run services describe "$service" --project="$PROJECT_ID" --region="$REGION" &>/dev/null; then
    # Check if allUsers already has invoker role
    if gcloud run services get-iam-policy "$service" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --format="value(bindings[].members[])" 2>/dev/null | grep -q "allUsers"; then

      # Create marker to avoid unnecessary future checks
      mkdir -p "$SCRIPT_DIR/.iam_markers"
      touch "$iam_marker"

      log_info "Service $service already has public access configured"
      return 1  # No IAM setup needed
    fi
  fi

  return 0  # IAM setup needed
}

# Function to create IAM marker
create_iam_marker() {
  local service="$1"

  local marker_dir="$SCRIPT_DIR/.iam_markers"
  mkdir -p "$marker_dir"

  local marker_file="$marker_dir/${service}_iam_configured"
  touch "$marker_file"

  [[ "$VERBOSE" == "true" ]] && log_info "Created IAM marker: ${service}_iam_configured"
}

# Function to execute deployment steps
execute_deployment() {
  local service="$1"

  log_header "Starting deployment process for: $service"

  # Step 1: Deploy services to Cloud Run
  log_info "Step 1/2: Deploying services to Cloud Run..."
  local deploy_args="--project $PROJECT_ID --region $REGION --service $service"
  [[ "$VERBOSE" == "true" ]] && deploy_args="$deploy_args --verbose"
  [[ "$FORCE_DEPLOY" == "true" ]] && deploy_args="$deploy_args --force"

  if "$SCRIPTS_DIR/deploy_cloudrun.sh" $deploy_args; then
    log_success "Cloud Run deployment completed successfully"
  else
    log_error "Cloud Run deployment failed"
    return 1
  fi

  # Step 2: Configure IAM permissions for public access (only if needed)
  if is_iam_setup_needed "$service"; then
    log_info "Step 2/2: Configuring IAM permissions for public access..."
    local iam_args="--project $PROJECT_ID --region $REGION --service $service"
    [[ "$VERBOSE" == "true" ]] && iam_args="$iam_args --verbose"

    if "$SCRIPTS_DIR/setup_iam.sh" $iam_args; then
      log_success "IAM configuration completed successfully"
      create_iam_marker "$service"
    else
      log_warning "IAM configuration failed, but deployment can continue"
      # Don't fail the entire deployment for IAM issues
    fi
  else
    log_success "IAM configuration is up-to-date, skipping"
  fi

  return 0
}

# Function to display actual service URLs
display_service_urls() {
  local service="$1"

  # Function to get service URL
  get_service_url() {
    local service_name="$1"
    gcloud run services describe "$service_name" \
      --project="$PROJECT_ID" \
      --region="$REGION" \
      --format="value(status.url)" 2>/dev/null || echo "URL not available"
  }

  if [[ "$service" == "all" ]]; then
    log_info "🌐 Service URLs:"
    local agent_url=$(get_service_url "agent-service")
    local comm_url=$(get_service_url "communications-service")
    local contact_url=$(get_service_url "contact-service")
    local analytics_url=$(get_service_url "analytics-service")
    log_info "  Agent Service: $agent_url"
    log_info "  Communications Service: $comm_url"
    log_info "  Contact Service: $contact_url"
    log_info "  Analytics Service: $analytics_url"
  else
    local service_url=$(get_service_url "$service")
    log_info "🌐 Service URL: $service_url"
  fi
}

# Function to build Docker images
build_images() {
  local service="$1"
  shift

  log_header "Building Docker images for: $service"

  # Check if build script exists
  local build_script="$SCRIPTS_DIR/docker_build.sh"
  if [[ ! -f "$build_script" ]]; then
    log_error "Docker build script not found: $build_script"
    exit 1
  fi

  # Execute Docker build
  exec "$build_script" --service "$service" "$@"
}

# Function to run local Docker containers
run_local() {
  local service="$1"
  shift

  log_header "Running local Docker container for: $service"

  # Check if local run script exists
  local local_script="$SCRIPTS_DIR/docker_run_local.sh"
  if [[ ! -f "$local_script" ]]; then
    log_error "Docker local run script not found: $local_script"
    exit 1
  fi

  # Execute local Docker run
  exec "$local_script" --service "$service" "$@"
}

# Main deployment function
deploy() {
  local service="$1"
  shift

  # Parse additional arguments
  while [[ $# -gt 0 ]]; do
    case $1 in
      --project)
        PROJECT_ID="$2"
        shift 2
        ;;
      --region)
        REGION="$2"
        shift 2
        ;;
      --verbose)
        VERBOSE=true
        shift
        ;;
      --force)
        FORCE_DEPLOY=true
        shift
        ;;
      *)
        log_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
    esac
  done

  # Validate service
  case "$service" in
    "agent-service"|"communications-service"|"contact-service"|"analytics-service"|"all") ;;
    *)
      log_error "Invalid service: $service"
      show_usage
      exit 1
      ;;
  esac

  # Get project ID if not provided
  if [[ -z "$PROJECT_ID" ]]; then
    if PROJECT_ID=$(get_project_id); then
      log_info "Using project ID: $PROJECT_ID"
    else
      log_error "Project ID required but not found"
      show_usage
      exit 1
    fi
  fi

  # Display deployment configuration
  log_info "Deployment Configuration:"
  log_info "  Service: $service"
  log_info "  Project: $PROJECT_ID"
  log_info "  Region: $REGION"
  log_info "  Verbose: $VERBOSE"

  # Check prerequisites
  check_prerequisites

  # Execute deployment
  if execute_deployment "$service"; then
    log_success "🎉 Deployment completed successfully!"
    log_info "💡 Services should be accessible within a few minutes"

    # Display actual service URLs
    display_service_urls "$service"
  else
    log_error "❌ Deployment failed!"
    exit 1
  fi
}

# Main script execution
main() {
  # Handle help and no arguments
  if [[ $# -eq 0 ]] || [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    show_usage
    exit 0
  fi

  # Handle commands
  case "$1" in
    "build")
      shift
      if [[ $# -eq 0 ]]; then
        log_error "Service name required for build command"
        show_usage
        exit 1
      fi
      build_images "$@"
      ;;
    "local")
      shift
      if [[ $# -eq 0 ]]; then
        log_error "Service name required for local command"
        show_usage
        exit 1
      fi
      run_local "$@"
      ;;
    "deploy")
      shift
      if [[ $# -eq 0 ]]; then
        log_error "Service name required for deploy command"
        show_usage
        exit 1
      fi
      deploy "$@"
      ;;
    *)
      log_error "Unknown command: $1"
      log_info "💡 Use '$0 --help' to see available commands"
      show_usage
      exit 1
      ;;
  esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
