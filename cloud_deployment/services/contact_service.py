"""
Contact Service - HubSpot Integration Microservice

This service handles all contact-related operations including HubSpot synchronization,
lead status management, and contact data operations.
"""

import os
from typing import Dict, Optional, Any
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, ValidationError
from dotenv import load_dotenv

# Shared service imports
from .shared import (
    get_database, get_message_broker, create_event_message,
    ServiceResponse, ValidationUtils, LoggingUtils, ConfigUtils, EventTypes, Topics
)
from .shared.shared_constants import *

load_dotenv()

# Configure logging
logger = LoggingUtils.setup_logger(__name__, ConfigUtils.get_env_var(ENV_LOG_LEVEL, DEFAULT_LOG_LEVEL))

# Initialize services
database = get_database()
message_broker = get_message_broker()

logger.info("Contact Service starting...")

# FastAPI app configuration
app = FastAPI(
    title="Contact Service",
    description="HubSpot integration and contact management microservice",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add validation error handler to provide detailed error messages
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    logger.error(f"Validation error for {request.method} {request.url}: {exc}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": exc.errors(),
            "body": str(exc)
        }
    )

@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    port = os.environ.get('PORT', '8081')
    logger.info(f"Contact Service running on port {port}")

    # Validate database connection
    if database.is_available():
        logger.info("Database connection validated successfully")
    else:
        logger.warning("Database not available - check database configuration")

    # Validate message broker connection
    if message_broker.is_available():
        logger.info("Message broker connection validated successfully")
    else:
        logger.warning("Message broker not available - check configuration")


# Pydantic models
class ContactModel(BaseModel):
    phone_number: str = Field(..., description="Contact phone number")
    email: Optional[str] = Field(None, description="Contact email")
    first_name: Optional[str] = Field(None, description="Contact first name")
    last_name: Optional[str] = Field(None, description="Contact last name")
    company: Optional[str] = Field(None, description="Contact company")
    hubspot_id: Optional[str] = Field(None, description="HubSpot contact ID")
    lead_status: Optional[str] = Field(None, description="Current lead status")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional properties")


class LeadStatusUpdateModel(BaseModel):
    contact_id: str = Field(..., description="Contact ID")
    user_id: str = Field(..., description="User ID for user-specific contact")
    lead_status: str = Field(..., description="New lead status")
    reason: Optional[str] = Field(None, description="Reason for status change")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


# HubSpot Integration with Dynamic Configuration
class HubSpotClient:
    """HubSpot API client with dynamic configuration from Firestore."""

    def __init__(self):
        self.base_url = "https://api.hubapi.com"
        self.session = None
        self._config_cache = None
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5 minutes cache TTL

    async def _get_hubspot_config(self, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get HubSpot configuration from user-specific Firestore location with caching."""
        try:
            if not user_id:
                logger.warning("No user_id provided for HubSpot configuration lookup")
                return None

            # Check cache first (include user_id in cache key)
            import time
            current_time = time.time()
            cache_key = f"{user_id}_config"

            if (self._config_cache and self._cache_timestamp and
                (current_time - self._cache_timestamp) < self._cache_ttl and
                self._config_cache.get("_cache_key") == cache_key):
                return self._config_cache

            # Fetch from user-specific Firestore location
            config = await database.get_document("users", f"{user_id}/integrations/hubspot")

            if config and config.get("api_key"):
                config["_cache_key"] = cache_key  # Add cache key for validation
                self._config_cache = config
                self._cache_timestamp = current_time
                return config

            return None

        except Exception as error:
            logger.error(f"Error fetching HubSpot configuration for user {user_id}: {error}")
            return None

    async def _initialize_session(self, api_key: str):
        """Initialize HTTP session for HubSpot API calls."""
        try:
            import aiohttp
            if self.session:
                await self.session.close()

            self.session = aiohttp.ClientSession(
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
            )
            logger.info("✅ HubSpot client session initialized")
        except ImportError:
            logger.error("aiohttp not available for HubSpot client")
        except Exception as error:
            logger.error(f"Failed to initialize HubSpot client: {error}")

    async def search_contact_by_phone(self, phone_number: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Search for a contact by phone number in HubSpot using user-specific configuration."""
        try:
            # Get user-specific HubSpot configuration
            config = await self._get_hubspot_config(user_id)
            if not config or not config.get("api_key"):
                logger.warning(f"HubSpot not configured for user {user_id} - skipping search")
                return None

            # Initialize session if needed
            if not self.session:
                await self._initialize_session(config["api_key"])

            clean_phone = ValidationUtils.validate_phone_number(phone_number)

            search_payload = {
                "filterGroups": [{
                    "filters": [{
                        "propertyName": "phone",
                        "operator": "EQ",
                        "value": clean_phone
                    }]
                }],
                "properties": ["firstname", "lastname", "email", "phone", "company", "hs_lead_status"]
            }

            async with self.session.post(
                f"{self.base_url}/crm/v3/objects/contacts/search",
                json=search_payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    results = data.get("results", [])
                    return results[0] if results else None
                else:
                    logger.error(f"HubSpot search failed: {response.status}")
                    return None

        except Exception as error:
            logger.error(f"Error searching HubSpot contact: {error}")
            return None

    async def update_lead_status(self, hubspot_id: str, lead_status: str, user_id: Optional[str] = None) -> bool:
        """Update lead status in HubSpot using user-specific configuration."""
        try:
            # Get user-specific HubSpot configuration
            config = await self._get_hubspot_config(user_id)
            if not config or not config.get("api_key"):
                logger.warning(f"HubSpot not configured for user {user_id} - skipping update")
                return False

            # Initialize session if needed
            if not self.session:
                await self._initialize_session(config["api_key"])

            update_payload = {
                "properties": {
                    "hs_lead_status": lead_status
                }
            }

            async with self.session.patch(
                f"{self.base_url}/crm/v3/objects/contacts/{hubspot_id}",
                json=update_payload
            ) as response:
                if response.status == 200:
                    logger.info(f"Updated HubSpot contact {hubspot_id} lead status to {lead_status} for user {user_id}")
                    return True
                else:
                    logger.error(f"HubSpot update failed: {response.status}")
                    return False

        except Exception as error:
            logger.error(f"Error updating HubSpot lead status: {error}")
            return False


# Initialize HubSpot client
hubspot_client = HubSpotClient()


# HubSpot Configuration Models
class HubSpotConfigModel(BaseModel):
    user_id: str = Field(..., description="User ID for user-specific configuration")
    api_key: str = Field(..., description="HubSpot API key")
    enabled: bool = Field(default=True, description="Whether HubSpot integration is enabled")
    sync_frequency: Optional[int] = Field(default=300, description="Sync frequency in seconds")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional configuration")


async def find_contact_by_phone(phone_number: str, user_id: str) -> Optional[Dict[str, Any]]:
    """Find contact by phone number in user-specific storage."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        # Query user's contacts collection to find contact with matching phone number
        contacts_collection = f"users/{user_id}/contacts"
        contacts = await database.query_documents(
            contacts_collection,
            filters={"phone_number": clean_phone},
            limit=1
        )

        return contacts[0] if contacts else None

    except Exception as error:
        logger.error(f"Error finding contact by phone: {error}")
        return None


async def get_contact_by_id(contact_id: str, user_id: str) -> Optional[Dict[str, Any]]:
    """Get contact by contact ID (which is now hubspot_id) from user-specific storage."""
    try:
        # contact_id is now the hubspot_id used as document ID
        contact = await database.get_document("users", f"{user_id}/contacts/{contact_id}")
        return contact
    except Exception as error:
        logger.error(f"Error getting contact by ID: {error}")
        return None


async def get_contact_by_phone(phone_number: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Get contact by phone number from user-specific database or HubSpot."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        if not user_id:
            logger.warning("No user_id provided for contact lookup")
            return None

        # First, try to find existing contact by phone number
        contact = await find_contact_by_phone(clean_phone, user_id)
        if contact:
            return contact

        # If not found, search in HubSpot using user's configuration
        hubspot_contact = await hubspot_client.search_contact_by_phone(clean_phone, user_id)
        if hubspot_contact:
            hubspot_id = hubspot_contact.get("id")

            if not hubspot_id:
                logger.warning(f"HubSpot contact found but missing ID for phone {clean_phone}")
                return None

            # Use hubspot_id as the document ID to naturally prevent duplicates
            # This eliminates the need for separate deduplication checks
            contact_data = {
                "contact_id": hubspot_id,  # Use hubspot_id as contact_id
                "phone_number": clean_phone,
                "hubspot_id": hubspot_id,
                "email": hubspot_contact.get("properties", {}).get("email"),
                "first_name": hubspot_contact.get("properties", {}).get("firstname"),
                "last_name": hubspot_contact.get("properties", {}).get("lastname"),
                "company": hubspot_contact.get("properties", {}).get("company"),
                "lead_status": hubspot_contact.get("properties", {}).get("hs_lead_status"),
                "last_sync": datetime.now().isoformat(),
                "source": "hubspot",
                "user_id": user_id,
                "created_at": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat()
            }

            # Use hubspot_id as document ID - Firestore will naturally prevent duplicates
            await database.set_document("users", f"{user_id}/contacts/{hubspot_id}", contact_data)
            logger.info(f"Stored contact with HubSpot ID {hubspot_id} for user {user_id}")
            return contact_data

        return None

    except Exception as error:
        logger.error(f"Error getting contact by phone: {error}")
        return None


async def fetch_hubspot_contacts_bulk(
    user_id: str,
    limit: int = 100,
    offset: int = 0,
    lead_status: Optional[str] = None,
    created_after: Optional[str] = None,
    updated_after: Optional[str] = None
) -> list:
    """Fetch contacts from HubSpot with pagination and filtering."""
    try:
        # Get user-specific HubSpot configuration
        hubspot_config = await hubspot_client._get_hubspot_config(user_id)
        if not hubspot_config:
            logger.warning(f"HubSpot configuration not found for user {user_id}")
            return []

        # Initialize session if needed
        await hubspot_client._initialize_session(hubspot_config.get("api_key"))

        if not hubspot_client.session:
            logger.error("Failed to initialize HubSpot session")
            return []

        # Build HubSpot API request parameters
        properties = [
            "firstname", "lastname", "email", "phone", "company",
            "lifecyclestage", "hs_lead_status", "lastmodifieddate", "createdate"
        ]

        params = {
            "limit": min(limit, 100),  # HubSpot API limit is 100 per request
            "properties": ",".join(properties),
            "archived": "false"
        }

        # Add pagination
        if offset > 0:
            # For HubSpot pagination, we need to handle this differently
            # We'll fetch in batches and skip the appropriate number
            pass

        # Add date filters
        filters = []
        if created_after:
            filters.append({
                "propertyName": "createdate",
                "operator": "GTE",
                "value": created_after
            })

        if updated_after:
            filters.append({
                "propertyName": "lastmodifieddate",
                "operator": "GTE",
                "value": updated_after
            })

        if lead_status:
            filters.append({
                "propertyName": "hs_lead_status",
                "operator": "EQ",
                "value": lead_status
            })

        # Make request to HubSpot API
        url = f"{hubspot_client.base_url}/crm/v3/objects/contacts"

        if filters:
            # Use search API for filtering
            url = f"{hubspot_client.base_url}/crm/v3/objects/contacts/search"
            search_payload = {
                "filterGroups": [{"filters": filters}],
                "properties": properties,
                "limit": params["limit"],
                "after": offset if offset > 0 else None
            }

            async with hubspot_client.session.post(url, json=search_payload) as response:
                if response.status != 200:
                    logger.error(f"HubSpot API error: {response.status}")
                    return []

                data = await response.json()
                hubspot_contacts = data.get("results", [])
        else:
            # Use simple GET for no filtering
            if offset > 0:
                params["after"] = str(offset)

            async with hubspot_client.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"HubSpot API error: {response.status}")
                    return []

                data = await response.json()
                hubspot_contacts = data.get("results", [])

        # Convert HubSpot contacts to our format
        contacts = []
        for hubspot_contact in hubspot_contacts:
            properties = hubspot_contact.get("properties", {})

            # Build phone number (clean it)
            phone = properties.get("phone", "")
            if phone:
                try:
                    clean_phone = ValidationUtils.validate_phone_number(phone)
                except Exception:
                    continue  # Skip contacts with invalid phone numbers
            else:
                continue  # Skip contacts without phone numbers

            hubspot_id = hubspot_contact.get("id")

            if not hubspot_id:
                logger.warning("HubSpot contact missing ID, skipping")
                continue

            contact_data = {
                "contact_id": hubspot_id,  # Use hubspot_id as contact_id
                "phone_number": clean_phone,
                "hubspot_id": hubspot_id,
                "email": properties.get("email"),
                "first_name": properties.get("firstname"),
                "last_name": properties.get("lastname"),
                "company": properties.get("company"),
                "lead_status": properties.get("hs_lead_status"),
                "lifecycle_stage": properties.get("lifecyclestage"),
                "created_at": properties.get("createdate"),
                "last_modified": properties.get("lastmodifieddate"),
                "source": "hubspot",
                "user_id": user_id
            }

            # Use hubspot_id as document ID - Firestore will naturally prevent duplicates
            # If contact already exists, this will update it instead of creating a duplicate
            try:
                await database.set_document("users", f"{user_id}/contacts/{hubspot_id}", contact_data)
                logger.debug(f"Stored/updated contact with HubSpot ID {hubspot_id} for user {user_id}")
            except Exception as store_error:
                logger.warning(f"Failed to store contact {hubspot_id} for user {user_id}: {store_error}")
                continue  # Skip this contact if storage fails

            contacts.append(contact_data)

        logger.info(f"Fetched {len(contacts)} contacts from HubSpot")
        return contacts

    except Exception as error:
        logger.error(f"Error fetching HubSpot contacts: {error}")
        return []


async def update_contact_lead_status(contact_id: str, lead_status: str, user_id: str, reason: Optional[str] = None) -> bool:
    """Update contact lead status in user-specific database and HubSpot using contact ID."""
    try:
        # Get contact from user-specific database using contact ID
        contact = await get_contact_by_id(contact_id, user_id)
        if not contact:
            logger.error(f"Contact {contact_id} not found for user {user_id}")
            return False

        # Store previous status for event
        previous_status = contact.get("lead_status")

        # Update in database
        contact["lead_status"] = lead_status
        contact["last_updated"] = datetime.now().isoformat()
        if reason:
            contact["status_change_reason"] = reason

        await database.set_document("users", f"{user_id}/contacts/{contact_id}", contact)

        # Update in HubSpot if hubspot_id exists
        hubspot_id = contact.get("hubspot_id")
        if hubspot_id:
            await hubspot_client.update_lead_status(hubspot_id, lead_status, user_id)

        # Publish event
        event_message = create_event_message(
            event_type=EventTypes.LEAD_STATUS_UPDATED,
            aggregate_id=contact_id,
            payload={
                "contact_id": contact_id,
                "user_id": user_id,
                "old_status": previous_status,
                "new_status": lead_status,
                "reason": reason,
                "hubspot_id": hubspot_id,
                "updated_at": datetime.now().isoformat()
            },
            source="contact-service"
        )

        await message_broker.publish(Topics.LEAD_EVENTS, event_message)

        logger.info(f"Updated lead status for contact {contact_id} to {lead_status}")
        return True

    except Exception as error:
        logger.error(f"Error updating contact lead status: {error}")
        return False


# API Endpoints
@app.get("/")
def root():
    return {"message": "Service is running"}


@app.post("/hubspot/configure")
async def configure_hubspot(config: HubSpotConfigModel):
    """Configure HubSpot integration settings for a specific user."""
    try:
        # Validate API key by making a test request
        test_client = HubSpotClient()
        await test_client._initialize_session(config.api_key)

        # Test the API key with a simple request
        if test_client.session:
            async with test_client.session.get(
                f"{test_client.base_url}/crm/v3/properties/contacts"
            ) as response:
                if response.status != 200:
                    raise HTTPException(status_code=400, detail="Invalid HubSpot API key")

        # Store configuration in user-specific location
        config_data = {
            "api_key": config.api_key,
            "enabled": config.enabled,
            "sync_frequency": config.sync_frequency,
            "properties": config.properties,
            "configured_at": datetime.now().isoformat(),
            "last_validated": datetime.now().isoformat(),
            "user_id": config.user_id
        }

        await database.set_document("users", f"{config.user_id}/integrations/hubspot", config_data)

        # Clear cache to force reload
        hubspot_client._config_cache = None
        hubspot_client._cache_timestamp = None

        logger.info(f"HubSpot configuration updated successfully for user {config.user_id}")
        return {"message": "HubSpot configuration updated successfully", "status": "configured", "user_id": config.user_id}

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error configuring HubSpot for user {config.user_id}: {error}")
        raise HTTPException(status_code=500, detail="Failed to configure HubSpot")


class HubSpotConfigWithUserModel(BaseModel):
    user_id: str = Field(..., description="User ID for user-specific configuration")
    api_key: str = Field(..., description="HubSpot API key")
    enabled: bool = Field(default=True, description="Whether HubSpot integration is enabled")
    sync_frequency: Optional[int] = Field(default=300, description="Sync frequency in seconds")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional configuration")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database availability
        database_healthy = database.is_available()

        # Check message broker availability
        message_broker_healthy = message_broker.is_available()

        # Determine overall health
        overall_healthy = all([database_healthy, message_broker_healthy])

        health_status = {
            "status": "healthy" if overall_healthy else "unhealthy",
            "service": "contact-service",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "database": "healthy" if database_healthy else "unhealthy",
                "message_broker": "healthy" if message_broker_healthy else "unhealthy"
            }
        }

        status_code = 200 if overall_healthy else 503
        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "contact-service",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            status_code=503
        )


@app.get("/contact/by-phone/{phone_number}")
async def get_contact_by_phone_endpoint(
    phone_number: str,
    user_id: str = Query(..., description="User ID for user-specific contact lookup")
):
    """Get contact by phone number for a specific user."""
    try:
        contact = await get_contact_by_phone(phone_number, user_id)
        if not contact:
            raise HTTPException(status_code=404, detail="Contact not found")

        return ServiceResponse.success(contact)

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error getting contact by phone: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/contact/{contact_id}")
async def get_contact_by_id_endpoint(
    contact_id: str,
    user_id: str = Query(..., description="User ID for user-specific contact lookup")
):
    """Get contact by contact ID for a specific user."""
    try:
        contact = await get_contact_by_id(contact_id, user_id)
        if not contact:
            raise HTTPException(status_code=404, detail="Contact not found")

        return ServiceResponse.success(contact)

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error getting contact by ID: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/contact/lookup/{phone_number}")
async def lookup_contact_id_by_phone(
    phone_number: str,
    user_id: str = Query(..., description="User ID for user-specific contact lookup")
):
    """Lookup contact ID by phone number - used by communications service."""
    try:
        contact = await find_contact_by_phone(phone_number, user_id)
        if not contact:
            raise HTTPException(status_code=404, detail="Contact not found")

        return ServiceResponse.success({
            "contact_id": contact.get("contact_id"),
            "phone_number": contact.get("phone_number"),
            "user_id": user_id
        })

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error looking up contact ID by phone: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/contacts")
async def get_all_contacts(
    user_id: str = Query(..., description="User ID for user-specific contacts"),
    limit: int = Query(default=100, ge=1, le=1000, description="Number of contacts to return"),
    offset: int = Query(default=0, ge=0, description="Number of contacts to skip"),
    lead_status: Optional[str] = Query(default=None, description="Filter by lead status"),
    created_after: Optional[str] = Query(default=None, description="Filter contacts created after this date (ISO format)"),
    updated_after: Optional[str] = Query(default=None, description="Filter contacts updated after this date (ISO format)")
):
    """Get all HubSpot contacts with pagination and filtering for a specific user."""
    try:
        # Check if HubSpot is configured for this user
        hubspot_config = await hubspot_client._get_hubspot_config(user_id)
        if not hubspot_config:
            raise HTTPException(status_code=503, detail=f"HubSpot integration not configured for user {user_id}")

        # Fetch contacts from HubSpot with pagination
        contacts = await fetch_hubspot_contacts_bulk(
            user_id=user_id,
            limit=limit,
            offset=offset,
            lead_status=lead_status,
            created_after=created_after,
            updated_after=updated_after
        )

        return ServiceResponse.success({
            "contacts": contacts,
            "pagination": {
                "limit": limit,
                "offset": offset,
                "total": len(contacts),
                "has_more": len(contacts) == limit
            }
        })

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error getting all contacts: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/contact/lead-status")
async def update_lead_status(request: LeadStatusUpdateModel):
    """Update contact lead status."""
    try:
        # Validate lead status value
        ValidationUtils.validate_lead_status(request.lead_status)

        success = await update_contact_lead_status(
            contact_id=request.contact_id,
            lead_status=request.lead_status,
            user_id=request.user_id,
            reason=request.reason
        )

        if not success:
            raise HTTPException(status_code=400, detail="Failed to update lead status")

        return ServiceResponse.success({"status": "updated"})

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error updating lead status: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/contact/{phone_number}/lead-status-history")
async def get_lead_status_history(phone_number: str):
    """Get lead status history for a contact."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        # Query lead status history
        history = await database.query_documents(
            "lead_status_history",
            filters={"contact_id": clean_phone},
            limit=50
        )

        # Sort by timestamp
        history.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

        return ServiceResponse.success(history)

    except Exception as error:
        logger.error(f"Error getting lead status history: {error}")
        raise HTTPException(status_code=500, detail="Internal server error")


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8080))
    logger.info(f"Starting Contact Service on http://0.0.0.0:{port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
