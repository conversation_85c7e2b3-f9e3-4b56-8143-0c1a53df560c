"""
Analytics Service - AI-Powered Conversation Analysis Microservice

This service handles conversation analysis, lead status detection, and analytics processing
using AI models to extract insights from conversations.
"""

import os
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, ValidationError
from dotenv import load_dotenv

# Shared service imports
from .shared import (
    get_database, get_message_broker, create_event_message,
    ServiceResponse, LoggingUtils, ConfigUtils, EventTypes, Topics
)
from .shared.shared_constants import *

load_dotenv()

# Configure logging
logger = LoggingUtils.setup_logger(__name__, ConfigUtils.get_env_var(ENV_LOG_LEVEL, DEFAULT_LOG_LEVEL))

# Initialize services
database = get_database()
message_broker = get_message_broker()

logger.info("Analytics Service starting...")

# FastAPI app configuration
app = FastAPI(
    title="Analytics Service",
    description="AI-powered conversation analysis and lead status detection microservice",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add validation error handler to provide detailed error messages
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    logger.error(f"Validation error for {request.method} {request.url}: {exc}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": exc.errors(),
            "body": str(exc)
        }
    )

@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    port = os.environ.get('PORT', '8082')
    logger.info(f"Analytics Service running on port {port}")

    # Validate database connection
    if database.is_available():
        logger.info("Database connection validated successfully")
    else:
        logger.warning("Database not available - check database configuration")

    # Validate message broker connection
    if message_broker.is_available():
        logger.info("Message broker connection validated successfully")
    else:
        logger.warning("Message broker not available - check configuration")

    # Validate AI client
    if conversation_analyzer.ai_client:
        logger.info("AI client validated successfully")
    else:
        logger.warning("AI client not available - check AI configuration")


# Pydantic models
class ConversationAnalysisRequest(BaseModel):
    contact_id: str = Field(..., description="Contact identifier")
    messages: List[Dict[str, Any]] = Field(..., description="List of conversation messages")
    agent_id: Optional[str] = Field(None, description="Agent ID if applicable")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class AnalysisResult(BaseModel):
    contact_id: str
    lead_status: Optional[str]
    confidence: float
    key_insights: List[str]
    sentiment: str
    intent: Optional[str]
    next_actions: List[str]
    analysis_timestamp: datetime


# AI Analysis Engine
class ConversationAnalyzer:
    """AI-powered conversation analysis engine."""

    def __init__(self):
        self.ai_client = None
        self._initialize_ai_client()

    def _initialize_ai_client(self):
        """Initialize AI client for conversation analysis."""
        try:
            # Initialize OpenAI or other AI service
            import openai
            api_key = ConfigUtils.get_env_var("OPENAI_API_KEY")
            if api_key:
                openai.api_key = api_key
                self.ai_client = openai
                logger.info("✅ AI client initialized")
            else:
                logger.warning("OpenAI API key not configured")
        except ImportError:
            logger.error("OpenAI library not available")
        except Exception as error:
            logger.error(f"Failed to initialize AI client: {error}")

    async def analyze_conversation(self, messages: List[Dict[str, Any]], contact_id: str) -> Dict[str, Any]:
        """Analyze conversation to extract insights and determine lead status."""
        try:
            if not self.ai_client:
                return self._fallback_analysis(messages)

            # Prepare conversation text
            conversation_text = self._format_conversation(messages)

            # AI analysis prompt
            analysis_prompt = f"""
            Analyze the following WhatsApp conversation and provide insights:

            Conversation:
            {conversation_text}

            Please analyze and provide:
            1. Lead Status (NEW, OPEN, IN_PROGRESS, OPEN_DEAL, UNQUALIFIED, ATTEMPTED_TO_CONTACT, CONNECTED, BAD_TIMING)
            2. Confidence level (0.0 to 1.0)
            3. Key insights (list of important points)
            4. Overall sentiment (positive, neutral, negative)
            5. Customer intent (if identifiable)
            6. Recommended next actions

            Respond in JSON format with valid HubSpot API lead status values.
            """

            # Call AI service
            response = await self._call_ai_service(analysis_prompt)

            # Parse and validate response
            analysis_result = self._parse_ai_response(response, contact_id)

            return analysis_result

        except Exception as error:
            logger.error(f"Error in conversation analysis: {error}")
            return self._fallback_analysis(messages)

    def _format_conversation(self, messages: List[Dict[str, Any]]) -> str:
        """Format messages into readable conversation text."""
        formatted_messages = []

        for msg in messages:
            sender = "Customer" if msg.get("direction") == "inbound" else "Agent"
            content = msg.get("content", msg.get("body", ""))
            timestamp = msg.get("timestamp", msg.get("stored_at", ""))

            formatted_messages.append(f"[{timestamp}] {sender}: {content}")

        return "\n".join(formatted_messages)

    async def _call_ai_service(self, prompt: str) -> str:
        """Call AI service for analysis."""
        try:
            response = await self.ai_client.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert conversation analyst specializing in lead qualification and customer intent detection."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )

            return response.choices[0].message.content

        except Exception as error:
            logger.error(f"AI service call failed: {error}")
            raise

    def _parse_ai_response(self, response: str, contact_id: str) -> Dict[str, Any]:
        """Parse AI response into structured analysis result."""
        try:
            # Try to parse JSON response
            analysis_data = json.loads(response)

            return {
                "contact_id": contact_id,
                "lead_status": analysis_data.get("lead_status"),
                "confidence": float(analysis_data.get("confidence", 0.5)),
                "key_insights": analysis_data.get("key_insights", []),
                "sentiment": analysis_data.get("sentiment", "neutral"),
                "intent": analysis_data.get("intent"),
                "next_actions": analysis_data.get("next_actions", []),
                "analysis_timestamp": datetime.utcnow(),
                "analysis_method": "ai"
            }

        except json.JSONDecodeError:
            logger.error("Failed to parse AI response as JSON")
            return self._fallback_analysis([])
        except Exception as error:
            logger.error(f"Error parsing AI response: {error}")
            return self._fallback_analysis([])

    def _fallback_analysis(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Provide fallback analysis when AI is not available."""
        message_count = len(messages)

        # Simple heuristic-based analysis using valid HubSpot values
        if message_count == 0:
            lead_status = "UNQUALIFIED"
            confidence = 0.3
        elif message_count == 1:
            lead_status = "ATTEMPTED_TO_CONTACT"
            confidence = 0.6
        elif message_count <= 3:
            lead_status = "CONNECTED"
            confidence = 0.7
        else:
            lead_status = "IN_PROGRESS"
            confidence = 0.8

        return {
            "contact_id": "unknown",
            "lead_status": lead_status,
            "confidence": confidence,
            "key_insights": [f"Conversation has {message_count} messages"],
            "sentiment": "neutral",
            "intent": None,
            "next_actions": ["Continue conversation", "Follow up"],
            "analysis_timestamp": datetime.utcnow(),
            "analysis_method": "heuristic"
        }


# Initialize analyzer
conversation_analyzer = ConversationAnalyzer()


async def analyze_and_update_lead_status(contact_id: str, messages: List[Dict[str, Any]],
                                       agent_id: Optional[str] = None) -> Dict[str, Any]:
    """Analyze conversation and update lead status if needed."""
    try:
        # Perform AI analysis
        analysis_result = await conversation_analyzer.analyze_conversation(messages, contact_id)

        # Store analysis result
        analysis_id = f"{contact_id}_{datetime.utcnow().isoformat()}"
        await database.set_document("conversation_analyses", analysis_id, analysis_result)

        # Check if lead status should be updated
        new_lead_status = analysis_result.get("lead_status")
        confidence = analysis_result.get("confidence", 0.0)

        if new_lead_status and confidence > 0.7:
            # Get current contact to compare status
            contact = await database.get_document("contacts", contact_id)
            current_status = contact.get("lead_status") if contact else None

            if current_status != new_lead_status:
                # Publish lead status update event
                event_message = create_event_message(
                    event_type=EventTypes.LEAD_STATUS_UPDATED,
                    aggregate_id=contact_id,
                    payload={
                        "contact_id": contact_id,
                        "old_status": current_status,
                        "new_status": new_lead_status,
                        "confidence": confidence,
                        "analysis_id": analysis_id,
                        "agent_id": agent_id
                    },
                    source="analytics-service"
                )

                await message_broker.publish(Topics.LEAD_EVENTS, event_message)

                logger.info(f"Published lead status update for {contact_id}: {current_status} -> {new_lead_status}")

        # Publish analysis completed event
        analysis_event = create_event_message(
            event_type=EventTypes.ANALYSIS_COMPLETED,
            aggregate_id=contact_id,
            payload={
                "contact_id": contact_id,
                "analysis_id": analysis_id,
                "lead_status": new_lead_status,
                "confidence": confidence,
                "agent_id": agent_id
            },
            source="analytics-service"
        )

        await message_broker.publish(Topics.ANALYTICS_EVENTS, analysis_event)

        logger.info(f"Completed analysis published to {Topics.AGENT_EVENTS} topic: {analysis_event}")

        return analysis_result

    except Exception as error:
        logger.error(f"Error in analyze_and_update_lead_status: {error}")
        raise


async def handle_message_received_event(message_data: Dict[str, Any], attributes: Dict[str, str]):
    """Handle message received events for analysis."""
    try:
        payload = message_data.get("payload", {})
        contact_id = payload.get("contact_id")
        agent_id = payload.get("agent_id")

        if not contact_id:
            logger.warning("Message received event missing contact_id")
            return

        # Get recent messages for this contact
        messages = await database.query_documents(
            os.getenv('CONVERSATIONS_COLLECTION', 'conversations'),
            filters={"phone_number": contact_id},
            limit=20
        )

        # Sort by timestamp
        messages.sort(key=lambda x: x.get("stored_at", ""))

        # Analyze conversation
        await analyze_and_update_lead_status(contact_id, messages, agent_id)

    except Exception as error:
        logger.error(f"Error handling message received event: {error}")


# API Endpoints
@app.get("/")
def root():
    return {"message": "Service is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database availability
        database_healthy = database.is_available()

        # Check message broker availability
        message_broker_healthy = message_broker.is_available()

        # Check AI client availability
        ai_client_healthy = conversation_analyzer.ai_client is not None

        # Determine overall health
        overall_healthy = all([database_healthy, message_broker_healthy, ai_client_healthy])

        health_status = {
            "status": "healthy" if overall_healthy else "unhealthy",
            "service": "analytics-service",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "database": "healthy" if database_healthy else "unhealthy",
                "message_broker": "healthy" if message_broker_healthy else "unhealthy",
                "ai_client": "healthy" if ai_client_healthy else "unhealthy"
            }
        }

        status_code = 200 if overall_healthy else 503
        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "analytics-service",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            status_code=503
        )


@app.post("/analyze")
async def analyze_conversation(request: ConversationAnalysisRequest):
    """Analyze a conversation and return insights."""
    try:
        analysis_result = await analyze_and_update_lead_status(
            contact_id=request.contact_id,
            messages=request.messages,
            agent_id=request.agent_id
        )

        return ServiceResponse.success(analysis_result)

    except Exception as error:
        logger.error(f"Error analyzing conversation: {error}")
        raise HTTPException(status_code=500, detail="Analysis failed")


@app.get("/analysis/{contact_id}")
async def get_analysis_history(contact_id: str, limit: int = 10):
    """Get analysis history for a contact."""
    try:
        analyses = await database.query_documents(
            "conversation_analyses",
            filters={"contact_id": contact_id},
            limit=limit
        )

        # Sort by timestamp
        analyses.sort(key=lambda x: x.get("analysis_timestamp", ""), reverse=True)

        return ServiceResponse.success(analyses)

    except Exception as error:
        logger.error(f"Error getting analysis history: {error}")
        raise HTTPException(status_code=500, detail="Failed to get analysis history")


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8080))
    logger.info(f"Starting Analytics Service on http://0.0.0.0:{port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
