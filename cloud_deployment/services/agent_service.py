import json
import os
import sys
import warnings
from typing import Any, Dict, List, Optional

# Add parent directory to Python path for bolna imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Shared service imports
from .shared import (
    get_database, check_database_available,
    ServiceResponse, ValidationUtils
)
from .shared.shared_constants import *

from dotenv import load_dotenv
from fastapi import (
  Body,
  FastAPI,
  HTTPException,
  Request,
  WebSocket,
  WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError

# Import proper bolna components
try:
    from bolna.agent_manager.assistant_manager import AssistantManager
    from bolna.llms import LiteLLM
    from bolna.helpers.logger_config import configure_logger
    from bolna.helpers.storage_utils import store_file
    from bolna.models import AgentModel
    from bolna.prompts import EXTRACTION_PROMPT_GENERATION_PROMPT
    from bolna.constants import PREPROCESS_DIR
    BOLNA_AVAILABLE = True
    logger = configure_logger(__name__)
    logger.info("✅ Bolna components imported successfully")
except ImportError as e:
    # Fallback to basic logging if bolna components are not available
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ Bolna components not available: {e}")
    logger.warning("Using fallback implementations - some features may be limited")

    # Fallback implementations
    class FallbackAssistantManager:
        def __init__(self, *args, **kwargs):
            logger.warning("Using fallback AssistantManager - WebSocket functionality limited")

        async def run(self, local=False):
            logger.warning("Fallback AssistantManager.run() called")
            yield 0, {"message": "Fallback mode - limited functionality"}

    class FallbackLiteLLM:
        def __init__(self, model, max_tokens=2000, **kwargs):
            self.model = model
            self.max_tokens = max_tokens
            logger.warning(f"Using fallback LiteLLM for model: {model}")

        async def generate(self, messages):
            logger.warning("Fallback LiteLLM.generate() called")
            return "Fallback response - LiteLLM not available"

    async def fallback_store_file(*args, **kwargs):
        logger.warning("Fallback store_file() called - file not actually stored")
        return True

    class FallbackAgentModel(BaseModel):
        agent_id: str
        name: str
        description: Optional[str] = None

    AssistantManager = FallbackAssistantManager
    LiteLLM = FallbackLiteLLM
    store_file = fallback_store_file
    AgentModel = FallbackAgentModel
    EXTRACTION_PROMPT_GENERATION_PROMPT = """You are a parsing assistant. Convert extraction instructions into JSON format with snake_case keys and full instruction blocks as values."""
    BOLNA_AVAILABLE = False

# Get bucket name from environment with fallback
BUCKET_NAME_PROMPTS = os.getenv("BUCKET_NAME_PROMPTS", "synapy-prompts")

# Constants for agent states and responses
AGENT_NOT_FOUND = "Agent not found"
AGENT_STATE_CREATED = "created"
AGENT_STATE_UPDATED = "updated"
AGENT_STATE_DELETED = "deleted"
AGENT_STATE_ACTIVE = "active"
AGENT_STATE_INACTIVE = "inactive"

# Error messages
INTERNAL_SERVER_ERROR = "Internal server error"
INVALID_REQUEST_DATA = "Invalid request data"
AGENT_CREATION_FAILED = "Failed to create agent"
AGENT_UPDATE_FAILED = "Failed to update agent"
AGENT_DELETION_FAILED = "Failed to delete agent"

# HTTP Status codes
HTTP_OK = 200
HTTP_CREATED = 201
HTTP_BAD_REQUEST = 400
HTTP_NOT_FOUND = 404
HTTP_INTERNAL_SERVER_ERROR = 500

# Suppress Google Cloud metadata server warnings for local development
warnings.filterwarnings("ignore", message=".*Compute Engine Metadata server.*")
warnings.filterwarnings("ignore", module="google.auth._default")

load_dotenv(dotenv_path="/app/config/production.env")

# Logger is already configured above in the import section
logger.info("Starting Synapy Agent Service...")

# Initialize database interface
database = get_database()

# WebSocket connection management
active_websockets: List[WebSocket] = []

# Firebase serialization functions
def serialize_primitive_types(value: Any) -> Any:
  """Handle primitive and simple types for Firestore serialization."""
  # Handle None explicitly first
  if value is None:
    return None

  # Handle basic types
  if isinstance(value, (str, bool)):
    return value

  # Handle numeric types with validation
  if isinstance(value, int):
    # Firestore has limits on integer size
    if -9223372036854775808 <= value <= 9223372036854775807:
      return value
    else:
      return str(value)

  if isinstance(value, float):
    # Handle special float values
    import math
    if math.isnan(value):
      return None
    elif math.isinf(value):
      return str(value)
    else:
      return value

  if isinstance(value, complex):
    return str(value)

  if isinstance(value, bytes):
    try:
      return value.decode('utf-8')
    except UnicodeDecodeError:
      return str(value)

  if isinstance(value, set):
    return list(value)

  return None

def serialize_pydantic_model(value: BaseModel, depth: int) -> Any:
  """Handle Pydantic model serialization."""
  try:
    return ensure_serializable(value.model_dump(exclude_none=True), depth + 1)
  except Exception:
    return str(value)

def serialize_dict(value: dict, depth: int) -> dict:
  """Handle dictionary serialization."""
  result = {}
  for k, v in value.items():
    key = str(k) if k is not None else "null"
    result[key] = ensure_serializable(v, depth + 1)
  return result

def serialize_list(value: Any, depth: int) -> list:
  """Handle list and tuple serialization."""
  return [ensure_serializable(item, depth + 1) for item in value]

def serialize_dict_for_firestore(value: dict, depth: int) -> dict:
  """Handle dictionary serialization with Firestore-specific constraints."""
  result = {}
  for k, v in value.items():
    # Ensure key is a valid Firestore field name
    key = str(k) if k is not None else "null"

    # Firestore field names cannot contain certain characters
    key = key.replace('.', '_dot_').replace('/', '_slash_').replace('[', '_lbracket_').replace(']', '_rbracket_')

    # Serialize the value
    serialized_value = ensure_serializable(v, depth + 1)

    # Skip None values to reduce document size
    if serialized_value is not None:
      result[key] = serialized_value

  return result

def serialize_list_for_firestore(value: Any, depth: int) -> list:
  """Handle list and tuple serialization with Firestore-specific constraints."""
  result = []
  for item in value:
    serialized_item = ensure_serializable(item, depth + 1)
    # Only add non-None items
    if serialized_item is not None:
      result.append(serialized_item)
  return result

def ensure_serializable(value: Any, depth: int = 0) -> Any:
  """Convert non-serializable objects to JSON-friendly types for Firestore."""
  # Firestore has strict limits - prevent deep nesting
  if depth > 5:  # Reduced from 10 to 5 for Firestore compatibility
    if isinstance(value, (dict, list, tuple)):
      # Convert complex nested structures to JSON strings
      try:
        return json.dumps(value, default=str)
      except Exception:
        return str(value)
    return str(value) if value is not None else None

  if value is None or callable(value):
    return None

  # Handle Pydantic models
  if isinstance(value, BaseModel):
    return serialize_pydantic_model(value, depth)

  # Handle dictionaries with Firestore-specific validation
  if isinstance(value, dict):
    return serialize_dict_for_firestore(value, depth)

  # Handle lists and tuples with Firestore-specific validation
  if isinstance(value, (list, tuple)):
    return serialize_list_for_firestore(value, depth)

  # Handle primitive types
  primitive_result = serialize_primitive_types(value)
  if primitive_result is not None:
    return primitive_result

  # Fallback to string representation
  try:
    return str(value)
  except Exception:
    return None

def process_llm_agent_config(tools_config: dict) -> None:
  """Process llm_agent configuration within tools_config."""
  if "llm_agent" in tools_config and isinstance(tools_config["llm_agent"], dict):
    llm_agent = tools_config["llm_agent"]
    if "extraction_json" in llm_agent and isinstance(llm_agent["extraction_json"], dict):
      llm_agent["extraction_json"] = json.dumps(llm_agent["extraction_json"])

def process_toolchain_config(processed_task: dict) -> None:
  """Process toolchain configuration within task."""
  if "toolchain" in processed_task and isinstance(processed_task["toolchain"], dict):
    toolchain = processed_task["toolchain"]
    if "pipelines" in toolchain and isinstance(toolchain["pipelines"], list):
      toolchain["pipelines"] = ensure_serializable(toolchain["pipelines"])

def process_tools_config_for_firestore(tools_config: dict) -> dict:
  """Process tools_config with Firestore-specific handling for deeply nested structures."""
  result = {}

  for key, value in tools_config.items():
    if isinstance(value, dict):
      # Handle specific tool configurations that tend to be deeply nested
      if key in ["llm_agent", "synthesizer", "transcriber", "input", "output"]:
        # These can have deeply nested configurations - flatten if necessary
        processed_value = {}
        for sub_key, sub_value in value.items():
          if isinstance(sub_value, dict) and len(str(sub_value)) > 1000:  # Large nested object
            # Convert large nested objects to JSON strings
            processed_value[sub_key] = json.dumps(sub_value, default=str)
          else:
            processed_value[sub_key] = ensure_serializable(sub_value, depth=0)
        result[key] = processed_value
      else:
        result[key] = ensure_serializable(value, depth=0)
    else:
      result[key] = ensure_serializable(value, depth=0)

  return result

def process_toolchain_for_firestore(toolchain: dict) -> dict:
  """Process toolchain with Firestore-specific handling."""
  result = {}

  for key, value in toolchain.items():
    if key == "pipelines" and isinstance(value, list):
      # Pipelines are arrays of arrays - ensure they're properly serialized
      result[key] = ensure_serializable(value, depth=0)
    else:
      result[key] = ensure_serializable(value, depth=0)

  return result


def convert_pipelines_to_firestore_format(pipelines: list) -> list:
  """Convert nested array pipelines to Firestore-compatible boolean maps."""
  result = []
  for pipeline in pipelines:
    if isinstance(pipeline, (list, tuple)):
      # Convert array of strings to boolean map
      pipeline_map = {}
      for step in pipeline:
        if isinstance(step, str):
          pipeline_map[step] = True
      result.append(pipeline_map)
    else:
      # Keep non-array items as is
      result.append(pipeline)
  return result


def convert_pipelines_from_firestore_format(pipelines: list) -> list:
  """Convert Firestore boolean maps back to nested array pipelines for AssistantManager."""
  result = []
  for pipeline in pipelines:
    if isinstance(pipeline, dict):
      # Convert boolean map back to array of strings
      pipeline_array = [step for step, enabled in pipeline.items() if enabled]
      result.append(pipeline_array)
    else:
      # Keep non-dict items as is (already in array format)
      result.append(pipeline)
  return result

def handle_primitive_types(data: Any) -> Any:
  """Handle primitive type serialization for Firestore."""
  if isinstance(data, bool):
    return data
  elif isinstance(data, int):
    # Firestore supports 64-bit signed integers
    if -9223372036854775808 <= data <= 9223372036854775807:
      return data
    else:
      return str(data)
  elif isinstance(data, float):
    import math
    if math.isnan(data):
      return None  # Exclude NaN values
    elif math.isinf(data):
      return str(data)  # Convert infinity to string
    else:
      return data
  elif isinstance(data, str):
    return data
  else:
    try:
      return str(data)
    except Exception:
      return None

def handle_dict_serialization(data: dict, depth: int, max_depth: int) -> dict:
  """Handle dictionary serialization for Firestore."""
  result = {}
  for key, value in data.items():
    # Sanitize key names for Firestore
    clean_key = str(key).replace('.', '_dot_').replace('/', '_slash_')

    # Recursively serialize value
    serialized_value = serialize_for_firestore(value, depth + 1, max_depth)

    # Only include non-None values
    if serialized_value is not None:
      result[clean_key] = serialized_value

  return result

def handle_list_serialization(data: Any, depth: int, max_depth: int) -> list:
  """Handle list serialization for Firestore."""
  result = []
  for item in data:
    serialized_item = serialize_for_firestore(item, depth + 1, max_depth)
    # Include None items in arrays (they might be meaningful)
    result.append(serialized_item)
  return result

def serialize_for_firestore(data: Any, depth: int = 0, max_depth: int = 6) -> Any:
  """Serialize data for Firestore while preserving object hierarchy and types."""
  # Prevent excessive nesting but allow reasonable depth
  if depth > max_depth:
    # Only convert to string if it's a complex nested structure
    if isinstance(data, (dict, list)) and len(str(data)) > 500:
      try:
        return json.dumps(data, default=str)
      except Exception:
        return str(data)
    return data

  # Handle None values - exclude them entirely
  if data is None:
    return None

  # Handle dictionaries - preserve object structure
  if isinstance(data, dict):
    return handle_dict_serialization(data, depth, max_depth)

  # Handle lists and tuples - preserve array structure
  elif isinstance(data, (list, tuple)):
    return handle_list_serialization(data, depth, max_depth)

  # Handle primitive types
  else:
    return handle_primitive_types(data)

def process_task_for_firestore_v2(task: Dict[str, Any]) -> Dict[str, Any]:
  """Process a single task with improved Firestore serialization."""
  # Handle toolchain pipelines specially
  if "toolchain" in task and isinstance(task["toolchain"], dict):
    toolchain = task["toolchain"]
    if "pipelines" in toolchain and isinstance(toolchain["pipelines"], list):
      # Convert nested arrays to boolean maps for Firestore compatibility
      toolchain["pipelines"] = convert_pipelines_to_firestore_format(toolchain["pipelines"])

  # Serialize the entire task while preserving structure
  return serialize_for_firestore(task)

def map_to_firestore(payload: Any) -> Dict[str, Any]:
  """Map CreateAgentPayload to Firestore-compatible format."""
  try:
    # Convert Pydantic model to dict
    agent_data = payload.agent_config.model_dump()
    agent_data["assistant_status"] = "seeding"
    agent_data["agent_prompts"] = payload.agent_prompts or {}

    # Process tasks with improved serialization
    if "tasks" in agent_data and isinstance(agent_data["tasks"], list):
      processed_tasks = []
      for i, task in enumerate(agent_data["tasks"]):
        try:
          # Use improved serialization that preserves structure
          processed_task = process_task_for_firestore_v2(task)
          processed_tasks.append(processed_task)
          logger.debug(f"Successfully processed task {i}")
        except Exception as task_error:
          logger.error(f"Error processing task {i}: {task_error}")
          # Fallback to basic serialization
          processed_tasks.append(serialize_for_firestore(task))

      agent_data["tasks"] = processed_tasks

    # Apply final serialization to entire structure
    result = serialize_for_firestore(agent_data)

    # Validate that result is JSON serializable (Firestore requirement)
    json.dumps(result, default=str)

    return result

  except Exception as e:
    logger.error(f"Error mapping to Firestore: {e}", exc_info=True)
    raise HTTPException(status_code=422, detail=f"Serialization error: {str(e)}")

def map_agent_update_to_firestore(payload: Any) -> Dict[str, Any]:
  """Map agent update payload to Firestore-compatible format."""
  try:
    # Convert Pydantic model to dict
    agent_data = payload.agent_config.model_dump()
    agent_data["agent_prompts"] = payload.agent_prompts or {}

    # Process tasks with improved serialization
    if "tasks" in agent_data and isinstance(agent_data["tasks"], list):
      processed_tasks = []
      for i, task in enumerate(agent_data["tasks"]):
        try:
          processed_task = process_task_for_firestore_v2(task)
          processed_tasks.append(processed_task)
        except Exception as task_error:
          logger.error(f"Error processing update task {i}: {task_error}")
          processed_tasks.append(serialize_for_firestore(task))

      agent_data["tasks"] = processed_tasks

    # Apply final serialization
    result = serialize_for_firestore(agent_data)

    # Validate JSON serialization
    json.dumps(result, default=str)

    return result

  except Exception as e:
    logger.error(f"Error mapping agent update to Firestore: {e}", exc_info=True)
    raise HTTPException(status_code=422, detail=f"Serialization error: {str(e)}")

app = FastAPI(
  title="Synapy Agent Service",
  description="AI agent management and conversation processing service using Firestore",
  version="1.0.0"
)

app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"]
)

# Add validation error handler to provide detailed error messages
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
  logger.error(f"Validation error for {request.method} {request.url}: {exc}")
  return JSONResponse(
    status_code=422,
    content={
      "detail": "Validation error",
      "errors": exc.errors(),
      "body": str(exc)
    }
  )


@app.on_event("startup")
async def startup_event():
  """Startup event handler."""
  port = os.environ.get('PORT', '8080')
  logger.info(f"Server running on port {port}")

  # Validate database connection
  if database.is_available():
    logger.info("Database connection validated successfully")
  else:
    logger.warning("Database not available - check database configuration")

  logger.info("Server ready")


class CreateAgentPayload(BaseModel):
  agent_config: AgentModel
  agent_prompts: Optional[Dict[str, Dict[str, str]]]


class AgentResponseData(BaseModel):
  """Response model for agent data that maps Firestore documents to frontend-expected structure."""
  agent_id: str
  agent_config: Dict[str, Any]
  agent_prompts: Optional[Dict[str, Any]] = None
  assistant_status: Optional[str] = None


def map_firestore_to_response(agent_id: str, firestore_data: Dict[str, Any]) -> AgentResponseData:
  """Map Firestore document to AgentResponseData for consistent API responses."""
  try:
    # Extract agent_prompts and assistant_status from the root level
    agent_prompts = firestore_data.pop("agent_prompts", {})
    assistant_status = firestore_data.pop("assistant_status", None)

    # The remaining data becomes agent_config
    agent_config = firestore_data.copy()

    return AgentResponseData(
      agent_id=agent_id,
      agent_config=agent_config,
      agent_prompts=agent_prompts,
      assistant_status=assistant_status
    )
  except Exception as e:
    logger.error(f"Error mapping Firestore data to response for agent {agent_id}: {e}")
    # Fallback: wrap all data in agent_config
    return AgentResponseData(
      agent_id=agent_id,
      agent_config=firestore_data,
      agent_prompts={},
      assistant_status=None
    )


def map_firestore_to_assistant_manager_format(firestore_data: Dict[str, Any]) -> Dict[str, Any]:
  """Map Firestore document to format expected by AssistantManager (reverse of map_to_firestore)."""
  try:
    # Create a copy to avoid modifying the original
    agent_config = firestore_data.copy()

    # Convert pipeline boolean maps back to arrays for AssistantManager
    if "tasks" in agent_config and isinstance(agent_config["tasks"], list):
      for task in agent_config["tasks"]:
        if isinstance(task, dict) and "toolchain" in task:
          toolchain = task["toolchain"]
          if "pipelines" in toolchain and isinstance(toolchain["pipelines"], list):
            # Convert boolean maps back to arrays
            toolchain["pipelines"] = convert_pipelines_from_firestore_format(toolchain["pipelines"])

    return agent_config

  except Exception as e:
    logger.error(f"Error converting Firestore data to AssistantManager format: {e}")
    # Return original data as fallback
    return firestore_data


# API Endpoints
@app.get("/")
def root():
    return {"message": "Service is running"}


@app.get("/health")
async def health_check():
  """Health check endpoint for Cloud Run with database abstraction."""
  try:
    # Check database availability through abstraction
    database_status = "available" if database.is_available() else "unavailable"

    # Check environment variables
    required_env_vars = ["GOOGLE_CLOUD_PROJECT"]
    env_status = {}
    for var in required_env_vars:
      env_status[var] = "set" if os.getenv(var) else "missing"

    return {
      "status": "healthy",
      "service": "agent-service",
      "database": database_status,
      "environment": env_status,
      "port": os.environ.get("PORT", "8080"),
      "infrastructure": "abstracted"
    }
  except Exception as e:
    logger.error(f"Health check failed: {e}")
    return {
      "status": "unhealthy",
      "service": "agent-service",
      "error": str(e)
    }


@app.get("/server-info")
async def get_server_info(request: Request):
  """Returns server URL information for client auto-discovery."""
  # Get the host from the request headers (Cloud Run provides this)
  host = request.headers.get("host")
  scheme = "https" if request.headers.get("x-forwarded-proto") == "https" else "http"

  # For Cloud Run, construct the WebSocket URL
  ws_scheme = "wss" if scheme == "https" else "ws"
  server_url = f"{ws_scheme}://{host}"

  return {
    "server_url": server_url,
    "websocket_endpoint": f"{server_url}/chat/v1/{{agent_id}}",
    "environment": "production"
  }


@app.get("/agent/{agent_id}")
async def get_agent(agent_id: str):
  """Fetches an agent's information by ID using abstracted database interface."""
  try:
    # Validate UUID format
    ValidationUtils.validate_uuid(agent_id)

    # Use abstracted database interface
    collection = os.getenv('AGENTS_COLLECTION', 'assistants')
    agent_data = await database.get_document(collection, agent_id)
    if not agent_data:
      raise ServiceResponse.error(AGENT_NOT_FOUND, "AGENT_NOT_FOUND", HTTP_NOT_FOUND)

    # Map database document to consistent response structure
    agent_response = map_firestore_to_response(agent_id, agent_data)
    return ServiceResponse.success(agent_response.model_dump())

  except HTTPException:
    raise
  except Exception as e:
    logger.error(f"Error fetching agent {agent_id}: {e}", exc_info=True)
    raise ServiceResponse.error("Failed to fetch agent", "FETCH_ERROR", 500)


async def process_extraction_tasks(data_for_db: Dict[str, Any]) -> None:
  """Process extraction tasks for agent data."""
  tasks = data_for_db.get('tasks', [])
  if not tasks:
    return

  logger.info("Setting up follow up tasks")
  for index, task in enumerate(tasks):
    if task.get('task_type') != "extraction":
      continue

    extraction_prompt_llm = os.getenv("EXTRACTION_PROMPT_GENERATION_MODEL")
    if not extraction_prompt_llm:
      raise HTTPException(status_code=500, detail="Extraction model not configured")

    extraction_prompt_generation_llm = LiteLLM(model=extraction_prompt_llm, max_tokens=2000)
    extraction_details = task.get("tools_config", {}).get("llm_agent", {}).get("extraction_details", "")

    extraction_prompt = await extraction_prompt_generation_llm.generate(
      messages=[
        {'role': 'system', 'content': EXTRACTION_PROMPT_GENERATION_PROMPT},
        {'role': 'user', 'content': extraction_details}
      ]
    )
    data_for_db["tasks"][index]["tools_config"]["llm_agent"]['extraction_json'] = extraction_prompt

@app.post("/agent")
async def create_agent(agent_data: CreateAgentPayload):
  """Creates a new agent using abstracted database interface."""
  check_database_available()
  try:
    logger.info('Creating agent...')

    # Map data with detailed error handling
    try:
      data_for_db = map_to_firestore(agent_data)
      logger.debug(f"Mapped data keys: {list(data_for_db.keys()) if isinstance(data_for_db, dict) else 'Not a dict'}")
    except Exception as mapping_error:
      logger.error(f"Error in data mapping: {mapping_error}", exc_info=True)
      raise HTTPException(status_code=422, detail=f"Data mapping failed: {str(mapping_error)}")

    # Process extraction tasks
    try:
      await process_extraction_tasks(data_for_db)
    except Exception as extraction_error:
      logger.error(f"Error in extraction processing: {extraction_error}", exc_info=True)
      # Continue without extraction processing

    # Store using abstracted database interface
    try:
      collection = os.getenv('AGENTS_COLLECTION', 'assistants')
      agent_id = await database.add_document(collection, data_for_db)
      logger.info(f"Successfully created agent {agent_id}")
    except Exception as storage_error:
      logger.error(f"Database storage error: {storage_error}", exc_info=True)
      logger.error(f"Data structure causing error: {json.dumps(data_for_db, indent=2, default=str)}")
      raise HTTPException(status_code=500, detail=f"Database storage failed: {str(storage_error)}")

    # Store conversation details file for WebSocket endpoint (similar to quickstart_server.py)
    try:
      stored_prompt_file_path = f"{agent_id}/conversation_details.json"
      agent_prompts = agent_data.agent_prompts or {}
      bucket_name = BUCKET_NAME_PROMPTS or "default-bucket"  # Provide fallback bucket name
      success = await store_file(
        bucket_name=bucket_name,
        file_key=stored_prompt_file_path,
        file_data=agent_prompts,
        content_type="json"
      )
      if success:
        logger.info(f"Successfully stored conversation details for agent {agent_id}")
      else:
        logger.warning(f"Failed to store conversation details for agent {agent_id}")
    except Exception as storage_error:
      logger.error(f"Storage error for conversation details: {storage_error}", exc_info=True)
      # Don't fail the entire request if storage fails, just log the error

    return {"agent_id": agent_id, "state": "created"}

  except ValidationError as e:
    logger.error(f"Validation error in create_agent: {e}")
    raise HTTPException(status_code=422, detail=f"Validation error: {str(e)}")
  except HTTPException:
    raise
  except Exception as e:
    logger.error(f"Unexpected error creating agent: {e}", exc_info=True)
    raise HTTPException(status_code=500, detail="Failed to create agent")


@app.put("/agent/{agent_id}")
async def edit_agent(agent_id: str, agent_data: CreateAgentPayload = Body(...)):
  """Edits an existing agent using abstracted database interface."""
  try:
    # Check if agent exists using abstracted database
    collection = os.getenv('AGENTS_COLLECTION', 'assistants')
    existing_agent = await database.get_document(collection, agent_id)
    if not existing_agent:
      raise HTTPException(status_code=HTTP_NOT_FOUND, detail=AGENT_NOT_FOUND)

    new_data = map_agent_update_to_firestore(agent_data)
    new_data["assistant_status"] = AGENT_STATE_UPDATED
    logger.info(f"Updating agent {agent_id}")

    await process_extraction_tasks(new_data)
    await database.set_document(collection, agent_id, new_data)

    # Store conversation details file for WebSocket endpoint (similar to quickstart_server.py)
    try:
      stored_prompt_file_path = f"{agent_id}/conversation_details.json"
      agent_prompts = agent_data.agent_prompts or {}
      bucket_name = BUCKET_NAME_PROMPTS or "default-bucket"  # Provide fallback bucket name
      success = await store_file(
        bucket_name=bucket_name,
        file_key=stored_prompt_file_path,
        file_data=agent_prompts,
        content_type="json"
      )
      if success:
        logger.info(f"Successfully updated conversation details for agent {agent_id}")
      else:
        logger.warning(f"Failed to update conversation details for agent {agent_id}")
    except Exception as storage_error:
      logger.error(f"Storage error for conversation details update: {storage_error}", exc_info=True)
      # Don't fail the entire request if storage fails, just log the error

    return {"agent_id": agent_id, "state": "updated"}

  except ValidationError as e:
    logger.error(f"Validation error in edit_agent: {e}")
    raise HTTPException(status_code=422, detail=f"Validation error: {str(e)}")
  except HTTPException:
    raise
  except Exception as e:
    logger.error(f"Error updating agent {agent_id}: {e}", exc_info=True)
    raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR)


@app.delete("/agent/{agent_id}")
async def delete_agent(agent_id: str):
  """Deletes an agent using abstracted database interface."""
  try:
    collection = os.getenv('AGENTS_COLLECTION', 'assistants')
    # Check if agent exists using abstracted database
    existing_agent = await database.get_document(collection, agent_id)
    if not existing_agent:
      raise HTTPException(status_code=HTTP_NOT_FOUND, detail=AGENT_NOT_FOUND)

    await database.delete_document(collection, agent_id)
    return {"agent_id": agent_id, "state": AGENT_STATE_DELETED}

  except HTTPException:
    raise
  except Exception as e:
    logger.error(f"Error deleting agent {agent_id}: {e}", exc_info=True)
    raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR)


@app.get("/all")
async def get_all_agents():
  """Fetches all agents using abstracted database interface."""
  try:
    collection = os.getenv('AGENTS_COLLECTION', 'assistants')
    # Query all agents using abstracted database
    agent_docs = await database.query_documents(collection)
    logger.info(f"Found {len(agent_docs)} agents in collection {collection}: {agent_docs}")

    agents = []
    for doc in agent_docs:
      # Map database document to consistent response structure
      agent_response = map_firestore_to_response(doc["id"], doc)
      agents.append(agent_response.model_dump())

    return {"agents": agents}

  except Exception as e:
    logger.error(f"Error fetching all agents: {e}", exc_info=True)
    raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR)


def cleanup_websocket(websocket: WebSocket) -> None:
  """Remove websocket from active connections."""
  if websocket in active_websockets:
    active_websockets.remove(websocket)

async def get_agent_config(agent_id: str, websocket: WebSocket) -> Optional[Dict[str, Any]]:
  """Retrieve agent configuration using abstracted database interface."""
  if not database.is_available():
    await websocket.close(code=1011, reason="Database unavailable")
    return None

  try:
    collection = os.getenv('AGENTS_COLLECTION', 'assistants')
    agent_data = await database.get_document(collection, agent_id)
    if not agent_data:
      await websocket.close(code=1008, reason="Agent not found")
      return None

    # Convert database document to AssistantManager-compatible format
    agent_config = map_firestore_to_assistant_manager_format(agent_data)

    # Force WebSocket-compatible input/output providers for WebSocket connections
    # This ensures we use DefaultInputHandler instead of telephony handlers
    if "tasks" in agent_config and isinstance(agent_config["tasks"], list):
      for task in agent_config["tasks"]:
        if isinstance(task, dict) and "tools_config" in task:
          tools_config = task["tools_config"]

          # Add input configuration if missing
          if "input" not in tools_config:
            tools_config["input"] = {
              "provider": "default",
              "format": "wav"
            }
          else:
            # Override input provider to use default handler for WebSocket
            tools_config["input"]["provider"] = "default"

          # Add output configuration if missing
          if "output" not in tools_config:
            tools_config["output"] = {
              "provider": "default",
              "format": "wav"
            }
          else:
            # Override output provider to use default handler for WebSocket
            tools_config["output"]["provider"] = "default"

    logger.info(f"Retrieved and converted agent config for {agent_id}")
    return agent_config

  except Exception as e:
    logger.error(f"Error retrieving agent config: {e}", exc_info=True)
    await websocket.close(code=1011, reason=INTERNAL_SERVER_ERROR)
    return None

@app.websocket("/chat/v1/{agent_id}")
async def websocket_endpoint(agent_id: str, websocket: WebSocket):
  logger.info(f"WebSocket connection initiated for agent {agent_id}")
  await websocket.accept()
  active_websockets.append(websocket)

  agent_config = await get_agent_config(agent_id, websocket)
  if not agent_config:
    return

  # Configure WebSocket connection as web-based call for proper handler initialization
  assistant_manager = AssistantManager(
    agent_config,
    websocket,
    agent_id,
    # turn_based_conversation=True,  # Enable turn-based conversation for WebSocket text processing
    # is_web_based_call=True,  # This ensures proper WebSocket handler setup
    # enforce_streaming=False  # Disable streaming for WebSocket connections
  )

  try:
    async for index, task_output in assistant_manager.run(local=False):
      logger.info(f"Task output {index}: {task_output}")
  except WebSocketDisconnect:
    cleanup_websocket(websocket)
    logger.info(f"WebSocket disconnected for agent {agent_id}")
  except Exception as e:
    logger.error(f"Error in WebSocket execution for agent {agent_id}: {e}", exc_info=True)
    cleanup_websocket(websocket)


if __name__ == "__main__":
  import uvicorn
  port = int(os.environ.get("PORT", 8080))
  logger.info(f"Starting server on http://0.0.0.0:{port}")
  uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
