"""
Communications Service - Technology-Agnostic Communication Management

This service handles all communication operations including WhatsApp conversations,
agent activation/deactivation, and message routing. Currently uses Twilio but
designed to be easily swappable with other communication providers.
"""

import os
import asyncio
import json
import websockets
import uuid
from typing import Dict, Optional, Any, AsyncGenerator
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request, Query
from fastapi.responses import PlainTextResponse, JSONResponse, StreamingResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, ValidationError
from dotenv import load_dotenv

# Shared service imports
from .shared import (
    get_database, get_message_broker, create_event_message,
    ValidationUtils, DataUtils, LoggingUtils, ConfigUtils, ServiceResponse, EventTypes, Topics
)
from .shared.shared_constants import *

load_dotenv()

# Configure logging
logger = LoggingUtils.setup_logger(__name__, ConfigUtils.get_env_var(ENV_LOG_LEVEL, DEFAULT_LOG_LEVEL))

# Initialize services
database = get_database()
message_broker = get_message_broker()

logger.info("Communications Service starting...")

# FastAPI app configuration
app = FastAPI(
    title="Communications Service",
    description="Technology-agnostic communication management microservice",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add validation error handler to provide detailed error messages
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    logger.error(f"Validation error for {request.method} {request.url}: {exc}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": exc.errors(),
            "body": str(exc)
        }
    )

@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    port = os.environ.get('PORT', '8083')
    logger.info(f"Communications Service running on port {port}")

    # Validate database connection
    if database.is_available():
        logger.info("Database connection validated successfully")
    else:
        logger.warning("Database not available - check database configuration")

    # Validate message broker connection
    if message_broker.is_available():
        logger.info("Message broker connection validated successfully")
    else:
        logger.warning("Message broker not available - check configuration")

    # Validate communication provider
    if comm_provider.is_available():
        logger.info("Communication provider validated successfully")
    else:
        logger.warning("Communication provider not available - check Twilio configuration")


# Communication Provider Abstraction
class CommunicationProviderInterface:
    """Abstract interface for communication providers."""

    async def send_message(self, to: str, message: str, from_number: str = None) -> Dict[str, Any]:
        """Send a message to a recipient."""
        raise NotImplementedError

    async def validate_webhook(self, request: Request) -> bool:
        """Validate incoming webhook request."""
        raise NotImplementedError

    def is_available(self) -> bool:
        """Check if provider is available."""
        raise NotImplementedError


class TwilioProvider(CommunicationProviderInterface):
    """Twilio implementation of communication provider."""

    def __init__(self):
        self.client = None
        self.account_sid = ConfigUtils.get_env_var("TWILIO_ACCOUNT_SID")
        self.auth_token = ConfigUtils.get_env_var("TWILIO_AUTH_TOKEN")
        self.whatsapp_number = ConfigUtils.get_env_var("TWILIO_WHATSAPP_NUMBER")
        self._initialize_client()

    def _initialize_client(self):
        """Initialize Twilio client."""
        try:
            if self.account_sid and self.auth_token:
                from twilio.rest import Client
                self.client = Client(self.account_sid, self.auth_token)
                logger.info("✅ Twilio client initialized")
            else:
                logger.warning("Twilio credentials not configured")
        except ImportError:
            logger.error("Twilio library not available")
        except Exception as error:
            logger.error(f"Failed to initialize Twilio client: {error}")

    def is_available(self) -> bool:
        """Check if Twilio client is available."""
        return self.client is not None

    async def send_message(self, to: str, message: str, from_number: str = None) -> Dict[str, Any]:
        """Send WhatsApp message via Twilio."""
        if not self.client:
            raise Exception("Twilio client not available")

        try:
            clean_to = ValidationUtils.validate_phone_number(to)
            from_number = from_number or self.whatsapp_number

            if not from_number:
                raise Exception("WhatsApp number not configured")

            # Send message
            message_obj = self.client.messages.create(
                body=message,
                from_=f"whatsapp:{from_number}",
                to=f"whatsapp:{clean_to}"
            )

            return {
                "message_id": message_obj.sid,
                "status": message_obj.status,
                "to": clean_to,
                "from": from_number,
                "provider": "twilio"
            }

        except Exception as error:
            logger.error(f"Error sending Twilio message: {error}")
            raise Exception(f"Message sending failed: {str(error)}")

    async def validate_webhook(self, request: Request) -> bool:
        """Validate Twilio webhook signature."""
        try:
            from twilio.request_validator import RequestValidator

            if not self.auth_token:
                logger.warning("No Twilio auth token available for webhook validation")
                if os.getenv("ENVIRONMENT") == "development":
                    logger.warning("⚠️ DEVELOPMENT MODE: Allowing webhook without signature validation")
                    return True
                return False

            validator = RequestValidator(self.auth_token)

            # Get the URL exactly as Twilio expects (force https if proxy downgraded it)
            url = str(request.url)
            if url.startswith("http://"):
                url = url.replace("http://", "https://", 1)

            signature = request.headers.get("X-Twilio-Signature", "")
            if not signature:
                logger.warning("No X-Twilio-Signature header found")
                return False

            # Twilio sends form-encoded payload
            form_data = await request.form()
            params = dict(form_data)

            is_valid = validator.validate(url, params, signature)

            if is_valid:
                logger.info("✅ Webhook signature validation successful")
            else:
                logger.warning("❌ Webhook signature validation failed")
                logger.warning(f"URL used for validation: {url}")
                logger.warning(f"Signature: {signature}")
                logger.warning(f"Params: {params}")

            return is_valid

        except Exception as error:
            logger.error(f"Error validating Twilio webhook: {error}")
            return False




# Initialize communication provider
comm_provider = TwilioProvider()

# Service URLs
AGENT_SERVICE_URL = ConfigUtils.get_env_var("AGENT_SERVICE_URL", "http://localhost:8080")


class AgentWebSocketManager:
    """Manages WebSocket connections to agent-service for real-time communication."""

    def __init__(self):
        # Dictionary to store active WebSocket connections
        # Key format: "{agent_id}_{phone_number}" for per-conversation context
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.connection_lock = asyncio.Lock()
        # Dictionary to store pending message responses
        # Key format: "message_id" -> asyncio.Future
        self.pending_responses: Dict[str, asyncio.Future] = {}

    def _get_connection_key(self, agent_id: str, phone_number: str) -> str:
        """Generate unique connection key for agent-phone combination."""
        clean_phone = phone_number.replace('+', '').replace(' ', '').replace('-', '')
        return f"{agent_id}_{clean_phone}"

    async def get_or_create_connection(self, agent_id: str, phone_number: str) -> Optional[Dict[str, Any]]:
        """Get existing connection or create new one for agent-phone combination."""
        connection_key = self._get_connection_key(agent_id, phone_number)

        logger.info(f"Connection key: {connection_key}")

        async with self.connection_lock:
            # Check if connection exists and is still active
            if connection_key in self.connections:
                logger.info(f"Connections [{self.connections.__len__}]: {self.connections}")
                connection_info = self.connections[connection_key]
                websocket = connection_info.get('websocket')

                # Check if WebSocket is still open
                # Use close_code to check if connection is still active (None means open)
                if websocket and websocket.close_code is None:
                    logger.info(f"Reusing existing WebSocket connection for {agent_id} - {phone_number}")
                    return connection_info
                else:
                    # Clean up closed connection
                    logger.info(f"Cleaning up closed WebSocket connection for {agent_id} - {phone_number} (close_code: {getattr(websocket, 'close_code', 'unknown')})")
                    del self.connections[connection_key]

            # Create new connection
            try:
                ws_url = f"{AGENT_SERVICE_URL}/chat/v1/{agent_id}".replace('http://', 'ws://').replace('https://', 'wss://')
                logger.info(f"Creating new WebSocket connection to {ws_url}")

                websocket = await websockets.connect(
                    ws_url,
                    ping_interval=30,   # Send ping every 30 seconds
                    ping_timeout=60,    # Wait 60 seconds for pong response
                    close_timeout=30,   # Wait 30 seconds for close handshake
                    max_size=2**20,     # 1MB max message size
                    max_queue=32       # Max queued messages
                )

                # Create connection info
                connection_info = {
                    'websocket': websocket,
                    'agent_id': agent_id,
                    'phone_number': phone_number,
                    'connection_key': connection_key,
                    'created_at': datetime.now(),
                    'message_count': 0
                }

                # Start persistent listener for all responses on this connection
                listener_task = asyncio.create_task(self._listen_for_all_responses(connection_info))
                connection_info['listener_task'] = listener_task

                self.connections[connection_key] = connection_info

                logger.info(f"✅ Created WebSocket connection of type {type(websocket)} for {agent_id} - {phone_number}")
                return connection_info

            except Exception as error:
                logger.error(f"❌ Failed to create WebSocket connection for {agent_id} - {phone_number}: {error}")
                return None

    async def send_message_to_agent(self, agent_id: str, phone_number: str, message: str, message_data: Dict[str, Any] = None) -> bool:
        """Send message to agent via WebSocket."""
        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                connection_info = await self.get_or_create_connection(agent_id, phone_number)
                if not connection_info:
                    logger.error(f"No WebSocket connection available for {agent_id} - {phone_number}")
                    return False

                websocket = connection_info['websocket']

                # Check if connection is still valid before sending
                if websocket.close_code is not None:
                    logger.warning(f"WebSocket connection is closed for {agent_id} - {phone_number}, attempting to reconnect")
                    await self.close_connection(agent_id, phone_number)
                    if retry_count < max_retries:
                        retry_count += 1
                        continue
                    else:
                        return False

                # Prepare message payload for agent-service in the format it expects
                message_id = str(uuid.uuid4())
                payload = {
                    "type": "text",  # Agent-service expects "text" type
                    "data": message,  # Agent-service expects message content in "data" field
                    "phone_number": phone_number,
                    "timestamp": datetime.now().isoformat(),
                    "message_id": message_id,
                    "channel": "whatsapp",
                    "metadata": message_data or {}
                }

                # Create future for response
                response_future = asyncio.Future()
                self.pending_responses[message_id] = response_future

                try:
                    # Send message to agent with timeout
                    await asyncio.wait_for(
                        websocket.send(json.dumps(payload)),
                        timeout=10.0  # 10 second timeout for sending
                    )

                    # Update connection stats
                    connection_info['message_count'] += 1
                    connection_info['last_message_at'] = datetime.now()

                    logger.info(f"📤 Sent message to agent {agent_id} for {phone_number}: {message[:50]}...")

                    # Wait for response (optional - fire and forget for now)
                    # The response will be handled by the persistent listener
                    return True

                except Exception as send_error:
                    # Clean up the pending response future if send fails
                    if message_id in self.pending_responses:
                        del self.pending_responses[message_id]
                    raise send_error

            except asyncio.TimeoutError:
                logger.error(f"Timeout sending message to agent {agent_id} for {phone_number}")
                await self.close_connection(agent_id, phone_number)
                if retry_count < max_retries:
                    retry_count += 1
                    logger.info(f"Retrying message send ({retry_count}/{max_retries})")
                    continue
                else:
                    return False
            except (websockets.exceptions.ConnectionClosed, websockets.exceptions.WebSocketException) as ws_error:
                logger.error(f"WebSocket error sending message to agent {agent_id} for {phone_number}: {ws_error}")
                await self.close_connection(agent_id, phone_number)
                if retry_count < max_retries:
                    retry_count += 1
                    logger.info(f"Retrying message send after WebSocket error ({retry_count}/{max_retries})")
                    continue
                else:
                    return False
            except Exception as error:
                logger.error(f"Error sending message to agent {agent_id} for {phone_number}: {error}")
                await self.close_connection(agent_id, phone_number)
                if retry_count < max_retries:
                    retry_count += 1
                    logger.info(f"Retrying message send after error ({retry_count}/{max_retries})")
                    continue
                else:
                    return False

        return False

    async def _listen_for_all_responses(self, connection_info: Dict[str, Any]):
        """Listen for all responses from agent and route them appropriately."""
        websocket = connection_info['websocket']
        agent_id = connection_info['agent_id']
        phone_number = connection_info['phone_number']
        connection_key = connection_info['connection_key']

        try:
            logger.info(f"👂 Started listening for agent responses: {agent_id} - {phone_number}")

            async for message in websocket:
                try:
                    data = json.loads(message)

                    # Check if this response corresponds to a pending message
                    response_message_id = data.get('message_id')
                    if response_message_id and response_message_id in self.pending_responses:
                        # This is a response to a specific message
                        future = self.pending_responses.pop(response_message_id)
                        if not future.done():
                            future.set_result(data)
                        logger.info(f"📥 Routed response for message {response_message_id} to pending future")

                    # Always handle the response for WhatsApp forwarding
                    await self._handle_agent_response(data, phone_number, agent_id)

                except json.JSONDecodeError as error:
                    logger.error(f"Invalid JSON from agent {agent_id}: {error}")
                except Exception as error:
                    logger.error(f"Error handling agent response from {agent_id}: {error}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 WebSocket connection closed for {agent_id} - {phone_number}")
        except Exception as error:
            logger.error(f"Error in WebSocket listener for {agent_id} - {phone_number}: {error}")
        finally:
            # Clean up connection and pending responses
            async with self.connection_lock:
                if connection_key in self.connections:
                    del self.connections[connection_key]
                    logger.info(f"🧹 Cleaned up WebSocket connection for {agent_id} - {phone_number}")

            # Cancel any pending responses for this connection
            pending_to_cancel = [
                msg_id for msg_id, future in self.pending_responses.items()
                if not future.done()
            ]
            for msg_id in pending_to_cancel:
                future = self.pending_responses.pop(msg_id, None)
                if future and not future.done():
                    future.cancel()
                    logger.info(f"🚫 Cancelled pending response for message {msg_id}")

    async def _handle_agent_response(self, data: Dict[str, Any], phone_number: str, agent_id: str):
        """Handle response from agent and send via WhatsApp."""
        try:
            message_type = data.get('type')

            if message_type == 'text':
                message_content = data.get('data', '')
                if message_content:
                    # Send response via WhatsApp using the communication provider
                    await comm_provider.send_message(
                        to=phone_number,
                        message=message_content
                    )

                    # Store the agent's response in conversation
                    agent_message_data = {
                        'type': 'agent_response',
                        'to': phone_number,
                        'content': message_content,
                        'direction': 'outbound',
                        'message_id': data.get('message_id', str(uuid.uuid4())),
                        'timestamp': datetime.now().isoformat(),
                        'agent_id': agent_id,
                        'channel': 'whatsapp',
                        'meta_info': data.get('meta_info', {})
                    }

                    await store_conversation_message(phone_number, agent_message_data)

                    logger.info(f"📥 Forwarded agent response to {phone_number}: {message_content[:50]}...")

            elif message_type == 'audio':
                # TODO: Handle audio responses (future feature)
                logger.info(f"Received audio response from agent {agent_id} (not implemented)")

            elif message_type == 'ack':
                # Agent-service acknowledgment - no action needed
                logger.debug(f"Received acknowledgment from agent {agent_id}")

            elif message_type == 'clear':
                # Agent-service interruption/clear message - no action needed for WhatsApp
                logger.debug(f"Received clear message from agent {agent_id}")

            elif message_type == 'error':
                logger.error(f"Agent {agent_id} returned error: {data.get('message', 'Unknown error')}")

            else:
                logger.debug(f"Unhandled message type from agent {agent_id}: {message_type}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received from agent {agent_id}: {message_content}")
        except Exception as e:
            logger.error(f"Error processing response from agent {agent_id}: {e}")

    async def close_connection(self, agent_id: str, phone_number: str):
        """Close WebSocket connection for specific agent-phone combination."""
        connection_key = self._get_connection_key(agent_id, phone_number)

        async with self.connection_lock:
            if connection_key in self.connections:
                connection_info = self.connections[connection_key]
                websocket = connection_info.get('websocket')
                listener_task = connection_info.get('listener_task')

                # Cancel the listener task with timeout
                if listener_task and not listener_task.done():
                    listener_task.cancel()
                    try:
                        # Wait for task cancellation with timeout
                        await asyncio.wait_for(listener_task, timeout=5.0)
                    except (asyncio.CancelledError, asyncio.TimeoutError):
                        logger.info(f"Listener task cancelled/timed out for {agent_id} - {phone_number}")
                    except Exception as error:
                        logger.error(f"Error cancelling listener task for {agent_id} - {phone_number}: {error}")

                # Close the WebSocket with timeout
                if websocket and websocket.close_code is None:
                    try:
                        # Close WebSocket with timeout to prevent hanging
                        await asyncio.wait_for(websocket.close(), timeout=10.0)
                        logger.info(f"🔌 Closed WebSocket connection for {agent_id} - {phone_number}")
                    except asyncio.TimeoutError:
                        logger.warning(f"WebSocket close timed out for {agent_id} - {phone_number}, forcing cleanup")
                    except Exception as error:
                        logger.error(f"Error closing WebSocket for {agent_id} - {phone_number}: {error}")

                # Always remove from connections dict, even if close failed
                del self.connections[connection_key]
                logger.info(f"🧹 Cleaned up connection entry for {agent_id} - {phone_number}")

    async def close_all_connections_for_agent(self, agent_id: str):
        """Close all WebSocket connections for a specific agent."""
        connections_to_close = []

        async with self.connection_lock:
            for connection_key, connection_info in self.connections.items():
                if connection_info['agent_id'] == agent_id:
                    connections_to_close.append((connection_key, connection_info))

        # Close connections outside the lock to avoid deadlock
        for connection_key, connection_info in connections_to_close:
            websocket = connection_info.get('websocket')
            listener_task = connection_info.get('listener_task')
            phone_number = connection_info.get('phone_number')

            # Cancel the listener task with timeout
            if listener_task and not listener_task.done():
                listener_task.cancel()
                try:
                    # Wait for task cancellation with timeout
                    await asyncio.wait_for(listener_task, timeout=5.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    logger.info(f"Listener task cancelled/timed out for {agent_id} - {phone_number}")
                except Exception as error:
                    logger.error(f"Error cancelling listener task for {agent_id} - {phone_number}: {error}")

            # Close the WebSocket with timeout
            if websocket and websocket.close_code is None:
                try:
                    # Close WebSocket with timeout to prevent hanging
                    await asyncio.wait_for(websocket.close(), timeout=10.0)
                    logger.info(f"🔌 Closed WebSocket connection for {agent_id} - {phone_number}")
                except asyncio.TimeoutError:
                    logger.warning(f"WebSocket close timed out for {agent_id} - {phone_number}, forcing cleanup")
                except Exception as error:
                    logger.error(f"Error closing WebSocket for {agent_id} - {phone_number}: {error}")

            # Always remove from connections dict, even if close failed
            async with self.connection_lock:
                if connection_key in self.connections:
                    del self.connections[connection_key]
                    logger.info(f"🧹 Cleaned up connection entry for {agent_id} - {phone_number}")

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about active connections."""
        return {
            'active_connections': len(self.connections),
            'connections': [
                {
                    'agent_id': info['agent_id'],
                    'phone_number': info['phone_number'],
                    'created_at': info['created_at'].isoformat(),
                    'message_count': info['message_count'],
                    'last_message_at': info.get('last_message_at', info['created_at']).isoformat()
                }
                for info in self.connections.values()
            ]
        }


# Initialize WebSocket manager
agent_ws_manager = AgentWebSocketManager()


# Pydantic models
class AgentActivationRequest(BaseModel):
    phone_number: str = Field(..., description="Contact phone number")
    agent_id: str = Field(..., description="Agent ID to activate")
    user_id: Optional[str] = Field(None, description="User ID for user-specific operations")
    initial_message: Optional[str] = Field(None, description="Initial message to send")


class MessageRequest(BaseModel):
    to: str = Field(..., description="Recipient phone number")
    message: str = Field(..., description="Message content")
    from_number: Optional[str] = Field(None, description="Sender number")


# WhatsApp conversation and message models
class ConversationMessage(BaseModel):
    message_id: str
    content: str
    direction: str  # 'inbound' or 'outbound'
    from_number: str
    to_number: str
    status: str
    timestamp: str
    stored_at: str
    provider_data: Optional[Dict[str, Any]] = None


class WhatsAppConversation(BaseModel):
    conversation_id: str
    phone_number: str
    agent_id: str
    messages: list[ConversationMessage]
    message_count: int
    last_message_at: str
    created_at: str
    updated_at: str


class WhatsAppActiveAgent(BaseModel):
    agent_doc_id: str
    phone_number: str
    agent_id: str
    contact_id: Optional[str]
    status: str
    created_at: str
    updated_at: str


# Helper functions for conversation and contact management
async def get_or_create_conversation(phone_number: str, agent_id: str) -> str:
    """Get existing conversation or create a new one for a phone number and agent."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)
        collection = os.getenv('CONVERSATIONS_COLLECTION', 'conversations')

        # Try to find existing conversation for this phone number and agent
        existing_conversations = await database.query_documents(
            collection,
            filters={"phone_number": clean_phone, "agent_id": agent_id},
            limit=1,
            order_by="created_at",
            order_direction="desc"
        )

        if existing_conversations:
            return existing_conversations[0]['conversation_id']

        # Create new conversation using Firestore's auto-generated ID
        now = datetime.now().isoformat()

        conversation_data = {
            'phone_number': clean_phone,
            'agent_id': agent_id,
            'messages': [],
            'message_count': 0,
            'last_message_at': now,
            'created_at': now,
            'updated_at': now
        }

        # Use Firestore's native add_document method for auto-generated unique ID
        conversation_id = await database.add_document(collection, conversation_data)

        # Update the document to include its own ID for easier reference
        conversation_data['conversation_id'] = conversation_id
        await database.set_document(collection, conversation_id, conversation_data)

        logger.info(f"Created new conversation {conversation_id} for {clean_phone} with agent {agent_id}")

        return conversation_id

    except Exception as error:
        logger.error(f"Error getting/creating conversation for {phone_number}: {error}")
        return None

async def get_contact_id_by_phone(phone_number: str, user_id: str) -> Optional[str]:
    """Get contact ID by phone number from contact service."""
    try:
        import aiohttp
        contact_service_url = os.getenv('CONTACT_SERVICE_URL', 'http://localhost:8081')
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        async with aiohttp.ClientSession() as session:
            url = f"{contact_service_url}/contact/lookup/{clean_phone}?user_id={user_id}"
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('data', {}).get('contact_id')
                elif response.status == 404:
                    return None
                else:
                    logger.error(f"Contact service error: {response.status}")
                    return None
    except Exception as error:
        logger.error(f"Error getting contact ID by phone: {error}")
        return None




# Database helper functions
async def get_active_agent(phone_number: str, user_id: Optional[str] = None) -> Optional[str]:
    """Get the active agent ID for a phone number."""
    try:
        collection = os.getenv('ACTIVE_AGENTS_COLLECTION', 'active_assistants')
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        # Query for active agent by phone number
        agents = await database.query_documents(
            collection,
            filters={"phone_number": clean_phone, "status": "active"},
            limit=1,
            order_by="created_at",
            order_direction="desc"
        )

        if agents:
            return agents[0].get('agent_id')

        # Fallback to old structure for backward compatibility
        if user_id:
            contact_id = await get_contact_id_by_phone(clean_phone, user_id)
            if contact_id:
                agent_data = await database.get_document(collection, contact_id)
                if agent_data:
                    return agent_data.get('agent_id')

        # Final fallback to phone number lookup
        agent_data = await database.get_document(collection, clean_phone)
        return agent_data.get('agent_id') if agent_data else None

    except Exception as error:
        logger.error(f"Error getting active agent for {phone_number}: {error}")
        return None


async def set_active_agent(phone_number: str, agent_id: str, user_id: Optional[str] = None) -> bool:
    """Set the active agent for a phone number using unique document ID."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)
        ValidationUtils.validate_uuid(agent_id)

        # Get contact ID if user_id is provided
        contact_id = None
        if user_id:
            contact_id = await get_contact_id_by_phone(clean_phone, user_id)

        # Check if there's already an active agent for this phone number
        existing_agent_id = await get_active_agent(clean_phone, user_id)
        if existing_agent_id:
            # Deactivate existing agent first
            await remove_active_agent(clean_phone, user_id)

        now = datetime.now().isoformat()
        # TODO: Update payload to contain 'channel' ('WHATSAPP', 'EMAIL', 'PHONE')
        # when adapted to multi-channel agents
        agent_data = {
            'phone_number': clean_phone,
            'agent_id': agent_id,
            'contact_id': contact_id,
            'status': 'active',
            'created_at': now,
            'updated_at': now
        }

        collection = os.getenv('ACTIVE_AGENTS_COLLECTION', 'active_assistants')
        # Use Firestore's native add_document method for auto-generated unique ID
        agent_doc_id = await database.add_document(collection, agent_data)

        # Create or get conversation for this agent and phone number
        conversation_id = await get_or_create_conversation(clean_phone, agent_id)
        if conversation_id:
            logger.info(f"Created/found conversation {conversation_id} for agent {agent_id} and {clean_phone}")

        # Publish agent activation event
        event_message = create_event_message(
            event_type=EventTypes.AGENT_ACTIVATED,
            aggregate_id=clean_phone,
            payload={
                "phone_number": clean_phone,
                "agent_id": agent_id,
                "agent_doc_id": agent_doc_id,
                "conversation_id": conversation_id,
                "activated_at": now
            },
            source="communications-service"
        )

        await message_broker.publish(Topics.AGENT_EVENTS, event_message)

        logger.info(f"Agent activation published to {Topics.AGENT_EVENTS} topic: {event_message}")

        # Initialize WebSocket connection for the activated agent
        try:
            connection_info = await agent_ws_manager.get_or_create_connection(agent_id, clean_phone)
            if connection_info:
                logger.info(f"🔌 WebSocket connection established for agent {agent_id} - {clean_phone}")
            else:
                logger.warning(f"⚠️  Failed to establish WebSocket connection for agent {agent_id} - {clean_phone}")
        except Exception as ws_error:
            logger.error(f"Error establishing WebSocket connection for agent {agent_id}: {ws_error}")
            # Don't fail the activation if WebSocket connection fails
            # The agent is still activated, just the WebSocket connection failed

        logger.info(f"Activated agent {agent_id} for {clean_phone} with doc ID {agent_doc_id}")
        return True

    except Exception as error:
        logger.error(f"Error setting active agent for {phone_number}: {error}")
        return False


async def remove_active_agent(phone_number: str, user_id: Optional[str] = None) -> Optional[str]:
    """Remove the active agent for a phone number using contact ID when possible."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)
        collection = os.getenv('ACTIVE_AGENTS_COLLECTION', 'active_assistants')

        # Query for active agent by phone number
        agents = await database.query_documents(
            collection,
            filters={"phone_number": clean_phone, "status": "active"},
            limit=1,
            order_by="created_at",
            order_direction="desc"
        )

        agent_id = None
        if agents:
            agent_data = agents[0]
            agent_id = agent_data.get('agent_id')
            agent_doc_id = agent_data.get('agent_doc_id')

            # Update status to inactive instead of deleting
            agent_data['status'] = 'inactive'
            agent_data['updated_at'] = datetime.now().isoformat()

            await database.set_document(collection, agent_doc_id, agent_data)
        else:
            # Fallback to old structure for backward compatibility
            document_key = clean_phone  # Default to phone number
            if user_id:
                contact_id = await get_contact_id_by_phone(clean_phone, user_id)
                if contact_id:
                    document_key = contact_id

            # Get current agent before removing
            agent_data = await database.get_document(collection, document_key)
            if not agent_data and document_key != clean_phone:
                # Try fallback to phone number if contact ID lookup failed
                agent_data = await database.get_document(collection, clean_phone)
                if agent_data:
                    document_key = clean_phone

            agent_id = agent_data.get('agent_id') if agent_data else None

            if agent_data:
                await database.delete_document(collection, document_key)

            # Publish agent deactivation event
            event_message = create_event_message(
                event_type=EventTypes.AGENT_DEACTIVATED,
                aggregate_id=clean_phone,
                payload={
                    "phone_number": clean_phone,
                    "agent_id": agent_id,
                    "deactivated_at": datetime.now().isoformat()
                },
                source="communications-service"
            )

            await message_broker.publish(Topics.AGENT_EVENTS, event_message)

            logger.info(f"Deactivated agent {agent_id} for {clean_phone}")

        # Close WebSocket connection for the deactivated agent with timeout
        if agent_id:
            try:
                # Add timeout to WebSocket cleanup to prevent hanging
                await asyncio.wait_for(
                    agent_ws_manager.close_connection(agent_id, clean_phone),
                    timeout=15.0  # 15 second timeout for WebSocket cleanup
                )
                logger.info(f"🔌 Closed WebSocket connection for agent {agent_id} - {clean_phone}")
            except asyncio.TimeoutError:
                logger.warning(f"WebSocket cleanup timed out for agent {agent_id} - {clean_phone}")
                # Continue with deactivation even if WebSocket cleanup fails
            except Exception as ws_error:
                logger.error(f"Error closing WebSocket connection for agent {agent_id}: {ws_error}")
                # Continue with deactivation even if WebSocket cleanup fails

        return agent_id

    except Exception as error:
        logger.error(f"Error removing active agent for {phone_number}: {error}")
        return None


async def store_conversation_message(phone_number: str, message_data: Dict[str, Any], direction: str = 'inbound', agent_id: str = None) -> bool:
    """Store a conversation message."""
    try:
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        # Get active agent if not provided
        if not agent_id:
            agent_id = await get_active_agent(clean_phone)

        if not agent_id:
            logger.warning(f"No active agent found for {clean_phone}, cannot store message in conversation")
            return False

        # Get or create conversation
        conversation_id = await get_or_create_conversation(clean_phone, agent_id)
        if not conversation_id:
            logger.error(f"Failed to get/create conversation for {clean_phone}")
            return False

        # Create unique message ID
        timestamp = datetime.now().isoformat()
        message_sid = message_data.get('MessageSid') or message_data.get('SmsMessageSid') or message_data.get('sid') or f"msg_{timestamp.replace(':', '').replace('-', '').replace('.', '')}"
        message_id = f"{clean_phone}_{timestamp}_{message_sid}"

        # Prepare message data
        message_obj = {
            'message_id': message_id,
            'content': message_data.get('Body', message_data.get('content', '')),
            'direction': direction,
            'from_number': message_data.get('From', '').replace('whatsapp:', ''),
            'to_number': message_data.get('To', '').replace('whatsapp:', ''),
            'status': message_data.get('SmsStatus', 'received' if direction == 'inbound' else 'sent'),
            'timestamp': timestamp,
            'stored_at': datetime.now().isoformat(),
            'provider_data': DataUtils.clean_dict(message_data)
        }

        # Update conversation with new message
        collection = os.getenv('CONVERSATIONS_COLLECTION', 'conversations')
        conversation = await database.get_document(collection, conversation_id)

        if conversation:
            # Add message to conversation
            messages = conversation.get('messages', [])
            messages.append(message_obj)

            # Update conversation metadata
            conversation.update({
                'messages': messages,
                'message_count': len(messages),
                'last_message_at': timestamp,
                'updated_at': datetime.now().isoformat()
            })

            await database.set_document(collection, conversation_id, conversation)

            # Publish message received event
            event_message = create_event_message(
                event_type=EventTypes.MESSAGE_RECEIVED,
                aggregate_id=clean_phone,
                payload={
                    "contact_id": clean_phone,
                    "conversation_id": conversation_id,
                    "message_id": message_id,
                    "content": message_obj['content'],
                    "direction": direction,
                    "timestamp": timestamp,
                    "agent_id": agent_id
                },
                source="communications-service"
            )

            await message_broker.publish(Topics.MESSAGE_EVENTS, event_message)

            logger.info(f"Stored message {message_id} in conversation {conversation_id} for {clean_phone}")
            return True
        else:
            logger.error(f"Conversation {conversation_id} not found")
            return False

    except Exception as error:
        logger.error(f"Error storing conversation message: {error}")
        return False


async def forward_message_to_agent(phone_number: str, message_data: Dict[str, Any]) -> bool:
    """Forward message to active agent service via WebSocket."""
    try:
        agent_id = await get_active_agent(phone_number)
        if not agent_id:
            logger.info(f"No active agent for {phone_number}")
            return False

        # Extract message content
        message_content = message_data.get('Body', message_data.get('content', ''))
        if not message_content:
            logger.warning(f"No message content to forward for {phone_number}")
            return False

        # Forward message to agent via WebSocket
        success = await agent_ws_manager.send_message_to_agent(
            agent_id=agent_id,
            phone_number=phone_number,
            message=message_content,
            message_data=message_data
        )

        if success:
            logger.info(f"✅ Successfully forwarded message to agent {agent_id} via WebSocket")
        else:
            logger.error(f"❌ Failed to forward message to agent {agent_id} via WebSocket")

        return success

    except Exception as error:
        logger.error(f"Error forwarding message to agent: {error}")
        return False


# API Endpoints
@app.get("/")
def root():
    return {"message": "Service is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database availability
        database_healthy = database.is_available()

        # Check message broker availability
        message_broker_healthy = message_broker.is_available()

        # Check communication provider availability
        comm_provider_healthy = comm_provider.is_available()

        # Determine overall health
        overall_healthy = all([database_healthy, message_broker_healthy, comm_provider_healthy])

        # Get Twilio configuration details
        twilio_config = {
            "available": comm_provider.is_available(),
            "account_sid_configured": bool(comm_provider.account_sid),
            "auth_token_configured": bool(comm_provider.auth_token),
            "whatsapp_number": comm_provider.whatsapp_number or "Not configured"
        }

        health_status = {
            "status": "healthy" if overall_healthy else "unhealthy",
            "service": "communications-service",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "database": "healthy" if database_healthy else "unhealthy",
                "message_broker": "healthy" if message_broker_healthy else "unhealthy",
                "communication_provider": "healthy" if comm_provider_healthy else "unhealthy"
            },
            "twilio": twilio_config
        }

        status_code = 200 if overall_healthy else 503
        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "service": "communications-service",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            status_code=503
        )


@app.post("/webhook/whatsapp")
async def whatsapp_webhook(request: Request):
    """Handle incoming WhatsApp messages from Twilio."""
    from twilio.twiml.messaging_response import MessagingResponse

    try:
        # Validate webhook
        if not await comm_provider.validate_webhook(request):
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=403, detail="Invalid signature")

        # Get form data
        form_data = await request.form()
        message_data = dict(form_data)

        phone_number = message_data.get('From', '').replace('whatsapp:', '')
        if not phone_number:
            raise HTTPException(status_code=400, detail="Missing phone number")

        # Store message
        await store_conversation_message(phone_number, message_data)

        # Forward to agent if active
        await forward_message_to_agent(phone_number, message_data)

        response = MessagingResponse()
        return PlainTextResponse(str(response), status_code=200, media_type='text/xml')

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error processing WhatsApp webhook: {error}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


@app.post("/agent/activate")
async def activate_agent(request: AgentActivationRequest):
    """Activate an agent for a contact."""
    try:
        # Extract user_id from request if available
        user_id = getattr(request, 'user_id', None)
        success = await set_active_agent(request.phone_number, request.agent_id, user_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to activate agent")

        # Send initial message if provided
        if request.initial_message:
            try:
                result = await comm_provider.send_message(
                    to=request.phone_number,
                    message=request.initial_message
                )
                logger.info(f"Initial message sent successfully: {result}")

                # Store outbound message
                await store_conversation_message(
                    request.phone_number,
                    {
                        'content': request.initial_message,
                        'From': comm_provider.whatsapp_number,
                        'To': f"whatsapp:{request.phone_number}",
                        'MessageSid': result.get('sid') if isinstance(result, dict) else None,
                        'SmsStatus': 'sent'
                    },
                    direction='outbound',
                    agent_id=request.agent_id
                )

            except Exception as msg_error:
                logger.error(f"Failed to send initial message: {msg_error}")
                # Don't fail the activation if message sending fails
                # The agent is still activated, just the welcome message failed

        return ServiceResponse.success({"status": "activated"})

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error activating agent: {error}")
        raise HTTPException(status_code=500, detail="Agent activation failed")


@app.delete("/agent/deactivate/{phone_number}")
async def deactivate_agent(
    phone_number: str,
    user_id: Optional[str] = Query(None, description="User ID for user-specific operations")
):
    """Deactivate agent for a contact."""
    try:
        # Add timeout to prevent hanging indefinitely
        agent_id = await asyncio.wait_for(
            remove_active_agent(phone_number, user_id),
            timeout=30.0  # 30 second timeout
        )

        if not agent_id:
            raise HTTPException(status_code=404, detail="No active agent found")

        return ServiceResponse.success({"agent_id": agent_id, "status": "deactivated"})

    except asyncio.TimeoutError:
        logger.error(f"Timeout deactivating agent for {phone_number}")
        raise HTTPException(status_code=504, detail="Agent deactivation timed out")
    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error deactivating agent: {error}")
        raise HTTPException(status_code=500, detail="Agent deactivation failed")


@app.get("/agent/websocket/stats")
async def get_websocket_stats():
    """Get WebSocket connection statistics."""
    try:
        stats = agent_ws_manager.get_connection_stats()
        return ServiceResponse.success(stats)
    except Exception as error:
        logger.error(f"Error getting WebSocket stats: {error}")
        raise HTTPException(status_code=500, detail="Failed to get WebSocket statistics")


@app.delete("/agent/websocket/close/{agent_id}")
async def close_agent_websockets(agent_id: str):
    """Close all WebSocket connections for a specific agent."""
    try:
        ValidationUtils.validate_uuid(agent_id)
        await agent_ws_manager.close_all_connections_for_agent(agent_id)
        return ServiceResponse.success({"agent_id": agent_id, "status": "connections_closed"})
    except Exception as error:
        logger.error(f"Error closing WebSocket connections for agent {agent_id}: {error}")
        raise HTTPException(status_code=500, detail="Failed to close WebSocket connections")


@app.get("/conversation/{agent_id}/{phone_number}")
async def get_conversation_by_agent_and_phone(agent_id: str, phone_number: str, limit: int = 50):
    """Get conversation history for a specific agent and phone number."""
    try:
        # Validate inputs
        ValidationUtils.validate_uuid(agent_id)
        clean_phone = ValidationUtils.validate_phone_number(phone_number)

        # Query conversations with both agent_id and phone_number
        collection = os.getenv('CONVERSATIONS_COLLECTION', 'conversations')
        filters = {
            "phone_number": clean_phone,
            "agent_id": agent_id
        }

        conversations = await database.query_documents(
            collection,
            filters=filters,
            limit=1,  # Get the most recent conversation
            order_by="last_message_at",
            order_direction="desc"
        )

        if conversations:
            conversation = conversations[0]
            messages = conversation.get('messages', [])

            # Apply limit to messages if specified
            if limit and len(messages) > limit:
                messages = messages[-limit:]  # Get the most recent messages

            # Sort messages by timestamp (oldest first for display)
            messages.sort(key=lambda x: x.get("timestamp", ""))

            return ServiceResponse.success({
                "conversation_id": conversation.get('conversation_id'),
                "phone_number": clean_phone,
                "agent_id": conversation.get('agent_id'),
                "messages": messages,
                "message_count": conversation.get('message_count', len(messages)),
                "last_message_at": conversation.get('last_message_at'),
                "created_at": conversation.get('created_at'),
                "updated_at": conversation.get('updated_at')
            })
        else:
            # Return empty conversation structure if no conversation found
            return ServiceResponse.success({
                "conversation_id": None,
                "phone_number": clean_phone,
                "agent_id": agent_id,
                "messages": [],
                "message_count": 0,
                "last_message_at": None,
                "created_at": None,
                "updated_at": None
            })

    except Exception as error:
        logger.error(f"Error getting conversation for agent {agent_id} and phone {phone_number}: {error}")
        raise HTTPException(status_code=500, detail="Failed to get conversation")


@app.get("/conversation/{agent_id}/")
async def get_conversations_by_agent(agent_id: str, limit: int = 50):
    """Get all conversations for a specific agent."""
    try:
        # Validate agent_id
        ValidationUtils.validate_uuid(agent_id)

        # Query all conversations for this agent
        collection = os.getenv('CONVERSATIONS_COLLECTION', 'conversations')
        filters = {"agent_id": agent_id}

        conversations = await database.query_documents(
            collection,
            filters=filters,
            limit=limit,
            order_by="last_message_at",
            order_direction="desc"
        )

        # Format conversations for response
        formatted_conversations = []
        for conversation in conversations:
            messages = conversation.get('messages', [])

            formatted_conversations.append({
                "conversation_id": conversation.get('conversation_id'),
                "phone_number": conversation.get('phone_number'),
                "agent_id": conversation.get('agent_id'),
                "message_count": conversation.get('message_count', len(messages)),
                "last_message_at": conversation.get('last_message_at'),
                "created_at": conversation.get('created_at'),
                "updated_at": conversation.get('updated_at'),
                "last_message": messages[-1] if messages else None
            })

        return ServiceResponse.success({
            "agent_id": agent_id,
            "conversations": formatted_conversations,
            "total_count": len(formatted_conversations)
        })

    except Exception as error:
        logger.error(f"Error getting conversations for agent {agent_id}: {error}")
        raise HTTPException(status_code=500, detail="Failed to get conversations")


@app.get("/agent/active/{phone_number}")
async def get_active_agent_endpoint(phone_number: str):
    """Get active agent for a contact."""
    try:
        agent_id = await get_active_agent(phone_number)
        if not agent_id:
            raise HTTPException(status_code=404, detail="No active agent")

        return ServiceResponse.success({"agent_id": agent_id})

    except HTTPException:
        raise
    except Exception as error:
        logger.error(f"Error getting active agent: {error}")
        raise HTTPException(status_code=500, detail="Failed to get active agent")


def create_conversation_update(change, agent_id: str) -> Dict[str, Any]:
    """Create a conversation update message from Firestore change."""
    doc_data = change.document.to_dict()
    return {
        'type': 'conversation_update',
        'conversation_id': change.document.id,
        'agent_id': agent_id,
        'phone_number': doc_data.get('phone_number'),
        'messages': doc_data.get('messages', []),
        'message_count': doc_data.get('message_count', 0),
        'last_message_at': doc_data.get('last_message_at'),
        'timestamp': datetime.now().isoformat()
    }

def create_firestore_listener(agent_id: str, update_queue: asyncio.Queue, event_loop):
    """Create and return a Firestore listener for the agent's conversations."""
    from google.cloud.firestore_v1.base_query import FieldFilter

    collection = os.getenv('CONVERSATIONS_COLLECTION', 'conversations')

    def on_snapshot(docs, changes, read_time):
        """Handle Firestore document changes."""
        try:
            for change in changes:
                if change.type.name in ['ADDED', 'MODIFIED']:
                    doc_data = change.document.to_dict()
                    if doc_data and doc_data.get('agent_id') == agent_id:
                        update = create_conversation_update(change, agent_id)
                        # Put update in queue using the captured event loop
                        try:
                            asyncio.run_coroutine_threadsafe(
                                update_queue.put(update),
                                event_loop
                            )
                        except Exception as queue_error:
                            logger.error(f"Error putting update in queue: {queue_error}")
        except Exception as e:
            logger.error(f"Error in Firestore snapshot callback: {e}")

    # Create and start listener
    query = database.client.collection(collection).where(filter=FieldFilter('agent_id', "==", agent_id))
    return query.on_snapshot(on_snapshot)

async def stream_updates(update_queue: asyncio.Queue) -> AsyncGenerator[str, None]:
    """Stream updates from the queue to the client."""
    while True:
        try:
            # Wait for updates with timeout for heartbeat
            update = await asyncio.wait_for(update_queue.get(), timeout=30.0)
            yield f"data: {json.dumps(update)}\n\n"
        except asyncio.TimeoutError:
            # Send heartbeat
            yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"

@app.get("/conversations/stream/{agent_id}")
async def stream_conversation_updates(agent_id: str):
    """
    Simple SSE endpoint for real-time conversation updates.
    Uses Firestore's synchronous client for real-time listeners.
    """
    try:
        # Validate agent_id
        ValidationUtils.validate_uuid(agent_id)

        # Check if agent is still active by looking for any active agents with this ID
        collection = os.getenv('ACTIVE_AGENTS_COLLECTION', 'active_assistants')
        active_agents = await database.query_documents(
            collection,
            filters={"agent_id": agent_id, "status": "active"},
            limit=1
        )

        async def event_stream() -> AsyncGenerator[str, None]:
            listener = None
            update_queue = asyncio.Queue()

            try:
                # Capture the current event loop
                current_loop = asyncio.get_running_loop()

                # Send connection confirmation
                yield f"data: {json.dumps({'type': 'connected', 'agent_id': agent_id})}\n\n"

                # Check if agent is still active
                if not active_agents:
                    logger.info(f"Agent {agent_id} is not active, closing SSE connection")
                    yield f"data: {json.dumps({'type': 'agent_deactivated', 'agent_id': agent_id})}\n\n"
                    return

                # Check database availability
                if not database.client:
                    yield f"data: {json.dumps({'type': 'error', 'message': 'Database not available'})}\n\n"
                    return

                # Start Firestore listener with event loop reference
                listener = create_firestore_listener(agent_id, update_queue, current_loop)
                logger.info(f"Started Firestore listener for agent {agent_id}")

                # Stream updates to client
                async for message in stream_updates(update_queue):
                    yield message

            except Exception as e:
                logger.error(f"Error in SSE stream for agent {agent_id}: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

            finally:
                # Clean up listener
                if listener:
                    try:
                        listener.unsubscribe()
                        logger.info(f"Unsubscribed Firestore listener for agent {agent_id}")
                    except Exception as e:
                        logger.error(f"Error unsubscribing listener: {e}")

        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control, Content-Type",
                "Access-Control-Allow-Methods": "GET, OPTIONS"
            }
        )

    except Exception as error:
        logger.error(f"Error starting SSE stream for agent {agent_id}: {error}")
        raise HTTPException(status_code=500, detail="Failed to start conversation stream")


@app.options("/conversations/stream/{agent_id}")
async def stream_conversation_updates_options(agent_id: str):
    """Handle CORS preflight requests for SSE endpoint."""
    return Response(
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "Cache-Control, Content-Type",
            "Access-Control-Max-Age": "86400"
        }
    )


if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8080))
    logger.info(f"Starting Communications Service on http://0.0.0.0:{port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
