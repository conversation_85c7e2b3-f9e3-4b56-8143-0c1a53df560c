"""
Message Broker Abstraction Layer

This module provides a technology-agnostic interface for message broking operations.
Currently implements Google Pub/Sub but can be easily swapped for other technologies.
"""

import os
import json
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Callable, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class MessageBrokerInterface(ABC):
    """Abstract interface for message broker operations."""
    
    @abstractmethod
    async def publish(self, topic: str, message: Dict[str, Any], attributes: Optional[Dict[str, str]] = None) -> str:
        """Publish a message to a topic."""
        pass
    
    @abstractmethod
    async def subscribe(self, subscription: str, handler: Callable) -> None:
        """Subscribe to messages from a subscription."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if message broker is available."""
        pass


class PubSubMessageBroker(MessageBrokerInterface):
    """Google Cloud Pub/Sub implementation of message broker interface."""
    
    def __init__(self):
        self.publisher_client = None
        self.subscriber_client = None
        self.project_id = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize Pub/Sub clients with automatic credential detection."""
        try:
            from google.cloud import pubsub_v1
            from google.auth import default
        except ImportError:
            logger.error("Google Cloud Pub/Sub dependencies not available")
            return
        
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        if not self.project_id:
            logger.error("GOOGLE_CLOUD_PROJECT environment variable not set")
            return
        
        try:
            # Initialize publisher and subscriber clients
            self.publisher_client = pubsub_v1.PublisherClient()
            self.subscriber_client = pubsub_v1.SubscriberClient()
            logger.info(f"✅ Pub/Sub clients initialized for project: {self.project_id}")
            
        except Exception as error:
            logger.error(f"❌ Failed to initialize Pub/Sub clients: {error}")
            self.publisher_client = None
            self.subscriber_client = None
    
    def is_available(self) -> bool:
        """Check if Pub/Sub clients are available."""
        return self.publisher_client is not None and self.subscriber_client is not None
    
    async def publish(self, topic: str, message: Dict[str, Any], attributes: Optional[Dict[str, str]] = None) -> str:
        """Publish a message to a Pub/Sub topic."""
        if not self.publisher_client:
            raise Exception("Pub/Sub publisher not available")
        
        try:
            # Create topic path
            topic_path = self.publisher_client.topic_path(self.project_id, topic)
            
            # Prepare message
            message_data = {
                'eventType': message.get('eventType'),
                'aggregateId': message.get('aggregateId'),
                'payload': message.get('payload', {}),
                'metadata': {
                    'source': message.get('metadata', {}).get('source', 'unknown'),
                    'version': message.get('metadata', {}).get('version', '1.0'),
                    'timestamp': datetime.utcnow().isoformat(),
                    'messageId': message.get('messageId')
                }
            }
            
            # Convert to JSON bytes
            data = json.dumps(message_data).encode('utf-8')
            
            # Prepare attributes
            message_attributes = attributes or {}
            message_attributes.update({
                'eventType': message_data['eventType'],
                'source': message_data['metadata']['source']
            })
            
            # Publish message
            future = self.publisher_client.publish(topic_path, data, **message_attributes)
            message_id = future.result()
            
            logger.info(f"Published message {message_id} to topic {topic}")
            return message_id
            
        except Exception as error:
            logger.error(f"Error publishing message to topic {topic}: {error}")
            raise Exception(f"Message publishing failed: {str(error)}")
    
    async def subscribe(self, subscription: str, handler: Callable) -> None:
        """Subscribe to messages from a Pub/Sub subscription."""
        if not self.subscriber_client:
            raise Exception("Pub/Sub subscriber not available")
        
        try:
            # Create subscription path
            subscription_path = self.subscriber_client.subscription_path(self.project_id, subscription)
            
            def callback(message):
                try:
                    # Parse message data
                    message_data = json.loads(message.data.decode('utf-8'))
                    
                    # Call handler
                    handler(message_data, message.attributes)
                    
                    # Acknowledge message
                    message.ack()
                    
                except Exception as error:
                    logger.error(f"Error processing message: {error}")
                    message.nack()
            
            # Start listening
            streaming_pull_future = self.subscriber_client.subscribe(subscription_path, callback=callback)
            logger.info(f"Listening for messages on subscription {subscription}")
            
            # Keep the main thread running
            try:
                streaming_pull_future.result()
            except KeyboardInterrupt:
                streaming_pull_future.cancel()
                logger.info(f"Stopped listening on subscription {subscription}")
                
        except Exception as error:
            logger.error(f"Error subscribing to {subscription}: {error}")
            raise Exception(f"Subscription failed: {str(error)}")


class MessageBrokerService:
    """Centralized message broker service."""
    
    def __init__(self, broker_type: str = "pubsub"):
        self.broker_type = broker_type
        self.broker: MessageBrokerInterface = self._create_broker()
    
    def _create_broker(self) -> MessageBrokerInterface:
        """Create message broker instance based on configuration."""
        if self.broker_type == "pubsub":
            return PubSubMessageBroker()
        else:
            raise ValueError(f"Unsupported message broker type: {self.broker_type}")
    
    def get_broker(self) -> MessageBrokerInterface:
        """Get the message broker instance."""
        return self.broker
    
    def is_available(self) -> bool:
        """Check if message broker is available."""
        return self.broker.is_available()


# Global message broker service instance
_message_broker_service: Optional[MessageBrokerService] = None


def get_message_broker_service() -> MessageBrokerService:
    """Get the global message broker service instance."""
    global _message_broker_service
    if _message_broker_service is None:
        broker_type = os.getenv('MESSAGE_BROKER_TYPE', 'pubsub')
        _message_broker_service = MessageBrokerService(broker_type)
    return _message_broker_service


def get_message_broker() -> MessageBrokerInterface:
    """Get the message broker interface instance."""
    return get_message_broker_service().get_broker()


# Event types constants
class EventTypes:
    MESSAGE_RECEIVED = "message.received"
    MESSAGE_SENT = "message.sent"
    AGENT_ACTIVATED = "agent.activated"
    AGENT_DEACTIVATED = "agent.deactivated"
    LEAD_STATUS_UPDATED = "lead.status.updated"
    CONVERSATION_ENDED = "conversation.ended"
    CONTACT_UPDATED = "contact.updated"
    ANALYSIS_COMPLETED = "analysis.completed"


# Topic names constants
class Topics:
    MESSAGE_EVENTS = "message-events"
    AGENT_EVENTS = "agent-events"
    LEAD_EVENTS = "lead-events"
    CONTACT_EVENTS = "contact-events"
    ANALYTICS_EVENTS = "analytics-events"


def create_event_message(event_type: str, aggregate_id: str, payload: Dict[str, Any], 
                        source: str, message_id: Optional[str] = None) -> Dict[str, Any]:
    """Create a standardized event message."""
    import uuid
    
    return {
        'eventType': event_type,
        'aggregateId': aggregate_id,
        'payload': payload,
        'messageId': message_id or str(uuid.uuid4()),
        'metadata': {
            'source': source,
            'version': '1.0',
            'timestamp': datetime.utcnow().isoformat()
        }
    }
