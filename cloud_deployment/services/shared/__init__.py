"""
Shared Infrastructure Package

This package contains shared utilities, database abstractions, and constants
that are used across all Synapy microservices to eliminate code duplication and
maintain consistency.
"""

# Import main components for easy access
from .database_service import get_database, check_database_available, DatabaseInterface
from .message_broker import (
    get_message_broker, get_message_broker_service, MessageBrokerInterface,
    EventTypes, Topics, create_event_message
)
from .shared_utils import (
    ServiceResponse, ValidationUtils, DataUtils, AsyncUtils,
    LoggingUtils, HealthCheckUtils, ConfigUtils, ErrorHandlerUtils
)
from .shared_constants import *

__all__ = [
    # Database
    'get_database',
    'check_database_available',
    'DatabaseInterface',

    # Message Broker
    'get_message_broker',
    'get_message_broker_service',
    'MessageBrokerInterface',
    'EventTypes',
    'Topics',
    'create_event_message',

    # Utilities
    'ServiceResponse',
    'ValidationUtils',
    'DataUtils',
    'AsyncUtils',
    'LoggingUtils',
    'HealthCheckUtils',
    'ConfigUtils',
    'ErrorHandlerUtils',

    # Constants (imported via *)
]
