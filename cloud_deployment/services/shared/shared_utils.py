"""
Shared utilities and common functionality for all services.
This module contains reusable code patterns and business logic.
"""

import os
import json
import logging
import async<PERSON>
from typing import Any, Dict, List
from datetime import datetime
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)


class ServiceResponse:
    """Standardized response format for all services."""

    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create a success response."""
        response = {
            "success": True,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        }
        if data is not None:
            response["data"] = data
        return response

    @staticmethod
    def error(message: str, code: str = "ERROR", status_code: int = 500) -> HTTPException:
        """Create an error response."""
        return HTTPException(
            status_code=status_code,
            detail={
                "success": False,
                "error": message,
                "code": code,
                "timestamp": datetime.utcnow().isoformat()
            }
        )


class ValidationUtils:
    """Common validation utilities."""

    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
        """Validate that all required fields are present in data."""
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        if missing_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required fields: {', '.join(missing_fields)}"
            )

    @staticmethod
    def validate_lead_status(lead_status: str) -> None:
        """Validate that lead status is a valid HubSpot API value."""
        from .shared_constants import VALID_HUBSPOT_LEAD_STATUS_VALUES

        if lead_status not in VALID_HUBSPOT_LEAD_STATUS_VALUES:
            valid_values = ", ".join(VALID_HUBSPOT_LEAD_STATUS_VALUES)
            raise HTTPException(
                status_code=400,
                detail=f"Invalid lead status '{lead_status}'. Valid values are: {valid_values}"
            )

    @staticmethod
    def validate_phone_number(phone_number: str) -> str:
        """Validate and clean phone number format."""
        if not phone_number:
            raise HTTPException(status_code=400, detail="Phone number is required")

        # Remove 'whatsapp:' prefix if present
        cleaned = phone_number.replace('whatsapp:', '').strip()

        # Basic validation - should start with + and contain only digits
        if not cleaned.startswith('+') or not cleaned[1:].replace(' ', '').replace('-', '').isdigit():
            raise HTTPException(status_code=400, detail="Invalid phone number format")

        return cleaned

    @staticmethod
    def validate_uuid(uuid_string: str) -> str:
        """Validate UUID format."""
        import uuid
        try:
            uuid.UUID(uuid_string)
            return uuid_string
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid UUID format")


class DataUtils:
    """Common data manipulation utilities."""

    @staticmethod
    def clean_dict(data: Dict[str, Any], remove_none: bool = True, remove_empty: bool = False) -> Dict[str, Any]:
        """Clean dictionary by removing None values and optionally empty values."""
        cleaned = {}
        for key, value in data.items():
            if remove_none and value is None:
                continue
            if remove_empty and value == "":
                continue
            if isinstance(value, dict):
                cleaned_nested = DataUtils.clean_dict(value, remove_none, remove_empty)
                if cleaned_nested or not remove_empty:
                    cleaned[key] = cleaned_nested
            elif isinstance(value, list):
                cleaned_list = [
                    DataUtils.clean_dict(item, remove_none, remove_empty) if isinstance(item, dict) else item
                    for item in value
                    if not (remove_none and item is None) and not (remove_empty and item == "")
                ]
                if cleaned_list or not remove_empty:
                    cleaned[key] = cleaned_list
            else:
                cleaned[key] = value
        return cleaned

    @staticmethod
    def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """Merge two dictionaries, with dict2 values taking precedence."""
        result = dict1.copy()
        for key, value in dict2.items():
            if isinstance(value, dict) and key in result and isinstance(result[key], dict):
                result[key] = DataUtils.merge_dicts(result[key], value)
            else:
                result[key] = value
        return result

    @staticmethod
    def extract_nested_value(data: Dict[str, Any], path: str, default: Any = None) -> Any:
        """Extract nested value from dictionary using dot notation path."""
        keys = path.split('.')
        current = data

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default

        return current


class AsyncUtils:
    """Async operation utilities."""

    @staticmethod
    async def run_with_timeout(coro, timeout_seconds: float = 30.0):
        """Run coroutine with timeout."""
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            logger.error(f"Operation timed out after {timeout_seconds} seconds")
            raise HTTPException(status_code=408, detail="Operation timed out")

    @staticmethod
    async def gather_with_error_handling(tasks: List, return_exceptions: bool = True):
        """Gather multiple async tasks with error handling."""
        try:
            results = await asyncio.gather(*tasks, return_exceptions=return_exceptions)
            return results
        except Exception as error:
            logger.error(f"Error in gather operation: {error}")
            raise


class LoggingUtils:
    """Logging utilities."""

    @staticmethod
    def setup_logger(name: str, level: str = "INFO") -> logging.Logger:
        """Setup logger with consistent formatting."""
        logger = logging.getLogger(name)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        logger.setLevel(getattr(logging, level.upper()))
        return logger

    @staticmethod
    def log_request(request: Request, logger: logging.Logger):
        """Log incoming request details."""
        logger.info(f"Request: {request.method} {request.url.path}")
        if request.query_params:
            logger.debug(f"Query params: {dict(request.query_params)}")

    @staticmethod
    def log_response(response_data: Any, logger: logging.Logger, level: str = "INFO"):
        """Log response details."""
        if level.upper() == "DEBUG":
            logger.debug(f"Response: {json.dumps(response_data, default=str, indent=2)}")
        else:
            logger.info("Response sent successfully")


class HealthCheckUtils:
    """Health check utilities."""

    @staticmethod
    async def check_service_health(service_name: str, checks: List[callable]) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        health_status = {
            "service": service_name,
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {}
        }

        overall_healthy = True

        for check in checks:
            check_name = check.__name__
            try:
                if asyncio.iscoroutinefunction(check):
                    result = await check()
                else:
                    result = check()

                health_status["checks"][check_name] = {
                    "status": "healthy",
                    "result": result
                }
            except Exception as error:
                overall_healthy = False
                health_status["checks"][check_name] = {
                    "status": "unhealthy",
                    "error": str(error)
                }

        health_status["status"] = "healthy" if overall_healthy else "unhealthy"
        return health_status


class ConfigUtils:
    """Configuration utilities."""

    @staticmethod
    def get_env_var(key: str, default: Any = None, required: bool = False) -> Any:
        """Get environment variable with validation."""
        value = os.getenv(key, default)

        if required and value is None:
            raise ValueError(f"Required environment variable {key} is not set")

        return value

    @staticmethod
    def get_bool_env(key: str, default: bool = False) -> bool:
        """Get boolean environment variable."""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')

    @staticmethod
    def get_int_env(key: str, default: int = 0) -> int:
        """Get integer environment variable."""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            logger.warning(f"Invalid integer value for {key}, using default: {default}")
            return default

    @staticmethod
    def get_list_env(key: str, separator: str = ",", default: List[str] = None) -> List[str]:
        """Get list environment variable."""
        if default is None:
            default = []

        value = os.getenv(key)
        if not value:
            return default

        return [item.strip() for item in value.split(separator) if item.strip()]


class ErrorHandlerUtils:
    """Error handling utilities."""

    @staticmethod
    def create_error_handler():
        """Create standardized error handler for FastAPI."""
        async def error_handler(request: Request, exc: Exception):
            logger.error(f"Unhandled error in {request.url.path}: {exc}", exc_info=True)

            if isinstance(exc, HTTPException):
                return JSONResponse(
                    status_code=exc.status_code,
                    content=exc.detail if isinstance(exc.detail, dict) else {"error": exc.detail}
                )

            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": "Internal server error",
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

        return error_handler

    @staticmethod
    def handle_database_error(error: Exception) -> HTTPException:
        """Handle database-specific errors."""
        error_message = str(error)

        if "not found" in error_message.lower():
            return HTTPException(status_code=404, detail="Resource not found")
        elif "permission" in error_message.lower() or "unauthorized" in error_message.lower():
            return HTTPException(status_code=403, detail="Access denied")
        elif "timeout" in error_message.lower():
            return HTTPException(status_code=408, detail="Database operation timed out")
        else:
            logger.error(f"Database error: {error}")
            return HTTPException(status_code=500, detail="Database operation failed")


# Common constants
DEFAULT_TIMEOUT = 30.0
MAX_RETRY_ATTEMPTS = 3
DEFAULT_PAGE_SIZE = 50
MAX_PAGE_SIZE = 1000
