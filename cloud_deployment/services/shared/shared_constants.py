"""
Shared constants for all services.
This module contains common constants, error messages, and configuration values.
"""

# Error Messages
AGENT_NOT_FOUND = "Agent not found"
INTERNAL_SERVER_ERROR = "Internal server error"
DATABASE_UNAVAILABLE = "Database unavailable - check database configuration"
VALIDATION_ERROR = "Validation error"
MAPPING_ERROR = "Data mapping failed"
STORAGE_ERROR = "Database storage failed"
FETCH_ERROR = "Failed to fetch resource"
CREATE_ERROR = "Failed to create resource"
UPDATE_ERROR = "Failed to update resource"
DELETE_ERROR = "Failed to delete resource"

# HTTP Status Codes
HTTP_OK = 200
HTTP_CREATED = 201
HTTP_BAD_REQUEST = 400
HTTP_UNAUTHORIZED = 401
HTTP_FORBIDDEN = 403
HTTP_NOT_FOUND = 404
HTTP_CONFLICT = 409
HTTP_UNPROCESSABLE_ENTITY = 422
HTTP_INTERNAL_SERVER_ERROR = 500
HTTP_SERVICE_UNAVAILABLE = 503

# WebSocket Close Codes
WS_CLOSE_NORMAL = 1000
WS_CLOSE_GOING_AWAY = 1001
WS_CLOSE_PROTOCOL_ERROR = 1002
WS_CLOSE_UNSUPPORTED_DATA = 1003
WS_CLOSE_INVALID_FRAME_PAYLOAD_DATA = 1007
WS_CLOSE_POLICY_VIOLATION = 1008
WS_CLOSE_MESSAGE_TOO_BIG = 1009
WS_CLOSE_INTERNAL_ERROR = 1011

# Timeouts (in seconds)
DEFAULT_TIMEOUT = 30
DATABASE_TIMEOUT = 10
WEBSOCKET_TIMEOUT = 60
HTTP_REQUEST_TIMEOUT = 30
AI_REQUEST_TIMEOUT = 45

# Limits
MAX_AGENTS_PER_USER = 100
MAX_CONVERSATION_LENGTH = 10000
MAX_MESSAGE_SIZE = 1024 * 1024  # 1MB
MAX_BATCH_SIZE = 500
DEFAULT_PAGE_SIZE = 50
MAX_PAGE_SIZE = 1000

# File Storage
DEFAULT_BUCKET_NAME = "default-bucket"
CONVERSATION_DETAILS_PATH_TEMPLATE = "{agent_id}/conversation_details.json"
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# Agent States
AGENT_STATE_CREATED = "created"
AGENT_STATE_UPDATED = "updated"
AGENT_STATE_ACTIVE = "active"
AGENT_STATE_INACTIVE = "inactive"
AGENT_STATE_DELETED = "deleted"

# Conversation States
CONVERSATION_STATE_ACTIVE = "active"
CONVERSATION_STATE_ENDED = "ended"
CONVERSATION_STATE_PAUSED = "paused"

# Message Types
MESSAGE_TYPE_TEXT = "text"
MESSAGE_TYPE_AUDIO = "audio"
MESSAGE_TYPE_IMAGE = "image"
MESSAGE_TYPE_SYSTEM = "system"

# Message Directions
MESSAGE_DIRECTION_INBOUND = "inbound"
MESSAGE_DIRECTION_OUTBOUND = "outbound"

# Valid HubSpot Lead Status Values (API values)
VALID_HUBSPOT_LEAD_STATUS_VALUES = [
    "NEW",
    "OPEN",
    "IN_PROGRESS",
    "OPEN_DEAL",
    "UNQUALIFIED",
    "ATTEMPTED_TO_CONTACT",
    "CONNECTED",
    "BAD_TIMING"
]

# Lead Status Options
LEAD_STATUS_OPTIONS = {
    "NEW": "Nuevo",
    "OPEN": "Abierto",
    "IN_PROGRESS": "En progreso",
    "OPEN_DEAL": "Negocio abierto",
    "UNQUALIFIED": "No calificado",
    "ATTEMPTED_TO_CONTACT": "Intento de contacto",
    "CONNECTED": "Conectado",
    "BAD_TIMING": "Mal momento"
}

# Default lead status
DEFAULT_LEAD_STATUS = "NEW"

# Environment Variables
ENV_LOG_LEVEL = "LOG_LEVEL"

# Default Values
DEFAULT_PORT = 8080
DEFAULT_DATABASE_TYPE = "firestore"
DEFAULT_LOG_LEVEL = "INFO"
DEFAULT_NODE_ENV = "production"

# Service Names
SERVICE_AGENT = "agent-service"
SERVICE_COMMUNICATIONS = "communications-service"
SERVICE_CONTACT = "contact-service"
SERVICE_ANALYTICS = "analytics-service"

# Service Ports
SERVICE_AGENT_PORT = 8080
SERVICE_COMMUNICATIONS_PORT = 8083
SERVICE_CONTACT_PORT = 8081
SERVICE_ANALYTICS_PORT = 8082

# API Endpoints
HEALTH_ENDPOINT = "/health"
AGENTS_ENDPOINT = "/agent"
ALL_AGENTS_ENDPOINT = "/all"
WEBSOCKET_ENDPOINT = "/chat/{agent_id}"

# Content Types
CONTENT_TYPE_JSON = "application/json"
CONTENT_TYPE_TEXT = "text/plain"
CONTENT_TYPE_XML = "application/xml"
CONTENT_TYPE_FORM = "application/x-www-form-urlencoded"

# Twilio Constants
TWILIO_WEBHOOK_TIMEOUT = 15
TWILIO_MAX_MESSAGE_LENGTH = 1600
TWILIO_WHATSAPP_PREFIX = "whatsapp:"

# AI/LLM Constants
DEFAULT_AI_MODEL = "claude-3-5-haiku-20241022"
AI_MAX_TOKENS = 4000
AI_TEMPERATURE = 0.7
AI_MAX_RETRIES = 3

# Logging Levels
LOG_LEVEL_DEBUG = "DEBUG"
LOG_LEVEL_INFO = "INFO"
LOG_LEVEL_WARNING = "WARNING"
LOG_LEVEL_ERROR = "ERROR"
LOG_LEVEL_CRITICAL = "CRITICAL"

# Cache Keys
CACHE_KEY_AGENT_PREFIX = "agent:"
CACHE_KEY_CONVERSATION_PREFIX = "conversation:"
CACHE_KEY_ACTIVE_AGENT_PREFIX = "active_agent:"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE = 60
RATE_LIMIT_REQUESTS_PER_HOUR = 1000
RATE_LIMIT_REQUESTS_PER_DAY = 10000

# Monitoring and Health Check
HEALTH_CHECK_INTERVAL = 600  # seconds
METRICS_COLLECTION_INTERVAL = 3600  # seconds

# Feature Flags
FEATURE_ENABLE_CACHING = "enable_caching"
FEATURE_ENABLE_ANALYTICS = "enable_analytics"
FEATURE_ENABLE_RATE_LIMITING = "enable_rate_limiting"
FEATURE_ENABLE_METRICS = "enable_metrics"

# Validation Patterns
UUID_PATTERN = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
PHONE_NUMBER_PATTERN = r'^\+[1-9]\d{1,14}$'
EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

# Security
MAX_LOGIN_ATTEMPTS = 5
SESSION_TIMEOUT = 3600  # 1 hour
TOKEN_EXPIRY = 86400  # 24 hours

# Performance
CONNECTION_POOL_SIZE = 10
MAX_CONCURRENT_REQUESTS = 100
REQUEST_QUEUE_SIZE = 1000
