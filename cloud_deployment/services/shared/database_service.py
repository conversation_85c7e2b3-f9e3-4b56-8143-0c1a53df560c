"""
Centralized database service with infrastructure abstraction.
This module provides a unified database interface that can be shared across all services.
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DatabaseInterface(ABC):
    """Abstract interface for database operations to decouple from specific implementations."""

    @abstractmethod
    async def get_document(self, collection: str, document_id: str) -> Optional[Dict[str, Any]]:
        """Get a document by ID."""
        pass

    @abstractmethod
    async def set_document(self, collection: str, document_id: str, data: Dict[str, Any]) -> None:
        """Set a document."""
        pass

    @abstractmethod
    async def add_document(self, collection: str, data: Dict[str, Any]) -> str:
        """Add a document with auto-generated ID and return the document ID."""
        pass

    @abstractmethod
    async def update_document(self, collection: str, document_id: str, data: Dict[str, Any]) -> None:
        """Update a document."""
        pass

    @abstractmethod
    async def delete_document(self, collection: str, document_id: str) -> None:
        """Delete a document."""
        pass

    @abstractmethod
    async def query_documents(self, collection: str, filters: Optional[Dict] = None, limit: Optional[int] = None,
                            order_by: Optional[str] = None, order_direction: str = "asc") -> List[Dict[str, Any]]:
        """Query documents in a collection with optional ordering."""
        pass

    @abstractmethod
    async def batch_write(self, operations: List[Dict[str, Any]]) -> None:
        """Execute multiple write operations in a batch."""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if database is available."""
        pass


class FirestoreDatabase(DatabaseInterface):
    """Firestore implementation of the database interface."""

    def __init__(self):
        self.client = None
        self.async_client = None
        self._initialize_clients()

    def _initialize_clients(self):
        """Initialize both sync and async Firestore clients with automatic credential detection."""
        try:
            from google.cloud import firestore
            from google.auth import default
        except ImportError:
            logger.error("Google Cloud Firestore dependencies not available")
            return

        project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        if not project_id:
            logger.error("GOOGLE_CLOUD_PROJECT environment variable not set")
            return

        try:
            # Try default credentials first (works in Cloud Run)
            credentials, detected_project = default()
            final_project_id = detected_project or project_id

            # Initialize both sync and async clients
            self.client = firestore.Client(project=final_project_id, credentials=credentials)
            self.async_client = firestore.AsyncClient(project=final_project_id, credentials=credentials)

            logger.info(f"✅ Firestore clients initialized for project: {final_project_id}")

        except Exception as error:
            logger.error(f"❌ Failed to initialize Firestore clients: {error}")

            # Fallback: try without explicit credentials
            try:
                self.client = firestore.Client(project=project_id)
                self.async_client = firestore.AsyncClient(project=project_id)
                logger.info(f"✅ Firestore clients initialized with implicit credentials for project: {project_id}")
            except Exception as fallback_error:
                logger.error(f"❌ Fallback initialization failed: {fallback_error}")
                self.client = None
                self.async_client = None

    def is_available(self) -> bool:
        """Check if Firestore clients are available."""
        return self.client is not None and self.async_client is not None

    async def get_document(self, collection: str, document_id: str) -> Optional[Dict[str, Any]]:
        """Get a document by ID."""
        if not self.async_client:
            return None

        try:
            doc_ref = self.async_client.collection(collection).document(document_id)
            doc = await doc_ref.get()
            if doc.exists:
                data = doc.to_dict()
                data['id'] = doc.id
                return data
            return None
        except Exception as error:
            logger.error(f"Error getting document {document_id} from {collection}: {error}")
            return None

    async def set_document(self, collection: str, document_id: str, data: Dict[str, Any]) -> None:
        """Set a document."""
        if not self.async_client:
            raise Exception("[set_document] Database not available")

        try:
            # Clean data for Firestore
            clean_data = self._clean_data_for_firestore(data)
            doc_ref = self.async_client.collection(collection).document(document_id)
            await doc_ref.set(clean_data)
        except Exception as error:
            logger.error(f"Error setting document {document_id} in {collection}: {error}")
            raise Exception(f"[set_document] Database operation failed: {str(error)}")

    async def add_document(self, collection: str, data: Dict[str, Any]) -> str:
        """Add a document with auto-generated ID and return the document ID."""
        if not self.async_client:
            raise Exception("[add_document] Database not available")

        try:
            # Clean data for Firestore
            clean_data = self._clean_data_for_firestore(data)

            # Firestore's async add() returns (DocumentReference, WriteResult)
            _, doc_ref = await self.async_client.collection(collection).add(clean_data)

            return doc_ref.id
        except Exception as error:
            logger.error(f"Error adding document to {collection}: {error}")
            raise Exception(f"[add_document] Database operation failed: {str(error)}")

    async def update_document(self, collection: str, document_id: str, data: Dict[str, Any]) -> None:
        """Update a document."""
        if not self.async_client:
            raise Exception("[update_document] Database not available")

        try:
            # Clean data for Firestore
            clean_data = self._clean_data_for_firestore(data)
            doc_ref = self.async_client.collection(collection).document(document_id)
            await doc_ref.update(clean_data)
        except Exception as error:
            logger.error(f"Error updating document {document_id} in {collection}: {error}")
            raise Exception(f"[update_document] Database operation failed: {str(error)}")

    async def delete_document(self, collection: str, document_id: str) -> None:
        """Delete a document."""
        if not self.async_client:
            raise Exception("[delete_document] Database not available")

        try:
            doc_ref = self.async_client.collection(collection).document(document_id)
            await doc_ref.delete()
        except Exception as error:
            logger.error(f"Error deleting document {document_id} from {collection}: {error}")
            raise Exception(f"[delete_document] Database operation failed: {str(error)}")

    async def query_documents(self, collection: str, filters: Optional[Dict] = None, limit: Optional[int] = None,
                            order_by: Optional[str] = None, order_direction: str = "asc") -> List[Dict[str, Any]]:
        """Query documents in a collection with optional ordering."""
        from google.cloud.firestore_v1.base_query import FieldFilter

        if not self.async_client:
            return []

        try:
            query = self.async_client.collection(collection)

            # Apply filters
            if filters:
                for field, value in filters.items():
                    if isinstance(value, dict) and 'operator' in value:
                        # Support for complex filters: {'operator': '>=', 'value': 10}
                        query = query.where(
                            filter=FieldFilter(field, value['operator'], value['value'])
                        )
                    else:
                        # Simple equality filter
                        query = query.where(
                            filter=FieldFilter(field, '==', value)
                        )

            # Apply ordering
            if order_by:
                # Convert order_direction to Firestore direction
                from google.cloud.firestore import Query
                direction = Query.DESCENDING if order_direction.lower() == "desc" else Query.ASCENDING
                query = query.order_by(order_by, direction=direction)

            # Apply limit
            if limit:
                query = query.limit(limit)

            # Execute query
            docs = query.stream()
            results = []
            async for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(data)

            return results

        except Exception as error:
            logger.error(f"Error querying collection {collection}: {error}")
            return []

    async def batch_write(self, operations: List[Dict[str, Any]]) -> None:
        """Execute multiple write operations in a batch."""
        if not self.async_client:
            raise Exception("Database not available")

        try:
            batch = self.async_client.batch()

            for operation in operations:
                op_type = operation.get('type')
                collection = operation.get('collection')
                document_id = operation.get('document_id')
                data = operation.get('data', {})

                doc_ref = self.async_client.collection(collection).document(document_id)

                if op_type == 'set':
                    clean_data = self._clean_data_for_firestore(data)
                    batch.set(doc_ref, clean_data)
                elif op_type == 'update':
                    clean_data = self._clean_data_for_firestore(data)
                    batch.update(doc_ref, clean_data)
                elif op_type == 'delete':
                    batch.delete(doc_ref)

            await batch.commit()

        except Exception as error:
            logger.error(f"Error executing batch write: {error}")
            raise Exception(f"Batch operation failed: {str(error)}")

    def _clean_data_for_firestore(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean data for Firestore storage by removing None values and converting types."""
        if isinstance(data, dict):
            cleaned = {}
            for key, value in data.items():
                if value is not None:
                    if isinstance(value, dict):
                        cleaned[key] = self._clean_data_for_firestore(value)
                    elif isinstance(value, list):
                        cleaned[key] = [
                            self._clean_data_for_firestore(item) if isinstance(item, dict) else item
                            for item in value if item is not None
                        ]
                    elif isinstance(value, datetime):
                        # Convert datetime to Firestore timestamp
                        cleaned[key] = value
                    else:
                        cleaned[key] = value
            return cleaned
        return data


class DatabaseService:
    """Centralized database service that provides a single point of access."""

    def __init__(self, database_type: str = "firestore"):
        self.database_type = database_type
        self.database: DatabaseInterface = self._create_database()

    def _create_database(self) -> DatabaseInterface:
        """Create database instance based on configuration."""
        if self.database_type == "firestore":
            return FirestoreDatabase()
        else:
            raise ValueError(f"Unsupported database type: {self.database_type}")

    def get_database(self) -> DatabaseInterface:
        """Get the database instance."""
        return self.database

    def is_available(self) -> bool:
        """Check if database is available."""
        return self.database.is_available()


# Global database service instance
_database_service: Optional[DatabaseService] = None


def get_database_service() -> DatabaseService:
    """Get the global database service instance."""
    global _database_service
    if _database_service is None:
        database_type = os.getenv('DATABASE_TYPE', 'firestore')
        _database_service = DatabaseService(database_type)
    return _database_service


def get_database() -> DatabaseInterface:
    """Get the database interface instance."""
    return get_database_service().get_database()


def check_database_available():
    """Check if database is available and raise exception if not."""
    from fastapi import HTTPException

    if not get_database_service().is_available():
        raise HTTPException(
            status_code=503,
            detail="Database not available. Check database configuration."
        )
