# Deployment Guide

Quick deployment instructions for the Synapy Dashboard.

## 🚀 Automatic Deployment

Deployment happens automatically via GitHub Actions when pushing to `main` branch.

## 🔧 Manual Deployment

### Frontend
```bash
npm run build
firebase deploy --only hosting
```

### Firebase Functions
```bash
cd functions
firebase deploy --only functions
```

## 📋 Prerequisites

- Firebase CLI installed and authenticated
- Required Google Cloud APIs enabled
## 🔍 Troubleshooting

- **Function not found**: Ensure functions are deployed correctly
- **CORS errors**: Check allowed origins configuration
- **API key errors**: Verify HubSpot private app token and scopes
- **Deployment errors**: Check Python version and Firebase permissions

## 📊 Monitoring

View function logs:
```bash
firebase functions:log --follow
```
