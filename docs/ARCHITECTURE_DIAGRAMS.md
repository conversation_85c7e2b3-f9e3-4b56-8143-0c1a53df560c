# 📊 Architecture Diagrams - AI Virtual Assistant Platform

This document contains comprehensive visual representations of the Synapy platform architecture using Mermaid diagrams.

## 1. Overall System Architecture

```mermaid
graph TB
    %% Frontend Layer
    subgraph "Frontend Layer"
        WEB[Web App - Vue 3 + TypeScript]
        FB_HOST[Firebase Hosting]
        FB_AUTH[Firebase Auth]
        WEB --> FB_HOST
        WEB --> FB_AUTH
    end

    %% API Gateway Layer
    subgraph "API Gateway Layer"
        LB[Cloud Load Balancer]
        API_GW[Cloud Endpoints/API Gateway]
        LB --> API_GW
    end

    %% Microservices Layer
    subgraph "Microservices Layer"
        AGT_SVC[Agent Service<br/>🔥 Always On<br/>2 vCPU, 4GB RAM]
        COMM_SVC[Communications Service<br/>Twilio Integration<br/>1 vCPU, 2GB RAM]
        CONT_SVC[Contact Service<br/>HubSpot Integration<br/>1 vCPU, 1GB RAM]
        ANA_SVC[Analytics Service<br/>🆕 AI Analysis<br/>2 vCPU, 2GB RAM]
        NOT_SVC[Notification Service<br/>0.5 vCPU, 512MB RAM]
    end

    %% Event Streaming
    subgraph "Event Streaming"
        PUBSUB[Cloud Pub/Sub]
        subgraph "Topics"
            T1[message.received]
            T2[message.sent]
            T3[agent.activated]
            T4[agent.deactivated]
            T5[lead.status.updated]
            T6[conversation.ended]
        end
    end

    %% Background Processing
    subgraph "Background Processing"
        CF[Cloud Functions 2nd Gen]
        CT[Cloud Tasks]
        CRON[Cloud Scheduler]

        subgraph "Functions"
            F1[hubspot-sync-trigger]
            F2[conversation-cleanup]
            F3[report-generator]
            F4[webhook-retries]
        end

        subgraph "Queues"
            Q1[message-processing-queue]
            Q2[analytics-processing]
        end
    end

    %% Data Layer
    subgraph "Data Layer"
        FIRESTORE[(Firestore)]
        FB_STORAGE[Firebase Storage]
        CLOUD_STORAGE[Cloud Storage]

        subgraph "Collections"
            C1[users/{userId}/contacts/]
            C2[users/{userId}/integrations/]
            C3[agents/]
            C4[analytics/]
            C5[conversations/]
        end
    end

    %% External Services
    subgraph "External Services"
        WHATSAPP[WhatsApp]
        TWILIO[Twilio API]
        HUBSPOT[HubSpot API]
        AI_ENGINE[AI Engine]
    end

    %% Monitoring
    subgraph "Observability"
        MONITORING[Cloud Monitoring]
        LOGGING[Cloud Logging]
        TRACE[Cloud Trace]
        ALERTING[Cloud Alerting]
    end

    %% Connections
    WEB --> API_GW
    API_GW --> AGT_SVC
    API_GW --> COMM_SVC
    API_GW --> CONT_SVC
    API_GW --> ANA_SVC
    API_GW --> NOT_SVC

    %% Service to Pub/Sub
    AGT_SVC -.-> PUBSUB
    COMM_SVC -.-> PUBSUB
    ANA_SVC -.-> PUBSUB
    CONT_SVC -.-> PUBSUB

    %% Pub/Sub to Services
    PUBSUB -.-> ANA_SVC
    PUBSUB -.-> NOT_SVC
    PUBSUB -.-> CONT_SVC

    %% Background Processing
    PUBSUB --> CT
    CRON --> CF
    CT --> AGT_SVC
    CT --> ANA_SVC

    %% Data Layer Connections
    AGT_SVC --> FIRESTORE
    COMM_SVC --> FIRESTORE
    CONT_SVC --> FIRESTORE
    ANA_SVC --> FIRESTORE
    NOT_SVC --> FIRESTORE

    AGT_SVC --> FB_STORAGE
    ANA_SVC --> CLOUD_STORAGE

    %% External Connections
    WHATSAPP <--> TWILIO
    TWILIO <--> COMM_SVC
    CONT_SVC <--> HUBSPOT
    AGT_SVC --> AI_ENGINE

    %% Monitoring Connections
    AGT_SVC -.-> MONITORING
    COMM_SVC -.-> MONITORING
    ANA_SVC -.-> MONITORING
    CONT_SVC -.-> MONITORING
    NOT_SVC -.-> MONITORING

    classDef frontend fill:#e1f5fe
    classDef gateway fill:#f3e5f5
    classDef microservice fill:#e8f5e8
    classDef critical fill:#ffebee
    classDef event fill:#fff3e0
    classDef data fill:#f1f8e9
    classDef external fill:#fafafa
    classDef monitoring fill:#e0f2f1

    class WEB,FB_HOST,FB_AUTH frontend
    class LB,API_GW gateway
    class COMM_SVC,CONT_SVC,NOT_SVC microservice
    class AGT_SVC,ANA_SVC critical
    class PUBSUB,CF,CT,CRON event
    class FIRESTORE,FB_STORAGE,CLOUD_STORAGE data
    class WHATSAPP,TWILIO,HUBSPOT,AI_ENGINE external
    class MONITORING,LOGGING,TRACE,ALERTING monitoring
```

## 2. Message Processing Flow

```mermaid
sequenceDiagram
    participant WA as WhatsApp
    participant TW as Twilio
    participant COMM as Communications Service
    participant FS as Firestore
    participant PS as Pub/Sub
    participant AGT as Agent Service
    participant AI as AI Engine
    participant ANA as Analytics Service

    Note over WA,ANA: Incoming Message Flow

    WA->>TW: User message
    TW->>COMM: Webhook: new message

    COMM->>FS: Find active agent
    FS-->>COMM: Agent data

    COMM->>PS: Publish message.received

    COMM->>AGT: Webhook: process message
    AGT->>AI: Process with AI
    AI-->>AGT: Generated response
    AGT-->>COMM: Agent response

    COMM->>TW: Send response
    TW->>WA: Message to user

    COMM->>PS: Publish message.sent

    Note over PS,ANA: Asynchronous Processing
    PS->>ANA: message.received event
    PS->>ANA: message.sent event

    ANA->>AI: Analyze conversation
    AI-->>ANA: Analysis complete

    ANA->>FS: Update analytics
    ANA->>PS: Publish lead.status.updated (if applicable)
```

## 3. Agent Activation Flow

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant API as API Gateway
    participant COMM as Communications Service
    participant AGT as Agent Service
    participant FS as Firestore
    participant TW as Twilio
    participant WA as WhatsApp
    participant PS as Pub/Sub
    participant ANA as Analytics Service

    Note over FE,ANA: Agent Activation Flow

    FE->>API: POST /activate-agent
    API->>COMM: Activate agent for contact

    COMM->>AGT: GET /agents/{agentId}
    AGT-->>COMM: Agent configuration

    COMM->>FS: Create/update active conversation
    FS-->>COMM: Conversation saved

    COMM->>TW: Send initial message
    TW->>WA: Welcome message

    COMM->>PS: Publish agent.activated
    COMM-->>API: Agent activated successfully
    API-->>FE: 200 OK response

    Note over PS,ANA: Background Processing
    PS->>ANA: agent.activated event
    ANA->>FS: Record conversation start
```

## 4. Service Components Architecture

```mermaid
graph TB
    subgraph "Agent Service (Core AI)"
        AGT_API[REST API]
        AGT_WEBHOOK[Webhook Endpoint]
        AGT_PROCESSOR[Message Processor]
        AGT_AI[AI Integration]
        AGT_CONFIG[Agent Configuration]

        AGT_API --> AGT_CONFIG
        AGT_WEBHOOK --> AGT_PROCESSOR
        AGT_PROCESSOR --> AGT_AI
        AGT_PROCESSOR --> AGT_CONFIG
    end

    subgraph "Communications Service (Twilio)"
        COMM_API[REST API]
        COMM_WEBHOOK[Twilio Webhook]
        COMM_MGR[Conversation Manager]
        COMM_TWILIO[Twilio Client]

        COMM_API --> COMM_MGR
        COMM_WEBHOOK --> COMM_MGR
        COMM_MGR --> COMM_TWILIO
    end

    subgraph "Analytics Service (New)"
        ANA_PROCESSOR[Event Processor]
        ANA_AI[AI Analyzer]
        ANA_LEAD[Lead Scorer]
        ANA_INSIGHTS[Insight Extractor]
        ANA_REPORTER[Report Generator]

        ANA_PROCESSOR --> ANA_AI
        ANA_AI --> ANA_LEAD
        ANA_AI --> ANA_INSIGHTS
        ANA_LEAD --> ANA_REPORTER
    end

    subgraph "Contact Service (HubSpot)"
        CONT_API[REST API]
        CONT_SYNC[HubSpot Sync]
        CONT_CACHE[Contact Cache]
        CONT_LEAD[Lead Manager]

        CONT_API --> CONT_CACHE
        CONT_SYNC --> CONT_CACHE
        CONT_CACHE --> CONT_LEAD
    end

    %% Inter-service communication
    COMM_MGR -.->|HTTP| AGT_WEBHOOK
    ANA_PROCESSOR -.->|Pub/Sub| COMM_MGR
    ANA_LEAD -.->|Pub/Sub| CONT_LEAD

    classDef apiComp fill:#e3f2fd
    classDef processorComp fill:#f1f8e9
    classDef integrationComp fill:#fff8e1
    classDef dataComp fill:#fce4ec

    class AGT_API,COMM_API,CONT_API apiComp
    class AGT_PROCESSOR,COMM_MGR,ANA_PROCESSOR processorComp
    class AGT_AI,COMM_TWILIO,ANA_AI,CONT_SYNC integrationComp
    class AGT_CONFIG,CONT_CACHE,ANA_LEAD dataComp
```

## 5. Event-Driven Architecture

```mermaid
graph LR
    subgraph "Event Publishers"
        COMM[Communications Service]
        AGT[Agent Service]
        ANA[Analytics Service]
        CONT[Contact Service]
    end

    subgraph "Pub/Sub Topics"
        T1[message.received]
        T2[message.sent]
        T3[agent.activated]
        T4[agent.deactivated]
        T5[lead.status.updated]
        T6[conversation.ended]
    end

    subgraph "Event Subscribers"
        ANA_SUB[Analytics Service]
        NOT_SUB[Notification Service]
        CONT_SUB[Contact Service]
        WEBHOOK_SUB[External Webhooks]
    end

    %% Publishers to Topics
    COMM --> T1
    COMM --> T2
    COMM --> T3
    COMM --> T4
    COMM --> T6
    ANA --> T5
    CONT --> T5

    %% Topics to Subscribers
    T1 --> ANA_SUB
    T2 --> ANA_SUB
    T3 --> ANA_SUB
    T3 --> NOT_SUB
    T4 --> NOT_SUB
    T5 --> CONT_SUB
    T5 --> NOT_SUB
    T5 --> WEBHOOK_SUB
    T6 --> ANA_SUB

    classDef publisher fill:#e8f5e8
    classDef topic fill:#fff3e0
    classDef subscriber fill:#e1f5fe

    class COMM,AGT,ANA,CONT publisher
    class T1,T2,T3,T4,T5,T6 topic
    class ANA_SUB,NOT_SUB,CONT_SUB,WEBHOOK_SUB subscriber
```

## 6. Cloud Run Deployment Architecture

```mermaid
graph TB
    subgraph "Google Cloud Platform"
        subgraph "Cloud Run Services"
            subgraph "Agent Service"
                AGT_MIN[Min: 1 instance]
                AGT_MAX[Max: 10 instances]
                AGT_CPU[2 vCPU, 4GB RAM]
                AGT_TIMEOUT[60s timeout]
            end

            subgraph "Communications Service"
                COMM_MIN[Min: 0 instances]
                COMM_MAX[Max: 5 instances]
                COMM_CPU[1 vCPU, 2GB RAM]
                COMM_TIMEOUT[30s timeout]
            end

            subgraph "Analytics Service"
                ANA_MIN[Min: 0 instances]
                ANA_MAX[Max: 5 instances]
                ANA_CPU[2 vCPU, 2GB RAM]
                ANA_TIMEOUT[60s timeout]
            end

            subgraph "Contact Service"
                CONT_MIN[Min: 0 instances]
                CONT_MAX[Max: 3 instances]
                CONT_CPU[1 vCPU, 1GB RAM]
                CONT_TIMEOUT[30s timeout]
            end

            subgraph "Notification Service"
                NOT_MIN[Min: 0 instances]
                NOT_MAX[Max: 3 instances]
                NOT_CPU[0.5 vCPU, 512MB RAM]
                NOT_TIMEOUT[30s timeout]
            end
        end

        subgraph "Shared Resources"
            LB[Cloud Load Balancer]
            VPC[VPC Network]
            IAM[Cloud IAM]
            SECRETS[Secret Manager]
        end

        subgraph "Data & Storage"
            FIRESTORE_PROD[(Firestore)]
            PUBSUB_PROD[Pub/Sub]
            STORAGE_PROD[Cloud Storage]
        end

        subgraph "Monitoring & Ops"
            MONITORING_PROD[Cloud Monitoring]
            LOGGING_PROD[Cloud Logging]
            TRACE_PROD[Cloud Trace]
        end
    end

    subgraph "CI/CD Pipeline"
        GITHUB[GitHub Repository]
        ACTIONS[GitHub Actions]
        REGISTRY[Artifact Registry]
        DEPLOY[Cloud Deploy]

        GITHUB --> ACTIONS
        ACTIONS --> REGISTRY
        REGISTRY --> DEPLOY
    end

    %% Deployment flow
    DEPLOY --> AGT_MIN
    DEPLOY --> COMM_MIN
    DEPLOY --> ANA_MIN
    DEPLOY --> CONT_MIN
    DEPLOY --> NOT_MIN

    %% Infrastructure connections
    LB --> AGT_MIN
    LB --> COMM_MIN
    LB --> ANA_MIN
    LB --> CONT_MIN
    LB --> NOT_MIN

    classDef service fill:#e8f5e8
    classDef resource fill:#e1f5fe
    classDef data fill:#f1f8e9
    classDef cicd fill:#fff3e0
    classDef monitoring fill:#fce4ec

    class AGT_MIN,COMM_MIN,ANA_MIN,CONT_MIN,NOT_MIN service
    class LB,VPC,IAM,SECRETS resource
    class FIRESTORE_PROD,PUBSUB_PROD,STORAGE_PROD data
    class GITHUB,ACTIONS,REGISTRY,DEPLOY cicd
    class MONITORING_PROD,LOGGING_PROD,TRACE_PROD monitoring
```

## 7. Security & Network Architecture

```mermaid
graph TB
    subgraph "Public Internet"
        USERS[Users]
        EXTERNAL[External APIs]
    end

    subgraph "Cloud Armor"
        ARMOR[DDoS Protection<br/>Rate Limiting<br/>WAF Rules]
    end

    subgraph "Load Balancer Layer"
        HTTPS_LB[HTTPS Load Balancer<br/>SSL Termination]
    end

    subgraph "API Gateway"
        API_GATEWAY[Cloud Endpoints<br/>Authentication<br/>Rate Limiting<br/>API Keys]
    end

    subgraph "VPC Network"
        subgraph "Cloud Run Services"
            SERVICES[Microservices<br/>Internal Communication]
        end

        subgraph "Private Resources"
            FIRESTORE_PRIVATE[(Firestore<br/>Private IP)]
            PUBSUB_PRIVATE[Pub/Sub<br/>Private Access]
        end
    end

    subgraph "Identity & Access"
        FIREBASE_AUTH[Firebase Auth]
        CLOUD_IAM[Cloud IAM<br/>Service Accounts]
        SECRET_MGR[Secret Manager<br/>API Keys & Tokens]
    end

    subgraph "External Integrations"
        TWILIO_API[Twilio API<br/>HTTPS + Auth]
        HUBSPOT_API[HubSpot API<br/>HTTPS + Auth]
        AI_API[AI Engine<br/>HTTPS + Auth]
    end

    %% Security Flow
    USERS --> ARMOR
    EXTERNAL --> ARMOR
    ARMOR --> HTTPS_LB
    HTTPS_LB --> API_GATEWAY

    API_GATEWAY --> FIREBASE_AUTH
    API_GATEWAY --> SERVICES

    SERVICES --> CLOUD_IAM
    SERVICES --> SECRET_MGR
    SERVICES --> FIRESTORE_PRIVATE
    SERVICES --> PUBSUB_PRIVATE

    SERVICES -.->|HTTPS + Auth| TWILIO_API
    SERVICES -.->|HTTPS + Auth| HUBSPOT_API
    SERVICES -.->|HTTPS + Auth| AI_API

    classDef security fill:#ffebee
    classDef network fill:#e8f5e8
    classDef service fill:#e1f5fe
    classDef external fill:#f5f5f5

    class ARMOR,FIREBASE_AUTH,CLOUD_IAM,SECRET_MGR security
    class HTTPS_LB,API_GATEWAY,VPC network
    class SERVICES,FIRESTORE_PRIVATE,PUBSUB_PRIVATE service
    class TWILIO_API,HUBSPOT_API,AI_API,EXTERNAL external
```

---

## Diagram Usage Guide

### For Stakeholders
- **Overall Architecture (1)**: Complete system overview
- **Security Architecture (7)**: Security and compliance overview

### For Developers
- **Message Flow (2)**: Critical path understanding
- **Agent Activation (3)**: User interaction flows
- **Service Components (4)**: Internal service architecture
- **Event Architecture (5)**: Asynchronous communication patterns

### For DevOps/Infrastructure
- **Deployment Architecture (6)**: Infrastructure and scaling configuration
- **Security Architecture (7)**: Network and security setup

### Color Legend
- 🟢 **Green**: Core services and microservices
- 🔵 **Blue**: APIs, gateways, and frontend components
- 🟡 **Yellow**: Events, background processing, and messaging
- 🟣 **Purple**: Data layer and storage
- ⚪ **Gray**: External services and integrations
- 🔴 **Red**: Critical services and security components
