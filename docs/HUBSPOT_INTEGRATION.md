# HubSpot Integration

Connect your HubSpot CRM to import and manage contacts.

## Quick Setup

1. **Get HubSpot API Key**:
   - Go to HubSpot → Settings → Integrations → Private Apps
   - Create app with `crm.objects.contacts.read` scope
   - Copy access token

2. **Configure in Dashboard**:
   - Navigate to Integrations → HubSpot
   - Enter API Key and Portal ID
   - Test connection and fetch contacts

## Features

- Import contacts from HubSpot CRM
- Sync contact data automatically
- Test API connectivity
- WhatsApp integration with contacts

## Troubleshooting

- **Invalid API Key**: Check access token and scopes
- **Connection Failed**: Verify API key and portal ID
- **Rate Limits**: Service handles automatically with retry logic
- **Missing Contacts**: Ensure contacts exist in HubSpot

## Security

- Store API keys securely in environment variables
- Use minimal required scopes
- Regularly rotate access tokens
