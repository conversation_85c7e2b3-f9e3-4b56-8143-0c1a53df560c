# 🔌 API Documentation - Synapy Platform

## Overview

The Synapy platform exposes RESTful APIs through multiple microservices, each responsible for specific domain functionality. All APIs follow consistent patterns for authentication, error handling, and response formats.

## Base URLs

### Production
```
Agent Service:         https://agent-service-prod.run.app
Communications Service: https://communications-service-prod.run.app
Contact Service:       https://contact-service-prod.run.app
Analytics Service:     https://analytics-service-prod.run.app
Notification Service:  https://notification-service-prod.run.app
```

### Development
```
Agent Service:         http://localhost:8080
Communications Service: http://localhost:8083
Contact Service:       http://localhost:8081
Analytics Service:     http://localhost:8082
Notification Service:  http://localhost:8084
```

## Authentication

### Firebase Auth Token
All API requests require a valid Firebase Auth token in the Authorization header:

```http
Authorization: Bearer <firebase_auth_token>
```

### Service-to-Service Authentication
Internal service communication uses Google Cloud IAM service accounts with JWT tokens.

## Common Response Format

All APIs return responses in the following format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "error": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Agent Service API

### Endpoints

#### Get Agent
```http
GET /agents/{agent_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "agent_id": "agent_123",
    "name": "Customer Support Agent",
    "description": "AI agent for customer support",
    "configuration": {
      "model": "gpt-4",
      "temperature": 0.7,
      "max_tokens": 2000
    },
    "status": "active"
  }
}
```

#### Process Message (Webhook)
```http
POST /agents/{agent_id}/process
```

**Request:**
```json
{
  "message": "Hello, I need help with my order",
  "conversation_id": "conv_123",
  "user_context": {
    "phone_number": "+1234567890",
    "name": "John Doe"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Hello John! I'd be happy to help you with your order. Could you please provide your order number?",
    "conversation_id": "conv_123",
    "metadata": {
      "processing_time_ms": 1250,
      "model_used": "gpt-4"
    }
  }
}
```

## Communications Service API

### Endpoints

#### Activate Agent
```http
POST /whatsapp/activate-agent
```

**Request:**
```json
{
  "contact_phone": "+1234567890",
  "agent_id": "agent_123",
  "welcome_message": "Hello! I'm your AI assistant. How can I help you today?"
}
```

#### Deactivate Agent
```http
POST /whatsapp/deactivate-agent
```

**Request:**
```json
{
  "contact_phone": "+1234567890"
}
```

#### Send Message
```http
POST /whatsapp/send-message
```

**Request:**
```json
{
  "to": "+1234567890",
  "message": "Thank you for contacting us!",
  "conversation_id": "conv_123"
}
```

## Contact Service API

### Endpoints

#### Get All Contacts
```http
GET /contacts?user_id={user_id}&limit=100&offset=0&lead_status=nuevo
```

**Query Parameters:**
- `user_id` (required): User ID for user-specific contacts
- `limit` (optional): Number of contacts to return (1-1000, default: 100)
- `offset` (optional): Number of contacts to skip (default: 0)
- `lead_status` (optional): Filter by lead status
- `created_after` (optional): ISO date string
- `updated_after` (optional): ISO date string

**Response:**
```json
{
  "success": true,
  "data": {
    "contacts": [
      {
        "phone_number": "+1234567890",
        "hubspot_id": "12345",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "company": "Example Corp",
        "lead_status": "NEW",
        "created_at": "2024-01-01T00:00:00Z",
        "last_modified": "2024-01-02T00:00:00Z"
      }
    ],
    "pagination": {
      "limit": 100,
      "offset": 0,
      "total": 1,
      "has_more": false
    }
  }
}
```

#### Get Contact by Phone
```http
GET /contact/{phone_number}?user_id={user_id}
```

#### Update Lead Status
```http
POST /contact/lead-status
```

**Request:**
```json
{
  "contact_id": "+1234567890",
  "user_id": "user_123",
  "lead_status": "IN_PROGRESS",
  "reason": "Manual update from dashboard"
}
```

#### Configure HubSpot
```http
POST /hubspot/configure
```

**Request:**
```json
{
  "user_id": "user_123",
  "api_key": "hubspot_api_key",
  "enabled": true,
  "sync_frequency": 300,
  "properties": {
    "portal_id": "12345",
    "lead_pipeline": "default"
  }
}
```

**Response:**
```json
{
  "message": "HubSpot configuration updated successfully",
  "status": "configured",
  "user_id": "user_123"
}
```

## Analytics Service API

### Endpoints

#### Get Conversation Analytics
```http
GET /analytics/conversations/{conversation_id}
```

#### Get Lead Insights
```http
GET /analytics/leads?user_id={user_id}&date_from=2024-01-01&date_to=2024-01-31
```

#### Generate Report
```http
POST /analytics/reports
```

**Request:**
```json
{
  "user_id": "user_123",
  "report_type": "lead_conversion",
  "date_range": {
    "from": "2024-01-01",
    "to": "2024-01-31"
  },
  "filters": {
    "lead_status": ["nuevo", "en_curso"]
  }
}
```

## Error Codes

### Common Error Codes
- `INVALID_REQUEST`: Malformed request data
- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `INTERNAL_ERROR`: Server error

### Service-Specific Error Codes

#### Contact Service
- `CONTACT_NOT_FOUND`: Contact not found
- `HUBSPOT_CONFIG_ERROR`: HubSpot configuration error
- `HUBSPOT_API_ERROR`: HubSpot API error

#### Communications Service
- `TWILIO_ERROR`: Twilio API error
- `AGENT_NOT_ACTIVE`: No active agent for contact
- `MESSAGE_SEND_FAILED`: Failed to send message

#### Agent Service
- `AGENT_NOT_FOUND`: Agent not found
- `AI_PROCESSING_ERROR`: AI processing failed
- `CONFIGURATION_ERROR`: Agent configuration error

## Rate Limits

- **Standard endpoints**: 100 requests per minute per user
- **Webhook endpoints**: 1000 requests per minute
- **Analytics endpoints**: 50 requests per minute per user

## Webhooks

### Twilio Webhook (Communications Service)
```http
POST /webhooks/twilio
```

Receives incoming WhatsApp messages from Twilio.

### Agent Processing Webhook (Agent Service)
```http
POST /agents/{agent_id}/webhook
```

Receives message processing requests from Communications Service.

---

For more detailed API specifications, see the OpenAPI documentation available at each service's `/docs` endpoint.
