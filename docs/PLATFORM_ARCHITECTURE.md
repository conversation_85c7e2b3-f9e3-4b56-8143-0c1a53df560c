# 🏗️ AI Virtual Assistant Platform Architecture

## Overview

The Synapy platform follows an **event-driven microservices architecture** that ensures decoupling, scalability, and cost optimization. This modern architecture enables real-time AI-powered conversations through WhatsApp while maintaining robust integrations with CRM systems like HubSpot.

## Architecture Principles

- **Microservices Pattern**: Independent, loosely-coupled services
- **Event-Driven Communication**: Asynchronous messaging via Pub/Sub
- **Cloud-Native**: Built for Google Cloud Platform with auto-scaling
- **Cost-Optimized**: Pay-per-use model with intelligent scaling
- **Multi-Tenant**: User-specific data isolation and configurations

## System Components

### 1. Frontend Layer
- **Web Application**: Vue 3 + TypeScript + Vuetify
- **Hosting**: Firebase Hosting with global CDN
- **Authentication**: Firebase Auth with multi-provider support
- **State Management**: Pinia for reactive state management

### 2. API Gateway Layer
- **Cloud Load Balancer**: Traffic distribution and SSL termination
- **Cloud Endpoints**: API management with rate limiting and authentication
- **Request Routing**: Intelligent routing to appropriate microservices
- **API Versioning**: Support for multiple API versions

### 3. Core Microservices

#### Agent Service (Critical - Always On)
```
Responsibilities:
- AI agent CRUD operations
- Conversation processing with AI engine
- Webhook endpoint for message processing
- Agent configuration management
- Real-time AI response generation

Resources: 2 vCPU, 4GB RAM
Scaling: Min 1, Max 10 instances
```

#### Communications Service (Twilio Integration)
```
Responsibilities:
- WhatsApp conversation management
- Agent activation/deactivation for contacts
- Twilio webhook handling for incoming messages
- Message sending via Twilio API
- Conversation state management

Resources: 1 vCPU, 2GB RAM
Scaling: Min 0, Max 5 instances
```

#### Contact Service (HubSpot Integration)
```
Responsibilities:
- HubSpot contact synchronization
- Contact CRUD operations
- Lead status management
- Contact caching and optimization
- User-specific contact isolation

Resources: 1 vCPU, 1GB RAM
Scaling: Min 0, Max 3 instances
```

#### Analytics Service (NEW - AI Analysis)
```
Responsibilities:
- Real-time conversation analysis with AI
- Lead scoring and status updates
- Key insight extraction
- Conversation metrics and reporting
- Automated lead progression

Resources: 2 vCPU, 2GB RAM
Scaling: Min 0, Max 5 instances
```

#### Notification Service
```
Responsibilities:
- Multi-channel notifications (email, SMS, webhooks)
- Alert management and escalation
- External webhook integrations
- Notification preferences management

Resources: 0.5 vCPU, 512MB RAM
Scaling: Min 0, Max 3 instances
```

### 4. Event Streaming & Messaging

#### Cloud Pub/Sub Topics
```
- message.received      → Analytics Service
- message.sent          → Analytics Service
- agent.activated       → Analytics Service, Notification Service
- agent.deactivated     → Notification Service
- lead.status.updated   → Contact Service, Notification Service
- conversation.ended    → Analytics Service
```

### 5. Data Layer

#### Firestore Collections (User-Specific)
```
users/{userId}/
├── integrations/
│   ├── hubspot/           # User-specific HubSpot config
├── contacts/              # User-specific contacts
│   └── {phone_number}/    # Individual contact data
└── conversations/         # User conversation history
    └── {conversation_id}/ # Individual conversations

Global Collections:
├── agents/                # AI agent configurations
├── analytics/             # Analysis results
└── system_config/         # System-wide settings
```

#### Firebase Storage
```
- /assets/              # Static application assets
- /uploads/             # User-uploaded files
- /reports/             # Generated analytics reports
```

#### Cloud Storage (Optional)
```
- /conversation-logs/   # Long-term conversation storage
- /ml-models/          # AI model artifacts
- /backups/            # Data backups
```

### 6. Background Processing

#### Cloud Functions (2nd Generation)
```
- hubspot-sync-trigger    # Scheduled HubSpot synchronization
- conversation-cleanup    # Historical data cleanup
- report-generator       # Periodic analytics reports
- webhook-retries        # Failed webhook retry logic
```

#### Cloud Tasks
```
- message-processing-queue  # Deferred message processing
- analytics-processing     # Batch conversation analysis
- notification-queue       # Notification delivery
```

## Key Data Flows

### Incoming Message Flow
```
1. WhatsApp → Twilio → Communications Service webhook
2. Communications Service → Find active agent (Firestore)
3. Communications Service → Publish message.received event
4. Communications Service → Call Agent Service webhook
5. Agent Service → Process with AI → Generate response
6. Communications Service → Send response via Twilio
7. Analytics Service → Process message (async via Pub/Sub)
8. Analytics Service → Update lead status if needed
```

### Agent Activation Flow
```
1. Frontend → API Gateway → Communications Service
2. Communications Service → Agent Service (get configuration)
3. Communications Service → Update Firestore
4. Communications Service → Send initial message via Twilio
5. Communications Service → Publish agent.activated event
```

## Security & Compliance

### Authentication & Authorization
- **Firebase Auth**: Multi-provider authentication
- **Cloud IAM**: Service-to-service authentication
- **API Keys**: External service authentication
- **JWT Tokens**: Secure inter-service communication

### Data Protection
- **User Data Isolation**: Complete separation of user data
- **Encryption**: Data encrypted at rest and in transit
- **Access Controls**: Principle of least privilege
- **Audit Logging**: Comprehensive activity logging

### Network Security
- **VPC Networks**: Isolated network environments
- **Cloud Armor**: DDoS protection and WAF
- **Private IPs**: Internal service communication
- **SSL/TLS**: End-to-end encryption

## Monitoring & Observability

### Application Monitoring
- **Cloud Monitoring**: Real-time metrics and alerting
- **Cloud Logging**: Centralized log aggregation
- **Cloud Trace**: Distributed request tracing
- **Error Reporting**: Automatic error detection

### Business Metrics
- **Conversation Analytics**: Response times, success rates
- **Lead Conversion**: Tracking from contact to conversion
- **System Performance**: Service health and availability
- **Cost Optimization**: Resource usage and optimization

## Deployment & CI/CD

### Containerization
- **Docker**: Containerized microservices
- **Cloud Run**: Serverless container platform
- **Artifact Registry**: Container image storage

### Continuous Integration/Deployment
- **GitHub Actions**: Automated testing and deployment
- **Cloud Build**: Container building and deployment
- **Environment Promotion**: Dev → Staging → Production

## Cost Optimization

### Scaling Strategy
- **Auto-scaling**: Based on demand and metrics
- **Cold Start Optimization**: Minimized startup times
- **Resource Right-sizing**: Optimal CPU/memory allocation

### Pay-per-Use Model
- **Cloud Run**: Pay only for actual usage
- **Pub/Sub**: Message-based pricing
- **Firestore**: Document-based pricing
- **Cloud Functions**: Execution-based pricing

## Migration Strategy

### From Firebase Functions to Microservices
1. **Phase 1**: WhatsApp and HubSpot operations (✅ Complete)
2. **Phase 2**: Assistant and Knowledge Base operations (Planned)
3. **Phase 3**: Complete Firebase Functions deprecation (Future)

### Data Migration
- **User-Specific Collections**: Gradual migration to user-isolated data
- **Zero-Downtime**: Rolling updates with health checks

---

For detailed implementation guides and diagrams, see:
- [Architecture Diagrams](./ARCHITECTURE_DIAGRAMS.md)
- [Deployment Guide](./DEPLOYMENT_INSTRUCTIONS.md)
- [API Documentation](./API_DOCUMENTATION.md)
