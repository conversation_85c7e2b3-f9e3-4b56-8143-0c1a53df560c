"""
Firebase Functions for Knowledge Base operations.
This module defines the HTTP endpoints for knowledge base-related operations.
"""

from src.infrastructure.http import authenticated_firebase_function
from src.app.handlers import KnowledgeBaseHandlers

# Create handler instance
_knowledge_base_handlers = KnowledgeBaseHandlers()

@authenticated_firebase_function
def create_knowledge_base_http(request_data: dict) -> dict:
    """Create a new knowledge base."""
    return _knowledge_base_handlers.create_knowledge_base(request_data)

@authenticated_firebase_function
def add_knowledge_base_sources_http(request_data: dict) -> dict:
    """Add sources to an existing knowledge base."""
    return _knowledge_base_handlers.add_knowledge_base_sources(request_data)

@authenticated_firebase_function
def delete_knowledge_base_source_http(request_data: dict) -> dict:
    """Delete a source from a knowledge base."""
    return _knowledge_base_handlers.delete_knowledge_base_source(request_data)

@authenticated_firebase_function
def get_knowledge_base_details_http(request_data: dict) -> dict:
    """Get knowledge base details by ID."""
    return _knowledge_base_handlers.get_knowledge_base_details(request_data)

@authenticated_firebase_function
def delete_knowledge_base_http(request_data: dict) -> dict:
    """Delete a knowledge base by ID."""
    return _knowledge_base_handlers.delete_knowledge_base(request_data)

@authenticated_firebase_function
def fetch_knowledge_bases_http(request_data: dict) -> dict:
    """Fetch all knowledge bases for a user."""
    return _knowledge_base_handlers.fetch_knowledge_bases(request_data)
