from typing import Optional
from src.domain.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Assistant, BusinessConfig, LLMConfig, AgentConfig

class UpdateAssistantRequest(SerializableModel):
    assistant_id: str
    assistant_data: dict

    def to_domain(self) -> Assistant:
        """Convert request to domain model."""
        data = self.assistant_data
        
        # Extract business config if present
        business_config = None
        if any(key in data for key in ['assistant_name', 'company_name', 'time_zone', 'notice_hours', 
                                       'phone_prefix', 'time_slot_options', 'time_slot_interval', 
                                       'support_email', 'support_number']):
            business_config = BusinessConfig(
                assistant_name=data.get('assistant_name'),
                company_name=data.get('company_name'),
                time_zone=data.get('time_zone'),
                notice_hours=data.get('notice_hours'),
                phone_prefix=data.get('phone_prefix'),
                time_slot_options=data.get('time_slot_options'),
                time_slot_interval=data.get('time_slot_interval'),
                support_email=data.get('support_email'),
                support_number=data.get('support_number'),
            )
        
        # Extract LLM config if present
        llm_config = None
        if any(key in data for key in ['model', 'model_temperature', 'begin_message', 
                                       'knowledge_base_ids', 'general_prompt']):
            llm_config = LLMConfig(
                model=data.get('model'),
                model_temperature=data.get('model_temperature'),
                begin_message=data.get('begin_message'),
                knowledge_base_ids=data.get('knowledge_base_ids'),
                general_prompt=data.get('general_prompt'),
            )
        
        # Extract agent config if present
        agent_config = None
        if any(key in data for key in ['voice_temperature', 'voice_speed', 'responsiveness', 
                                       'backchannel_words', 'ambient_sound', 'ambient_sound_volume',
                                       'language', 'voice_id', 'voice_model']):
            agent_config = AgentConfig(
                voice_temperature=data.get('voice_temperature'),
                voice_speed=data.get('voice_speed'),
                responsiveness=data.get('responsiveness'),
                backchannel_words=data.get('backchannel_words'),
                ambient_sound=data.get('ambient_sound'),
                ambient_sound_volume=data.get('ambient_sound_volume'),
                language=data.get('language'),
                voice_id=data.get('voice_id'),
                voice_model=data.get('voice_model'),
            )
        
        return Assistant(
            id=self.assistant_id,
            user_id=data.get('user_id'),
            type=data.get('type'),
            business_config=business_config,
            llm_config=llm_config,
            agent_config=agent_config,
        )
