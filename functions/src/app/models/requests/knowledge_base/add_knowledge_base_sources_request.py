from typing import Optional, List
from src.domain.models import SerializableModel, KnowledgeBaseSource

class KnowledgeBaseTextInputRequest(SerializableModel):
    text: str
    title: Optional[str] = None

class KnowledgeBaseFileInputRequest(SerializableModel):
    filename: str
    url: str

class AddKnowledgeBaseSourcesRequest(SerializableModel):
    knowledge_base_id: str
    knowledge_base_texts: Optional[List[KnowledgeBaseTextInputRequest]] = None
    knowledge_base_urls: Optional[List[str]] = None
    knowledge_base_file_urls: Optional[List[KnowledgeBaseFileInputRequest]] = None  # File objects with filename and URL

    def to_domain_sources(self) -> List[KnowledgeBaseSource]:
        """Convert request data to domain sources."""
        sources = []

        # Convert text inputs to sources
        if self.knowledge_base_texts:
            for text_input in self.knowledge_base_texts:
                sources.append(KnowledgeBaseSource(
                    type="text",
                    name=text_input.title,
                    text=text_input.text,
                ))

        # Convert URLs to sources
        if self.knowledge_base_urls:
            for url in self.knowledge_base_urls:
                sources.append(KnowledgeBaseSource(
                    type="url",
                    url=url,
                ))

        # Convert file data to sources
        if self.knowledge_base_file_urls:
            for file_data in self.knowledge_base_file_urls:
                sources.append(KnowledgeBaseSource(
                    type="document",
                    name=file_data.filename,
                    url=file_data.url,
                ))

        return sources
