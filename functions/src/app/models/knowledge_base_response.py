from typing import Optional, List
from src.domain.models import SerializableModel, KnowledgeBase, KnowledgeBaseSource

class KnowledgeBaseSourceResponse(SerializableModel):
    type: Optional[str] = None
    source_id: Optional[str] = None
    name: Optional[str] = None
    url: Optional[str] = None

    @classmethod
    def from_domain(cls, source: KnowledgeBaseSource) -> "KnowledgeBaseSourceResponse":
        return cls(
            type=source.type,
            source_id=source.source_id,
            name=source.name,
            url=source.url,
        )

class KnowledgeBaseResponse(SerializableModel):
    knowledge_base_id: Optional[str] = None
    user_id: Optional[str] = None
    external_knowledge_base_id: Optional[str] = None
    knowledge_base_name: Optional[str] = None
    knowledge_base_type: Optional[str] = None
    custom_trigger: Optional[str] = None
    status: Optional[str] = None
    sources: Optional[List[KnowledgeBaseSourceResponse]] = None
    last_refreshed_timestamp: Optional[int] = None

    def to_domain(self) -> KnowledgeBase:
        sources = None
        if self.sources:
            sources = [KnowledgeBaseSource(
                type=source.type,
                source_id=source.source_id,
                name=source.name,
                url=source.url,
            ) for source in self.sources]

        return KnowledgeBase(
            id=self.knowledge_base_id,
            user_id=self.user_id,
            knowledge_base_id=self.external_knowledge_base_id,
            name=self.knowledge_base_name,
            type=self.knowledge_base_type,
            custom_trigger=self.custom_trigger,
            status=self.status,
            sources=sources,
            last_refreshed_timestamp=self.last_refreshed_timestamp,
        )

    @classmethod
    def from_domain(cls, knowledge_base: KnowledgeBase) -> "KnowledgeBaseResponse":
        sources = None
        if knowledge_base.sources:
            sources = [KnowledgeBaseSourceResponse.from_domain(source) for source in knowledge_base.sources]

        return cls(
            knowledge_base_id=knowledge_base.id,
            user_id=knowledge_base.user_id,
            external_knowledge_base_id=knowledge_base.knowledge_base_id,
            knowledge_base_name=knowledge_base.name,
            knowledge_base_type=knowledge_base.type,
            custom_trigger=knowledge_base.custom_trigger,
            status=knowledge_base.status,
            sources=sources,
            last_refreshed_timestamp=knowledge_base.last_refreshed_timestamp,
        )
