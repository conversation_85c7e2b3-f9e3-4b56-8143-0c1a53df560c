from typing import List
from abc import ABC, abstractmethod
from src.domain.models import KnowledgeBase, KnowledgeBaseSource

class KnowledgeBaseRepository(ABC):

    @abstractmethod
    def save_knowledge_base(self, knowledge_base: KnowledgeBase) -> KnowledgeBase:
        pass

    @abstractmethod
    def add_knowledge_base_sources(self, knowledge_base_id: str, sources: List[KnowledgeBaseSource]) -> KnowledgeBase:
        pass

    @abstractmethod
    def delete_knowledge_base_source(self, knowledge_base_id: str, source_id: str) -> KnowledgeBase:
        pass

    @abstractmethod
    def get_knowledge_base(self, knowledge_base_id: str) -> KnowledgeBase:
        pass

    @abstractmethod
    def find_knowledge_bases(self, filters: dict) -> List[KnowledgeBase]:
        pass

    @abstractmethod
    def delete_knowledge_base(self, knowledge_base_id: str) -> None:
        pass
