from typing import List
from abc import ABC, abstractmethod
from src.domain.models import Assistant

class AssistantRepository(ABC):

    @abstractmethod
    def save_assistant(self, assistant: Assistant) -> Assistant:
        pass

    @abstractmethod
    def update_assistant(self, assistant: Assistant) -> None:
        pass

    @abstractmethod
    def get_assistant(self, assistant_id: str) -> Assistant:
        pass

    @abstractmethod
    def find_assistants(self, filters: dict) -> List[Assistant]:
        pass

    @abstractmethod
    def delete_assistant(self, assistant_id: str) -> None:
        pass
