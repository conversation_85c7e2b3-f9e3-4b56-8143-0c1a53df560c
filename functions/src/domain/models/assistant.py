from .base import SerializableModel
from typing import Optional, List

class BusinessConfig(SerializableModel):
    assistant_name: Optional[str] = None
    company_name: Optional[str] = None
    time_zone: Optional[str] = None
    notice_hours: Optional[int] = None
    phone_prefix: Optional[str] = None
    time_slot_options: Optional[int] = None
    time_slot_interval: Optional[int] = None
    support_email: Optional[str] = None
    support_number: Optional[str] = None

class LLMConfig(SerializableModel):
    llm_id: Optional[str] = None
    model: Optional[str] = None
    model_temperature: Optional[float] = None
    begin_message: Optional[str] = None
    knowledge_base_ids: Optional[List[str]] = None
    general_prompt: Optional[str] = None

class AgentConfig(SerializableModel):
    agent_id: Optional[str] = None
    voice_temperature: Optional[float] = None
    voice_speed: Optional[float] = None
    responsiveness: Optional[float] = None
    backchannel_words: Optional[List[str]] = None
    ambient_sound: Optional[str] = None
    ambient_sound_volume: Optional[float] = None
    language: Optional[str] = None
    voice_id: Optional[str] = None
    voice_model: Optional[str] = None

class Assistant(SerializableModel):
    id: Optional[str] = None
    user_id: Optional[str] = None
    agent_id: Optional[str] = None
    type: Optional[str] = None
    llm_id: Optional[str] = None
    business_config: Optional[BusinessConfig] = None
    llm_config: Optional[LLMConfig] = None
    agent_config: Optional[AgentConfig] = None
