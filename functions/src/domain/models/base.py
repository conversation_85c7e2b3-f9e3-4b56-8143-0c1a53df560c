from pydantic import BaseModel

class SerializableModel(BaseModel):
  model_config = {
    "arbitrary_types_allowed": True
  }

  def to_dict(self) -> dict:
    """Return dict excluding fields with None values."""
    return self.model_dump(exclude_none=True)

  def to_json(self) -> str:
    """Return a JSON string of the payload (excluding None)."""
    return self.model_dump_json(exclude_none=True)

  def __str__(self) -> str:
    return self.to_json()
