from typing import List
from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from src.infrastructure import FirestoreKnowledgeBaseRepository, RetellKnowledgeBaseRepository
from src.infrastructure.firebase import FirestoreError
from src.infrastructure.retell import RetellAPIError
from src.shared.logging import Logger

class KnowledgeBaseService:
    def __init__(self):
        self.firestore = FirestoreKnowledgeBaseRepository()
        self.retell = RetellKnowledgeBaseRepository()

    def create(self, knowledge_base: KnowledgeBase) -> KnowledgeBase:
        """Create a new knowledge base."""
        try:
            # First create in Retell AI to get the external ID
            knowledge_base = self.retell.save_knowledge_base(knowledge_base)
            # Then save to Firestore with the external ID
            knowledge_base = self.firestore.save_knowledge_base(knowledge_base)

            return knowledge_base
        except RetellAPIError as e:
            # If Retell creation fails, don't save to Firestore
            raise e
        except FirestoreError as e:
            # If Firestore save fails, clean up Retell
            if knowledge_base.knowledge_base_id:
                try:
                    self.retell.delete_knowledge_base(knowledge_base.knowledge_base_id)
                except Exception:
                    pass  # Best effort cleanup
            raise e

    def add_sources(self, knowledge_base_id: str, sources: List[KnowledgeBaseSource]) -> KnowledgeBase:
        """Add sources to an existing knowledge base."""
        try:
            # Get the knowledge base to find the external ID
            knowledge_base = self.firestore.get_knowledge_base(knowledge_base_id)

            # Add sources to Retell first if we have an external ID
            if knowledge_base.knowledge_base_id:
                updated_kb = self.retell.add_knowledge_base_sources(knowledge_base.knowledge_base_id, sources)
                # Update Firestore with the new sources
                self.firestore.add_knowledge_base_sources(knowledge_base_id, sources)
                return updated_kb
            else:
                # If no external ID, just update Firestore
                return self.firestore.add_knowledge_base_sources(knowledge_base_id, sources)
        except RetellAPIError as e:
            # If Retell fails, still update Firestore
            self.firestore.add_knowledge_base_sources(knowledge_base_id, sources)
            raise e

    def delete_source(self, knowledge_base_id: str, source_id: str) -> KnowledgeBase:
        """Delete a source from a knowledge base."""
        try:
            # Get the knowledge base to find the external ID
            knowledge_base = self.firestore.get_knowledge_base(knowledge_base_id)

            # Delete source from Retell first if we have an external ID
            if knowledge_base.knowledge_base_id:
                updated_kb = self.retell.delete_knowledge_base_source(knowledge_base.knowledge_base_id, source_id)
                # Update Firestore
                self.firestore.delete_knowledge_base_source(knowledge_base_id, source_id)
                return updated_kb
            else:
                # If no external ID, just update Firestore
                return self.firestore.delete_knowledge_base_source(knowledge_base_id, source_id)
        except RetellAPIError as e:
            # If Retell fails, still update Firestore
            self.firestore.delete_knowledge_base_source(knowledge_base_id, source_id)
            raise e

    def get(self, knowledge_base_id: str) -> KnowledgeBase:
        """Retrieve a knowledge base by its ID."""
        # Get from Firestore (which has our internal data)
        knowledge_base = self.firestore.get_knowledge_base(knowledge_base_id)

        # If we have an external ID, get the latest status from Retell
        if knowledge_base.knowledge_base_id:
            try:
                retell_kb = self.retell.get_knowledge_base(knowledge_base.knowledge_base_id)
                # Update status and other dynamic fields from Retell
                knowledge_base.status = retell_kb.status
                knowledge_base.sources = retell_kb.sources
                knowledge_base.last_refreshed_timestamp = retell_kb.last_refreshed_timestamp
            except RetellAPIError:
                # If Retell fails, return what we have in Firestore
                pass

        return knowledge_base

    def fetch_by_user(self, user_id: str) -> List[KnowledgeBase]:
        """Fetch all knowledge bases associated with a user."""
        filters = {"userId": user_id}
        return self.firestore.find_knowledge_bases(filters)

    def delete(self, knowledge_base_id: str) -> None:
        """Delete a knowledge base by its ID."""
        # Get the knowledge base to find the external ID
        knowledge_base = self.firestore.get_knowledge_base(knowledge_base_id)

        # Delete from Retell first if we have an external ID
        if knowledge_base.knowledge_base_id:
            try:
                self.retell.delete_knowledge_base(knowledge_base.knowledge_base_id)
            except RetellAPIError:
                # Continue with Firestore deletion even if Retell fails
                pass

        # Delete from Firestore
        self.firestore.delete_knowledge_base(knowledge_base_id)
