class AppError(Exception):
    """Base exception for all application-level errors."""
    def __init__(self, message: str, *, code: str = "unknown_error", cause: Exception | None = None):
        super().__init__(message)
        self.message = message
        self.code = code
        self.cause = cause

    def __str__(self):
        return f"[{self.code}] {self.message}" + (f" | Cause: {self.cause}" if self.cause else "")

class BadRequestError(AppError):
    def __init__(self, message: str = "Bad request", cause: Exception | None = None):
        super().__init__(message, code="bad_request", cause=cause)
