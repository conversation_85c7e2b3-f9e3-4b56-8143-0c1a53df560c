from firebase_functions import logger as firebase_logger
from src.domain.models import SerializableModel

class Logger:
  @staticmethod
  def _sanitize_args(*args):
    return [str(arg) if isinstance(arg, SerializableModel) else arg for arg in args]

  @staticmethod
  def _prepend_emoji_if_missing(emoji: str, args: list):
    if not args:
      return args
    first_arg = args[0]
    if isinstance(first_arg, str) and not first_arg.startswith(emoji):
      args = [f"{emoji} {first_arg}", *args[1:]]
    return args

  @staticmethod
  def debug(*args, **kwargs):
    firebase_logger.debug(*Logger._sanitize_args(*args), **kwargs)

  @staticmethod
  def info(*args, **kwargs):
    firebase_logger.info(*Logger._sanitize_args(*args), **kwargs)

  @staticmethod
  def warn(*args, **kwargs):
    args = Logger._prepend_emoji_if_missing("⚠️", list(args))
    firebase_logger.warn(*Logger._sanitize_args(*args), **kwargs)

  @staticmethod
  def error(*args, **kwargs):
    args = Logger._prepend_emoji_if_missing("❌", list(args))
    firebase_logger.error(*Logger._sanitize_args(*args), **kwargs)

  @staticmethod
  def write(severity: int, *args, **kwargs):
    firebase_logger.write(severity, *Logger._sanitize_args(*args), **kwargs)
