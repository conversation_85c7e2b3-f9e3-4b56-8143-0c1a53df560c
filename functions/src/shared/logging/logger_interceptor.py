import logging
from .logger import Logger

class FirebaseLogHandler(logging.Handler):
    def emit(self, record):
        log_entry = self.format(record)
        level = record.levelname.lower()
        if level == "debug":
            Logger.debug(log_entry)
        elif level == "info":
            Logger.info(log_entry)
        elif level == "warning":
            Logger.warn(log_entry)
        elif level == "error":
            Logger.error(log_entry)
        else:
            Logger.write(record.levelno, log_entry)

def setup_external_logger_interceptor():
    external_loggers = ["httpx", "google.auth", "urllib3"]

    handler = FirebaseLogHandler()
    handler.setFormatter(logging.Formatter("[%(name)s] %(message)s"))

    for name in external_loggers:
        log = logging.getLogger(name)
        log.setLevel(logging.INFO)
        log.addHandler(handler)
        log.propagate = False
