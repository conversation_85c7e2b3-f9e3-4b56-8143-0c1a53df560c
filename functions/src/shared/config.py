from dotenv import load_dotenv
import firebase_admin
from firebase_admin import initialize_app
from .logging import setup_external_logger_interceptor

def initialize():
    """Initialize the Firebase app and load environment variables."""
    # Load environment variables from .env file
    # This is useful for local development
    # and testing, but in production, you should set these variables
    # in your environment or use a secret management service.
    load_dotenv()
    _initialize_firebase()
    setup_external_logger_interceptor()

def _initialize_firebase():
    """Initialize Firebase Admin SDK."""
    if not firebase_admin._apps:
        initialize_app()