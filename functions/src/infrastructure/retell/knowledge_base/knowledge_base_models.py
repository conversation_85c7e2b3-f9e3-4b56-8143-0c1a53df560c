import io
from typing import List, Optional, Any
from src.domain.models import SerializableModel

# Shared models for both request and response
class KnowledgeBaseSourceResponse(SerializableModel):
    """Knowledge base source as returned by the API."""
    type: Optional[str] = None  # "document", "url", "text"
    source_id: Optional[str] = None
    filename: Optional[str] = None
    file_url: Optional[str] = None

class KnowledgeBaseTextInput(SerializableModel):
    """Text input for knowledge base creation."""
    text: str
    title: Optional[str] = None

# REQUEST MODELS (for API input)
class CreateKnowledgeBaseRequest(SerializableModel):
    """Request model for creating a knowledge base."""
    knowledge_base_name: str
    knowledge_base_texts: Optional[List[KnowledgeBaseTextInput]] = None
    knowledge_base_urls: Optional[List[str]] = None
    knowledge_base_files: Optional[List[io.IOBase]] = None
    enable_auto_refresh: Optional[bool] = None

    def to_dict(self) -> dict:
      return self.model_dump(exclude={"knowledge_base_files"}, exclude_none=True)

class AddKnowledgeBaseSourcesRequest(SerializableModel):
    """Request model for adding sources to a knowledge base."""
    knowledge_base_texts: Optional[List[KnowledgeBaseTextInput]] = None
    knowledge_base_urls: Optional[List[str]] = None
    knowledge_base_files: Optional[List[io.IOBase]] = None

    def to_dict(self) -> dict:
      return self.model_dump(exclude={"knowledge_base_files"}, exclude_none=True)

# RESPONSE MODELS (for API output)
class KnowledgeBaseResponse(SerializableModel):
    """Response model for knowledge base operations."""
    knowledge_base_id: str
    knowledge_base_name: str
    status: str  # "in_progress", "ready", "error"
    knowledge_base_sources: Optional[List[KnowledgeBaseSourceResponse]] = None
    enable_auto_refresh: Optional[bool] = None
    last_refreshed_timestamp: Optional[int] = None


