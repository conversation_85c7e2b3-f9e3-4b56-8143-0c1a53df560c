from typing import List
from src.shared.util import has_values
from src.domain.ports import AssistantRepository
from src.domain.models import Assistant
from .assistant_client import RetellAssistantClient
from .assistant_mappers import to_retell_agent_request, to_retell_llm_request, to_assistant_llm_config, to_assistant_agent_config
from ..shared.exceptions import RetellAPIError

class RetellAssistantRepository(AssistantRepository):

    def __init__(self):
        self.client = RetellAssistantClient()

    def save_assistant(self, assistant: Assistant) -> Assistant:
        """Create a new assistant in Retell AI."""
        retell_llm_response = None
        try:
          retell_llm_request = to_retell_llm_request(assistant)
          retell_llm_response = self.client.create_retell_llm(retell_llm_request)

          assistant.llm_id = retell_llm_response.llm_id
          assistant.llm_config = to_assistant_llm_config(retell_llm_response)

          retell_agent_request = to_retell_agent_request(assistant)
          retell_agent_response = self.client.create_retell_agent(retell_agent_request)

          assistant.agent_id = retell_agent_response.agent_id
          assistant.agent_config = to_assistant_agent_config(retell_agent_response)

          return assistant
        except RetellAPIError as e:
            if retell_llm_response and retell_llm_response.llm_id:
                try:
                    self.client.delete_retell_llm(retell_llm_response.llm_id)
                except RetellAPIError as cleanup_error:
                    raise ValueError(f"Error cleaning up LLM after failure: {cleanup_error}") from e
            raise e

    def get_assistant(self, assistant_id: str) -> Assistant:
        """Retrieve an assistant by its ID. Have in mind the assistant_id must be the agent_id in Retell."""
        retell_agent = self.client.get_retell_agent(assistant_id)
        agent_config = to_assistant_agent_config(retell_agent)

        llm_id = retell_agent.get_llm_id()
        retell_llm = self.client.get_retell_llm(llm_id)
        llm_config = to_assistant_llm_config(retell_llm)

        return Assistant(
            agent_id      = assistant_id,
            llm_id        = llm_id,
            agent_config  = agent_config,
            llm_config    = llm_config
        )

    def delete_assistant(self, assistant_id: str) -> None:
        """Delete an assistant by its ID. Have in mind the assistant_id must be the agent_id in Retell."""
        retell_agent = self.client.get_retell_agent(assistant_id)
        llm_id = retell_agent.get_llm_id()

        self.client.delete_retell_agent(assistant_id)
        self.client.delete_retell_llm(llm_id)

    def update_assistant(
        self,
        assistant_id: str,
        assistant: Assistant,
    ) -> None:
        """Update an assistant in Retell AI. Have in mind the assistant_id must be the agent_id in Retell."""
        errors = []

        if has_values(assistant.agent_config):
            try:
                retell_agent_request = to_retell_agent_request(assistant)
                self.client.update_retell_agent(assistant_id, retell_agent_request)
            except RetellAPIError as e:
                errors.append(f"Agent update failed: {e}")

        if has_values(assistant.llm_config):
            try:
                retell_llm_request = to_retell_llm_request(assistant)
                self.client.update_retell_llm(assistant.llm_id, retell_llm_request)
            except RetellAPIError as e:
                errors.append(f"LLM update failed: {e}")

        if errors:
            raise RetellAPIError(" | ".join(errors))

    def find_assistants(self, _filters: dict) -> List[Assistant]:
        # Note: Retell API doesn't support filtering, so filters parameter is ignored
        del _filters  # Suppress unused parameter warning
        raise NotImplementedError("Retell API doesn't support listing agents with filters")
