import os
import json
from .exceptions import RetellAPIError

class RetellClient:
    """Base Retell API client providing common functionality."""
    
    def __init__(self):
        from retell import Retell
        self.client = Retell(api_key=os.getenv("RETELL_API_KEY"))
    
    def _handle_exception(self, error: Exception) -> None:
        """Handle Retell API exceptions with proper error formatting."""
        response = getattr(error, "response", None)
        raw_body = getattr(response, "text", None)

        try:
            error_data = json.loads(raw_body) if raw_body else {}
            message = error_data.get("message", str(error))
            status = error_data.get("status", "error")
            raise RetellAPIError(
                message=f"{status.upper()} - {message}",
                status_code=getattr(response, "status_code", 500),
                cause=error
            )
        except Exception:
            raise RetellAPIError(
                message=f"Retell API failed. Raw body: {raw_body or 'No body'}",
                status_code=getattr(response, "status_code", 500),
                cause=error
            )
