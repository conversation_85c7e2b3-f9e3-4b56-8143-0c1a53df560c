from src.domain.exceptions import AppError

class RetellAPIError(AppError):
    def __init__(self, message: str, *, status_code: int | None = None, cause: Exception | None = None):
        code = "retell_api_error"
        if status_code:
            code = f"retell_{status_code}"
        super().__init__(message, code=code, cause=cause)
        self.code = code

class RetellAgentCreationError(RetellAPIError):
    def __init__(self, reason: str, cause: Exception | None = None):
        super().__init__(f"Failed to create Retell agent: {reason}", cause=cause)

class RetellKnowledgeBaseError(RetellAPIError):
    def __init__(self, reason: str, cause: Exception | None = None):
        super().__init__(f"Failed to manage Retell knowledge base: {reason}", cause=cause)
