from src.domain.models import Assistant, BusinessConfig
from .assistant_models import FirestoreAssistantData

def to_firestore_assistant_model(assistant: Assistant, partial: bool = False) -> FirestoreAssistantData:
    if partial:
        data = {}
        mapping = {
            "user_id": "user_id",
            "llm_id": "llm_id",
            "agent_id": "agent_id",
            "type": "type",
        }

        for domain_key, firestore_key in mapping.items():
            value = getattr(assistant, domain_key, None)
            if value is not None:
                data[firestore_key] = value

        business = assistant.business_config
        if business:
            data.update(_business_config_to_dict(business))

        return FirestoreAssistantData(**data)

    business = assistant.business_config
    business_kwargs = _business_config_to_dict(business) if business else {}

    return FirestoreAssistantData(
        user_id=assistant.user_id,
        llm_id=assistant.llm_id,
        agent_id=assistant.agent_id,
        type=assistant.type,
        **{field: business_kwargs.get(field, None) for field in [
            "assistant_name",
            "company_name",
            "time_zone",
            "notice_hours",
            "phone_prefix",
            "time_slot_options",
            "time_slot_interval",
            "support_email",
            "support_number",
        ]}
    )

def to_assistant_domain(data: FirestoreAssistantData) -> Assistant:
    return Assistant(
        id=data.id,
        user_id=data.user_id,
        type=data.type,
        llm_id=data.llm_id,
        agent_id=data.agent_id,
        business_config=BusinessConfig(
            assistant_name=data.agent_name,
            company_name=data.company_name,
            time_zone=data.time_zone,
            notice_hours=data.notice_hours,
            phone_prefix=data.phone_prefix,
            time_slot_options=data.time_slot_options,
            time_slot_interval=data.time_slot_interval,
            support_email=data.support_email,
            support_number=data.support_number,
        )
    )

def _business_config_to_dict(business: BusinessConfig) -> dict:
    mapping = {
        "assistant_name": "assistant_name",
        "company_name": "company_name",
        "time_zone": "time_zone",
        "notice_hours": "notice_hours",
        "phone_prefix": "phone_prefix",
        "time_slot_options": "time_slot_options",
        "time_slot_interval": "time_slot_interval",
        "support_email": "support_email",
        "support_number": "support_number",
    }
    return {
        firestore_key: getattr(business, domain_key)
        for domain_key, firestore_key in mapping.items()
        if getattr(business, domain_key, None) is not None
    }
