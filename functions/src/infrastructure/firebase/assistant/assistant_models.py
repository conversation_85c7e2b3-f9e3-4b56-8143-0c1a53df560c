from typing import Optional
from src.domain.models import SerializableModel

class FirestoreAssistantData(SerializableModel):
    id: Optional[str] = None
    user_id: Optional[str] = None
    type: Optional[str] = None
    llm_id: Optional[str] = None
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    company_name: Optional[str] = None
    time_zone: Optional[str] = None
    notice_hours: Optional[int] = None
    phone_prefix: Optional[str] = None
    time_slot_options: Optional[int] = None
    time_slot_interval: Optional[int] = None
    support_email: Optional[str] = None
    support_number: Optional[str] = None
