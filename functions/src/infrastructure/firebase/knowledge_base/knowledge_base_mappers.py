from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from .knowledge_base_models import FirestoreKnowledgeBaseData, FirestoreKnowledgeBaseSourceData

def to_firestore_knowledge_base_model(knowledge_base: KnowledgeBase, partial: bool = False) -> FirestoreKnowledgeBaseData:
    if partial:
        data = {}
        mapping = {
            "user_id": "user_id",
            "knowledge_base_id": "knowledge_base_id",
            "name": "name",
            "type": "type",
            "custom_trigger": "custom_trigger",
            "status": "status",
            "last_refreshed_timestamp": "last_refreshed_timestamp",
        }

        for domain_key, firestore_key in mapping.items():
            value = getattr(knowledge_base, domain_key, None)
            if value is not None:
                data[firestore_key] = value

        if knowledge_base.sources:
            data["sources"] = [_knowledge_base_source_to_dict(source) for source in knowledge_base.sources]

        return FirestoreKnowledgeBaseData(**data)

    sources = None
    if knowledge_base.sources:
        sources = [_knowledge_base_source_to_dict(source) for source in knowledge_base.sources]

    return FirestoreKnowledgeBaseData(
        user_id=knowledge_base.user_id,
        knowledge_base_id=knowledge_base.knowledge_base_id,
        name=knowledge_base.name,
        type=knowledge_base.type,
        custom_trigger=knowledge_base.custom_trigger,
        status=knowledge_base.status,
        sources=sources,
        last_refreshed_timestamp=knowledge_base.last_refreshed_timestamp,
    )

def to_knowledge_base_domain(data: FirestoreKnowledgeBaseData) -> KnowledgeBase:
    sources = None
    if data.sources:
        sources = [_dict_to_knowledge_base_source(source) for source in data.sources]

    return KnowledgeBase(
        id=data.id,
        user_id=data.user_id,
        knowledge_base_id=data.knowledge_base_id,
        name=data.name,
        type=data.type,
        custom_trigger=data.custom_trigger,
        status=data.status,
        sources=sources,
        last_refreshed_timestamp=data.last_refreshed_timestamp,
    )

def _knowledge_base_source_to_dict(source: KnowledgeBaseSource) -> FirestoreKnowledgeBaseSourceData:
    return FirestoreKnowledgeBaseSourceData(
        type=source.type,
        source_id=source.source_id,
        name=source.name,
        url=source.url,
        text=source.text,
    )

def _dict_to_knowledge_base_source(data: FirestoreKnowledgeBaseSourceData) -> KnowledgeBaseSource:
    return KnowledgeBaseSource(
        type=data.type,
        source_id=data.source_id,
        name=data.name,
        url=data.url,
        text=data.text,
    )
