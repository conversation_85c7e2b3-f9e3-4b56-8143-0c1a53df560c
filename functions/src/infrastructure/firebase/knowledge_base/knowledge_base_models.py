from typing import Optional, List
from src.domain.models import SerializableModel

class FirestoreKnowledgeBaseSourceData(SerializableModel):
    type: Optional[str] = None
    source_id: Optional[str] = None
    name: Optional[str] = None
    url: Optional[str] = None
    text: Optional[str] = None

class FirestoreKnowledgeBaseData(SerializableModel):
    id: Optional[str] = None
    user_id: Optional[str] = None
    knowledge_base_id: Optional[str] = None  # External Retell ID
    name: Optional[str] = None
    type: Optional[str] = None
    custom_trigger: Optional[str] = None
    status: Optional[str] = None
    sources: Optional[List[FirestoreKnowledgeBaseSourceData]] = None
    last_refreshed_timestamp: Optional[int] = None
