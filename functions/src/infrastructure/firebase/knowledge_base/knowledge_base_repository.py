from typing import List
from src.domain.ports import KnowledgeBaseRepository
from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from .knowledge_base_client import FirestoreKnowledgeBaseClient
from .knowledge_base_mappers import to_firestore_knowledge_base_model, to_knowledge_base_domain

class FirestoreKnowledgeBaseRepository(KnowledgeBaseRepository):
    def __init__(self):
        self.client = FirestoreKnowledgeBaseClient()

    def save_knowledge_base(self, knowledge_base_data: KnowledgeBase) -> KnowledgeBase:
        payload = to_firestore_knowledge_base_model(knowledge_base_data)
        knowledge_base_id = self.client.add(payload)
        knowledge_base_data.id = knowledge_base_id
        return knowledge_base_data

    def get_knowledge_base(self, knowledge_base_id: str) -> KnowledgeBase:
        firestore_knowledge_base = self.client.get(knowledge_base_id)
        return to_knowledge_base_domain(firestore_knowledge_base)

    def find_knowledge_bases(self, filters: dict) -> List[KnowledgeBase]:
        firestore_knowledge_bases = self.client.find(filters)
        return [to_knowledge_base_domain(kb) for kb in firestore_knowledge_bases]

    def add_knowledge_base_sources(self, knowledge_base_id: str, sources: List[KnowledgeBaseSource]) -> KnowledgeBase:
        """Add sources to an existing knowledge base in Firestore."""
        # Get current knowledge base
        current_kb = self.get_knowledge_base(knowledge_base_id)

        # Add new sources to existing ones
        if current_kb.sources:
            current_kb.sources.extend(sources)
        else:
            current_kb.sources = sources

        # Update the knowledge base
        payload = to_firestore_knowledge_base_model(current_kb, partial=True)
        self.client.update(knowledge_base_id, payload)

        return current_kb

    def delete_knowledge_base_source(self, knowledge_base_id: str, source_id: str) -> KnowledgeBase:
        """Delete a source from a knowledge base in Firestore."""
        # Get current knowledge base
        current_kb = self.get_knowledge_base(knowledge_base_id)

        # Remove the source with the specified ID
        if current_kb.sources:
            current_kb.sources = [s for s in current_kb.sources if s.source_id != source_id]

        # Update the knowledge base
        payload = to_firestore_knowledge_base_model(current_kb, partial=True)
        self.client.update(knowledge_base_id, payload)

        return current_kb

    def delete_knowledge_base(self, knowledge_base_id: str) -> None:
        self.client.delete(knowledge_base_id)
