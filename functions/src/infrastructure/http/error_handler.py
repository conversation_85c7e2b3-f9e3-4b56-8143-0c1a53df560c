from firebase_functions.https_fn import HttpsError
from src.domain.exceptions import AppError, BadRequestError
from .core import VALID_CODES

def handle_app_error(error: AppError, operation: str) -> HttpsError:
    """
    Convert domain errors to Firebase HttpsError with appropriate codes.
    """
    error_message = f"Error {operation}: {str(error)}"

    if isinstance(error, BadRequestError):
        return HttpsError("invalid-argument", error_message)
    else:
        code = error.code if error.code in VALID_CODES else "internal"
        return HttpsError(code, error_message)

def get_error_message(operation: str, entity: str) -> str:
    """
    Generate standardized error messages.
    """
    return f"Error {operation} {entity}"
