/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_FIREBASE_API_KEY: string
  readonly VITE_FIREBASE_AUTH_DOMAIN: string
  readonly VITE_FIREBASE_PROJECT_ID: string
  readonly VITE_FIREBASE_STORAGE_BUCKET: string
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string
  readonly VITE_FIREBASE_APP_ID: string
  readonly VITE_FIREBASE_MEASUREMENT_ID: string
  // Backend configuration
  readonly VITE_USE_EMULATORS: string;
  // API configuration
  readonly VITE_API_BASE_URL: string
  readonly VITE_FIREBASE_FUNCTIONS_URL: string
  readonly VITE_HUBSPOT_API_BASE_URL: string
  // Microservices configuration
  readonly VITE_AGENT_SERVICE_URL: string
  readonly VITE_COMMUNICATIONS_SERVICE_URL: string
  readonly VITE_CONTACT_SERVICE_URL: string
  readonly VITE_ANALYTICS_SERVICE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
