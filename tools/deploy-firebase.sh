#!/bin/bash
set -e

# Buscar clave JSON del service account
KEY_PATH=$(find ./functions/.keys -type f -name '*.json' | head -n 1)

if [ -z "$KEY_PATH" ]; then
  echo "❌ JSON key not found in functions/.keys/"
  exit 1
fi

echo "✅ Using key from: $KEY_PATH"

# Extraer el project_id desde la clave JSON
PROJECT_ID=$(jq -r '.project_id' "$KEY_PATH")
if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" == "null" ]; then
  echo "❌ Could not read project_id from key file"
  exit 1
fi
echo "✅ Project ID: $PROJECT_ID"

# Activar la cuenta de servicio
gcloud auth activate-service-account --key-file="$KEY_PATH"
gcloud config set project "$PROJECT_ID"

# Generar token de acceso para Firebase CLI
ACCESS_TOKEN=$(gcloud auth print-access-token)

# Desplegar Functions + reglas Firestore + reglas Storage usando el token
npx dotenv -e functions/.env -- \
npx cross-env GOOGLE_APPLICATION_CREDENTIALS="$KEY_PATH" \
firebase deploy --only functions,firestore:rules,storage --non-interactive --token "$ACCESS_TOKEN" --force
