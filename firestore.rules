rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    function signedInOrPublic() {
      return request.auth.uid != null || resource.data.visibility == 'public';
    }

    match /profile/{userId} {
      allow read, update, delete: if signedInOrPublic() && request.auth.uid == userId;
      allow create:               if signedInOrPublic();
    }
    match /assistants/{assistantId} {
      allow read:           if signedInOrPublic() && resource.data.userId == request.auth.uid;
      allow update, delete: if signedInOrPublic() && request.resource.data.userId == request.auth.uid;
      allow create:         if signedInOrPublic();
    }
    match /users/{userId} {
      allow read, write: if request.auth.uid == userId;

      match /integrations/{integrationId} {
        allow read, write: if request.auth.uid == userId;
      }
      match /contacts/{contactId} {
        allow read: if request.auth.uid == userId;
      }
    }
    match /active_assistants/{activeAssistantId} {
      allow read:           if signedInOrPublic();
    }
    match /conversations/{conversationId} {
      allow read:           if signedInOrPublic();
    }
  }
}
