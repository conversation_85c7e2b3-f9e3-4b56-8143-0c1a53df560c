{"name": "synapy-dashboard", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 4000", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "emulators": "./tools/start-emulators.sh", "deploy-firebase": "./tools/deploy-firebase.sh"}, "dependencies": {"@iconify/vue": "4.1.1", "@tiptap/pm": "2.2.2", "@tiptap/starter-kit": "2.2.2", "@tiptap/vue-3": "2.2.2", "apexcharts": "3.45.2", "date-fns": "^2.29.3", "firebase": "^11.6.1", "lodash": "^4.17.21", "maska": "^1.5.0", "pinia": "^3.0.2", "vee-validate": "^4.15.0", "vite-plugin-vuetify": "2.0.1", "vue": "3.4.26", "vue-router": "4.2.5", "vue-tabler-icons": "2.21.0", "vue3-apexcharts": "1.5.2", "vuetify": "3.5.18", "yup": "^1.3.3"}, "devDependencies": {"@mdi/font": "7.4.47", "@rushstack/eslint-patch": "1.7.2", "@types/chance": "^1.1.6", "@types/node": "20.11.17", "@vitejs/plugin-vue": "5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.1.3", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "esbuild": "^0.20.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.21.1", "firebase-tools": "^14.11.2", "prettier": "3.2.5", "sass": "1.70.0", "sass-loader": "14.1.0", "typescript": "^5.8.3", "vite": "^7.0.6", "vue-cli-plugin-vuetify": "2.5.8", "vue-tsc": "^2.2.10", "vuetify-loader": "1.9.2"}}