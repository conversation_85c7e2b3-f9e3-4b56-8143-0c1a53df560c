from datetime import datetime
import json
import asyncio
import math
import re
import copy
import hashlib
import os
import traceback
import io
import wave
import numpy as np
import aiofiles
import torch
import torchaudio
from scipy.io import wavfile
from dotenv import load_dotenv
from pydantic import create_model
from .logger_config import configure_logger
from bolna.constants import PREPROCESS_DIR, PRE_FUNCTION_CALL_MESSAGE, DEFAULT_LANGUAGE_CODE, TRANSFERING_CALL_FILLER
from pydub import AudioSegment

logger = configure_logger(__name__)
load_dotenv()

FORMAT_PATTERN = re.compile(r"%\(([^)]+)\)([sdifouxXeEgGcr])")


class DictWithMissing(dict):
    def __missing__(self, key):
        return ''


def load_file(file_path, is_json=False):
    data = None
    with open(file_path, "r") as f:
        if is_json:
            data = json.load(f)
        else:
            data = f.read()

    return data


def write_json_file(file_path, data):
    with open(file_path, 'w') as file:
        json.dump(data, file, indent=4, ensure_ascii=False)


def create_ws_data_packet(data, meta_info=None, is_md5_hash=False, llm_generated=False):
    metadata = copy.deepcopy(meta_info)
    if meta_info is not None: #It'll be none in case we connect through dashboard playground
        metadata["is_md5_hash"] = is_md5_hash
        metadata["llm_generated"] = llm_generated
    return {
        'data': data,
        'meta_info': metadata
    }


def int2float(sound):
    abs_max = np.abs(sound).max()
    sound = sound.astype('float32')
    if abs_max > 0:
        sound *= 1 / 32768
    sound = sound.squeeze()  # depends on the use case
    return sound


def float2int(sound):
    sound = np.int16(sound * 32767)
    return sound


def mu_law_encode(audio, quantization_channels=256):
    mu = quantization_channels - 1
    safe_audio_abs = np.minimum(np.abs(audio), 1.0)
    magnitude = np.log1p(mu * safe_audio_abs) / np.log1p(mu)
    signal = np.sign(audio) * magnitude
    return ((signal + 1) / 2 * mu + 0.5).astype(np.int32)


def float32_to_int16(float_audio):
    float_audio = np.clip(float_audio, -1.0, 1.0)
    int16_audio = (float_audio * 32767).astype(np.int16)
    return int16_audio

def wav_bytes_to_pcm(wav_bytes):
    wav_buffer = io.BytesIO(wav_bytes)
    rate, data = wavfile.read(wav_buffer)
    if data.dtype == np.int16:
        return data.tobytes()
    if data.dtype == np.float32:
        data = float32_to_int16(data)
        return data.tobytes()


# def wav_bytes_to_pcm(wav_bytes):
#     wav_buffer = io.BytesIO(wav_bytes)
#     with wave.open(wav_buffer, 'rb') as wav_file:
#         pcm_data = wav_file.readframes(wav_file.getnframes())
#     return pcm_data

# def wav_bytes_to_pcm(wav_bytes):
#     wav_buffer = io.BytesIO(wav_bytes)
#     audio = AudioSegment.from_file(wav_buffer, format="wav")
#     pcm_data = audio.raw_data
#     return pcm_data



def raw_to_mulaw(raw_bytes):
    # Convert bytes to numpy array of int16 values
    samples = np.frombuffer(raw_bytes, dtype=np.int16)
    samples = samples.astype(np.float32) / (2 ** 15)
    mulaw_encoded = mu_law_encode(samples)
    return mulaw_encoded


def get_md5_hash(text):
    return hashlib.md5(text.encode()).hexdigest()


def is_valid_md5(hash_string):
    return bool(re.fullmatch(r"[0-9a-f]{32}", hash_string))


def split_payload(payload, max_size=500 * 1024):
    if len(payload) <= max_size:
        return payload
    return [payload[i:i + max_size] for i in range(0, len(payload), max_size)]


def get_required_input_types(task):
    input_types = dict()
    for i, chain in enumerate(task['toolchain']['pipelines']):
        first_model = chain[0]
        if chain[0] == "transcriber":
            input_types["audio"] = i
        elif chain[0] == "synthesizer" or chain[0] == "llm":
            input_types["text"] = i
    return input_types


def format_messages(messages, use_system_prompt=False, include_tools=False):
    formatted_string = ""
    for message in messages:
        role = message['role']
        if message['content'] is None:
            logger.info(f"Continuing the loop as content received is None")
            continue
        content = message['content']

        if use_system_prompt and role == 'system':
            try:
                formatted_string += "system: " + content + "\n"
            except Exception as e:
                a = 1
        if role == 'assistant':
            formatted_string += "assistant: " + content + "\n"
        elif role == 'user':
            formatted_string += "user: " + content + "\n"
        elif include_tools and role == 'tool':
            formatted_string += "tool_response: " + content + "\n"

    return formatted_string


def update_prompt_with_context(prompt, context_data):
    try:
        if not context_data or not isinstance(context_data.get('recipient_data'), dict):
            return prompt.format_map(DictWithMissing({}))
        return prompt.format_map(DictWithMissing(context_data.get('recipient_data', {})))
    except Exception as e:
        return prompt


async def execute_tasks_in_chunks(tasks, chunk_size=10):
    task_chunks = [tasks[i:i + chunk_size] for i in range(0, len(tasks), chunk_size)]

    for chunk in task_chunks:
        await asyncio.gather(*chunk)


def has_placeholders(s):
    return bool(re.search(r'\{[^{}\s]*\}', s))


def infer_type(value):
    if isinstance(value, int):
        return (int, ...)
    elif isinstance(value, float):
        return (float, ...)
    elif isinstance(value, bool):
        return (bool, ...)
    elif isinstance(value, list):
        return (list, ...)
    elif isinstance(value, dict):
        return (dict, ...)
    else:
        return (str, ...)


def json_to_pydantic_schema(json_data):
    parsed_json = json.loads(json_data)

    fields = {key: infer_type(value) for key, value in parsed_json.items()}
    dynamic_model = create_model('DynamicModel', **fields)

    return dynamic_model.schema_json(indent=2)


def clean_json_string(json_str):
    if type(json_str) is not str:
        return json_str
    if json_str.startswith("```json") and json_str.endswith("```"):
        json_str = json_str[7:-3].strip()
    json_str = json_str.replace("###JSON Structure\n", "")
    return json_str


def yield_chunks_from_memory(audio_bytes, chunk_size=512):
    total_length = len(audio_bytes)
    for i in range(0, total_length, chunk_size):
        yield audio_bytes[i:i + chunk_size]


def pcm_to_wav_bytes(pcm_data, sample_rate=16000, num_channels=1, sample_width=2):
    buffer = io.BytesIO()
    bit_depth = 16
    if len(pcm_data)%2 == 1:
        pcm_data += b'\x00'
    tensor_pcm = torch.frombuffer(pcm_data, dtype=torch.int16)
    tensor_pcm = tensor_pcm.float() / (2**(bit_depth - 1))
    tensor_pcm = tensor_pcm.unsqueeze(0)
    torchaudio.save(buffer, tensor_pcm, sample_rate, format='wav')
    return buffer.getvalue()


def convert_audio_to_wav(audio_bytes, source_format = 'flac'):
    logger.info(f"CONVERTING AUDIO TO WAV {source_format}")
    audio = AudioSegment.from_file(io.BytesIO(audio_bytes), format=source_format)
    logger.info(f"GOT audio wav {audio}")
    buffer = io.BytesIO()
    audio.export(buffer, format="wav")
    logger.info(f"SENDING BACK WAV")
    return buffer.getvalue()


def resample(audio_bytes, target_sample_rate, format = "mp3"):
    audio_buffer = io.BytesIO(audio_bytes)
    waveform, orig_sample_rate = torchaudio.load(audio_buffer, format = format)
    if orig_sample_rate == target_sample_rate:
        return audio_bytes
    resampler = torchaudio.transforms.Resample(orig_sample_rate, target_sample_rate)
    audio_waveform = resampler(waveform)
    audio_buffer = io.BytesIO()
    logger.info(f"Resampling from {orig_sample_rate} to {target_sample_rate}")
    torchaudio.save(audio_buffer, audio_waveform, target_sample_rate, format="wav")
    return audio_buffer.getvalue()


def merge_wav_bytes(wav_files_bytes):
    combined = AudioSegment.empty()
    for wav_bytes in wav_files_bytes:
        file_like_object = io.BytesIO(wav_bytes)

        audio_segment = AudioSegment.from_file(file_like_object, format="wav")
        combined += audio_segment

    buffer = io.BytesIO()
    combined.export(buffer, format="wav")
    return buffer.getvalue()


def calculate_audio_duration(size_bytes, sampling_rate, bit_depth = 16, channels = 1, format = "wav"):
    bytes_per_sample = (bit_depth / 8) * channels if format != 'mulaw' else 1
    total_samples = size_bytes / bytes_per_sample
    duration_seconds = total_samples / sampling_rate
    return duration_seconds


def create_empty_wav_file(duration_seconds, sampling_rate = 24000):
    total_frames = duration_seconds * sampling_rate
    wav_io = io.BytesIO()
    with wave.open(wav_io, 'wb') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(sampling_rate)
        wav_file.setnframes(total_frames)
        wav_file.writeframes(b'\x00' * total_frames * 2)
    wav_io.seek(0)
    return wav_io



'''
Message type
1. Component
2. Request/Response
3. conversation_leg_id
4. data
5. num_input_tokens
6. num_output_tokens
7. num_characters
8. is_final
9. engine
'''


async def write_request_logs(message, run_id):
    component_details = [None, None, None, None, None]
    message_data = message.get('data', '')
    if message_data is None:
        message_data = ''

    row = [message['time'], message["component"], message["direction"], message["leg_id"], message['sequence_id'], message['model']]
    if message["component"] in ("llm", "llm_hangup"):
        component_details = [message_data, message.get('input_tokens', 0), message.get('output_tokens', 0), None, message.get('latency', None), message['cached'], None]
    elif message["component"] == "transcriber":
        component_details = [message_data, None, None, None, message.get('latency', None), False, message.get('is_final', False)]
    elif message["component"] == "synthesizer":
        component_details = [message_data, None, None, len(message_data), message.get('latency', None), message['cached'], None, message['engine']]
    elif message["component"] == "function_call":
        component_details = [message_data, None, None, None, message.get('latency', None), None, None, None]

    row = row + component_details

    header = "Time,Component,Direction,Leg ID,Sequence ID,Model,Data,Input Tokens,Output Tokens,Characters,Latency,Cached,Final Transcript,Engine\n"
    log_string = ','.join(['"' + str(item).replace('"', '""') + '"' if item is not None else '' for item in row]) + '\n'
    log_dir = f"./logs"
    os.makedirs(log_dir, exist_ok=True)
    log_file_path = f"{log_dir}/{run_id}.csv"
    file_exists = os.path.exists(log_file_path)

    async with aiofiles.open(log_file_path, mode='a') as log_file:
        if not file_exists:
            await log_file.write(header+log_string)
        else:
            await log_file.write(log_string)


def list_number_of_wav_files_in_directory(directory):
    count = 0
    for filename in os.listdir(directory):
        if filename.endswith(".mp3") or filename.endswith(".wav") or filename.endswith(".ogg"):
            count += 1
    return count


def get_file_names_in_directory(directory):
    return os.listdir(directory)


def convert_to_request_log(message, meta_info, model, component="transcriber", direction='response', is_cached=False, engine=None, run_id=None):
    log = dict()
    log['direction'] = direction
    log['data'] = message
    log['leg_id'] = meta_info['request_id'] if "request_id" in meta_info else "-"
    log['time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log['component'] = component
    log['sequence_id'] = meta_info.get('sequence_id', None)
    log['model'] = model
    log['cached'] = is_cached
    if component == "llm":
        log['latency'] = meta_info.get('llm_latency', None) if direction == "response" else None
    if component == "synthesizer":
        log['latency'] = meta_info.get('synthesizer_latency', None) if direction == "response" else None
    if component == "transcriber":
        log['latency'] = meta_info.get('transcriber_latency', None) if direction == "response" else None
        if 'is_final' in meta_info and meta_info['is_final']:
            log['is_final'] = True
    if component == "function_call":
        log['latency'] = None
    if component == "llm-hangup":
        log['latency'] = meta_info.get('llm_latency', None) if direction == "response" else None
    else:
        log['is_final'] = False #This is logged only for users to know final transcript from the transcriber
    log['engine'] = engine
    asyncio.create_task(write_request_logs(log, run_id))


def get_route_info(message, route_layer):
    route = route_layer(message)
    logger.info(f"route gotten {route}")
    return route.name


async def run_in_seperate_thread(fun):
    resp = await asyncio.to_thread(fun)
    return resp


async def process_task_cancellation(asyncio_task, task_name):
    if asyncio_task is not None:
        try:
            asyncio_task.cancel()
            await asyncio_task
        except asyncio.CancelledError:
            logger.info(f"{task_name} has been successfully cancelled.")
        except Exception as e:
            logger.error(f"Error cancelling {task_name}: {e}")


def get_date_time_from_timezone(timezone):
    dt = datetime.now(timezone).strftime("%A, %B %d, %Y")
    ts = datetime.now(timezone).strftime("%I:%M:%S %p")

    return dt, ts


def safe_format_with_context(template_dict, context):
    result = {}
    for key, value in template_dict.items():
        if isinstance(value, str):
            def replacer(match):
                var_name, fmt_type = match.group(1), match.group(2)
                if var_name in context:
                    try:
                        return ("%%(%s)%s" % (var_name, fmt_type)) % context
                    except (ValueError, TypeError):
                        return match.group(0)  # leave untouched on format failure
                else:
                    return match.group(0)  # leave as-is if missing
            result[key] = FORMAT_PATTERN.sub(replacer, value)
        else:
            result[key] = value  # pass non-strings as-is
    return result


def compute_function_pre_call_message(language, function_name, api_tool_pre_call_message):
    default_filler = PRE_FUNCTION_CALL_MESSAGE.get(language, PRE_FUNCTION_CALL_MESSAGE.get(DEFAULT_LANGUAGE_CODE))
    if function_name and function_name.startswith("transfer_call"):
        default_filler = TRANSFERING_CALL_FILLER.get(language, TRANSFERING_CALL_FILLER.get(DEFAULT_LANGUAGE_CODE))

    filler = api_tool_pre_call_message if api_tool_pre_call_message else default_filler
    return filler
