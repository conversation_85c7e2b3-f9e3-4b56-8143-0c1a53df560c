"""
Storage abstraction layer for Bolna server.
Provides a unified interface for different storage backends (S3, Google Cloud Storage, Local).
"""

import os
import json
import asyncio
from abc import ABC, abstractmethod
from typing import Optional, Union, Any
from .logger_config import configure_logger

logger = configure_logger(__name__)


class StorageInterface(ABC):
    """Abstract base class for storage backends."""

    @abstractmethod
    async def get_file(self, bucket_name: str, file_key: str) -> Optional[bytes]:
        """Retrieve a file from storage."""
        pass

    @abstractmethod
    async def store_file(self, bucket_name: str, file_key: str, file_data: Union[bytes, str, Any],
                        content_type: str = "json") -> Optional[str]:
        """Store a file to storage and return its path."""
        pass

    @abstractmethod
    async def delete_file(self, bucket_name: str, file_key: str) -> bool:
        """Delete a file from storage."""
        pass

    @abstractmethod
    async def delete_files_by_prefix(self, bucket_name: str, prefix: str) -> bool:
        """Delete files by prefix from storage."""
        pass


class LocalStorageBackend(StorageInterface):
    """Local file system storage backend."""

    def __init__(self, base_dir: str = None):
        from bolna.constants import PREPROCESS_DIR
        self.base_dir = base_dir or PREPROCESS_DIR

    async def get_file(self, bucket_name: str, file_key: str) -> Optional[bytes]:
        """Retrieve a file from local storage."""
        try:
            file_path = os.path.join(self.base_dir, file_key)
            if os.path.isfile(file_path):
                with open(file_path, 'rb') as file:
                    return file.read()
            return None
        except Exception as e:
            logger.error(f"Error reading local file {file_key}: {e}")
            return None

    async def store_file(self, bucket_name: str, file_key: str, file_data: Union[bytes, str, Any],
                        content_type: str = "json") -> Optional[str]:
        """Store a file to local storage and return its path."""
        try:
            file_path = os.path.join(self.base_dir, file_key)
            directory_path = os.path.dirname(file_path)
            os.makedirs(directory_path, exist_ok=True)

            logger.info(f"Writing to {file_path}")

            if content_type == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    data = json.dumps(file_data) if not isinstance(file_data, str) else file_data
                    f.write(data)
            elif content_type in ['csv', 'txt']:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(str(file_data))
            else:
                with open(file_path, 'wb') as f:
                    if hasattr(file_data, 'read'):
                        f.write(file_data.read())
                    else:
                        f.write(file_data)
            return os.path.abspath(file_path)
        except Exception as e:
            logger.error(f"Could not save local file {file_key}: {e}")
            return None

    async def delete_file(self, bucket_name: str, file_key: str) -> bool:
        """Delete a file from local storage."""
        try:
            file_path = os.path.join(self.base_dir, file_key)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting local file {file_key}: {e}")
            return False

    async def delete_files_by_prefix(self, bucket_name: str, prefix: str) -> bool:
        """Delete files by prefix from local storage."""
        try:
            prefix_path = os.path.join(self.base_dir, prefix)
            directory = os.path.dirname(prefix_path)
            filename_prefix = os.path.basename(prefix_path)

            if os.path.exists(directory):
                for filename in os.listdir(directory):
                    if filename.startswith(filename_prefix):
                        file_path = os.path.join(directory, filename)
                        os.remove(file_path)
            return True
        except Exception as e:
            logger.error(f"Error deleting files by prefix {prefix}: {e}")
            return False


class S3StorageBackend(StorageInterface):
    """Amazon S3 storage backend."""

    def __init__(self):
        from botocore.exceptions import BotoCoreError, ClientError
        from aiobotocore.session import AioSession
        from contextlib import AsyncExitStack

        self.BotoCoreError = BotoCoreError
        self.ClientError = ClientError
        self.AioSession = AioSession
        self.AsyncExitStack = AsyncExitStack

    async def get_file(self, bucket_name: str, file_key: str) -> Optional[bytes]:
        """Retrieve a file from S3."""
        session = self.AioSession()

        async with self.AsyncExitStack() as exit_stack:
            s3_client = await exit_stack.enter_async_context(session.create_client('s3'))
            try:
                response = await s3_client.get_object(Bucket=bucket_name, Key=file_key)
                file_content = await response['Body'].read()
                return file_content
            except (self.BotoCoreError, self.ClientError) as error:
                logger.error(f"S3 get_file error: {error}")
                return None

    async def store_file(self, bucket_name: str, file_key: str, file_data: Union[bytes, str, Any],
                        content_type: str = "json") -> Optional[str]:
        """Store a file to S3 and return its path."""
        session = self.AioSession()

        async with self.AsyncExitStack() as exit_stack:
            s3_client = await exit_stack.enter_async_context(session.create_client('s3'))
            try:
                if content_type == "json":
                    data = json.dumps(file_data) if not isinstance(file_data, str) else file_data
                else:
                    data = file_data

                await s3_client.put_object(Bucket=bucket_name, Key=file_key, Body=data)
                return f"https://{bucket_name}.s3.amazonaws.com/{file_key}"
            except (self.BotoCoreError, self.ClientError) as error:
                logger.error(f"S3 store_file error: {error}")
                return None
            except Exception as e:
                logger.error(f'Unexpected error while S3 put_object: {e}')
                return None

    async def delete_file(self, bucket_name: str, file_key: str) -> bool:
        """Delete a file from S3."""
        session = self.AioSession()

        async with self.AsyncExitStack() as exit_stack:
            s3_client = await exit_stack.enter_async_context(session.create_client('s3'))
            try:
                await s3_client.delete_object(Bucket=bucket_name, Key=file_key)
                return True
            except (self.BotoCoreError, self.ClientError) as error:
                logger.error(f"S3 delete_file error: {error}")
                return False

    async def delete_files_by_prefix(self, bucket_name: str, prefix: str) -> bool:
        """Delete files by prefix from S3."""
        session = self.AioSession()

        async with self.AsyncExitStack() as exit_stack:
            s3_client = await exit_stack.enter_async_context(session.create_client('s3'))
            try:
                # List objects with the prefix
                response = await s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)

                if 'Contents' in response:
                    # Delete objects
                    objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]
                    await s3_client.delete_objects(
                        Bucket=bucket_name,
                        Delete={'Objects': objects_to_delete}
                    )
                return True
            except (self.BotoCoreError, self.ClientError) as error:
                logger.error(f"S3 delete_files_by_prefix error: {error}")
                return False


class GoogleCloudStorageBackend(StorageInterface):
    """Google Cloud Storage backend."""

    def __init__(self):
        try:
            from google.cloud import storage
            from google.cloud.exceptions import NotFound, GoogleCloudError
            self.storage = storage
            self.NotFound = NotFound
            self.GoogleCloudError = GoogleCloudError
            self.client = None
        except ImportError:
            logger.error("Google Cloud Storage library not installed. Install with: pip install google-cloud-storage")
            raise

    def _get_client(self):
        """Get or create Google Cloud Storage client."""
        if self.client is None:
            self.client = self.storage.Client()
        return self.client

    async def get_file(self, bucket_name: str, file_key: str) -> Optional[bytes]:
        """Retrieve a file from Google Cloud Storage."""
        try:
            client = self._get_client()
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(file_key)

            # Run the synchronous download in a thread pool
            loop = asyncio.get_event_loop()
            file_content = await loop.run_in_executor(None, blob.download_as_bytes)
            return file_content
        except self.NotFound:
            logger.warning(f"File not found in GCS: {file_key}")
            return None
        except self.GoogleCloudError as e:
            logger.error(f"GCS get_file error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in GCS get_file: {e}")
            return None

    async def store_file(self, bucket_name: str, file_key: str, file_data: Union[bytes, str, Any],
                        content_type: str = "json") -> Optional[str]:
        """Store a file to Google Cloud Storage and return its path."""
        try:
            client = self._get_client()
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(file_key)

            # Prepare data based on content type
            if content_type == "json":
                data = json.dumps(file_data) if not isinstance(file_data, str) else file_data
                data = data.encode('utf-8') if isinstance(data, str) else data
            else:
                if hasattr(file_data, 'read'):
                    data = file_data.read()
                elif isinstance(file_data, str):
                    data = file_data.encode('utf-8')
                else:
                    data = file_data

            # Determine the correct content type for the upload
            if content_type == "json":
                upload_content_type = "application/json"
            elif content_type == "wav":
                upload_content_type = "audio/wav"
            elif content_type == "mp3":
                upload_content_type = "audio/mpeg"
            else:
                upload_content_type = "application/octet-stream"

            # Run the synchronous upload in a thread pool with explicit content type
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: blob.upload_from_string(data, content_type=upload_content_type)
            )
            return f"https://storage.googleapis.com/{bucket_name}/{file_key}"
        except self.GoogleCloudError as e:
            logger.error(f"GCS store_file error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in GCS store_file: {e}")
            return None

    async def delete_file(self, bucket_name: str, file_key: str) -> bool:
        """Delete a file from Google Cloud Storage."""
        try:
            client = self._get_client()
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(file_key)

            # Run the synchronous delete in a thread pool
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, blob.delete)
            return True
        except self.NotFound:
            logger.warning(f"File not found for deletion in GCS: {file_key}")
            return True  # Consider it successful if file doesn't exist
        except self.GoogleCloudError as e:
            logger.error(f"GCS delete_file error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in GCS delete_file: {e}")
            return False

    async def delete_files_by_prefix(self, bucket_name: str, prefix: str) -> bool:
        """Delete files by prefix from Google Cloud Storage."""
        try:
            client = self._get_client()
            bucket = client.bucket(bucket_name)

            # Run the synchronous list and delete operations in a thread pool
            loop = asyncio.get_event_loop()

            def delete_blobs_with_prefix():
                blobs = bucket.list_blobs(prefix=prefix)
                for blob in blobs:
                    blob.delete()

            await loop.run_in_executor(None, delete_blobs_with_prefix)
            return True
        except self.GoogleCloudError as e:
            logger.error(f"GCS delete_files_by_prefix error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in GCS delete_files_by_prefix: {e}")
            return False


class StorageFactory:
    """Factory class for creating storage backend instances."""

    _instance = None
    _storage_backend = None

    @classmethod
    def get_storage_backend(cls) -> StorageInterface:
        """Get the configured storage backend instance (singleton)."""
        if cls._storage_backend is None:
            cls._storage_backend = cls._create_storage_backend()
        return cls._storage_backend

    @classmethod
    def _create_storage_backend(cls) -> StorageInterface:
        """Create storage backend based on configuration."""
        # Check environment variables for storage configuration
        storage_type = os.getenv('STORAGE_BACKEND', 'auto').lower()

        if storage_type == 'local':
            logger.info("Using Local storage backend")
            return LocalStorageBackend()
        elif storage_type == 's3':
            logger.info("Using S3 storage backend")
            return S3StorageBackend()
        elif storage_type == 'gcs' or storage_type == 'google':
            logger.info("Using Google Cloud Storage backend")
            return GoogleCloudStorageBackend()
        elif storage_type == 'auto':
            # Auto-detect based on environment
            return cls._auto_detect_storage_backend()
        else:
            logger.warning(f"Unknown storage backend '{storage_type}', falling back to auto-detection")
            return cls._auto_detect_storage_backend()

    @classmethod
    def _auto_detect_storage_backend(cls) -> StorageInterface:
        """Auto-detect the appropriate storage backend based on environment."""
        # Check if running in Google Cloud environment
        if os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('K_SERVICE'):
            try:
                logger.info("Google Cloud environment detected, using Google Cloud Storage")
                return GoogleCloudStorageBackend()
            except ImportError:
                logger.warning("Google Cloud Storage not available, falling back to S3")
                return S3StorageBackend()

        # Check if AWS credentials are available
        if os.getenv('AWS_ACCESS_KEY_ID') or os.getenv('AWS_PROFILE'):
            logger.info("AWS environment detected, using S3 storage")
            return S3StorageBackend()

        # Default to local storage
        logger.info("No cloud environment detected, using local storage")
        return LocalStorageBackend()

    @classmethod
    def reset(cls):
        """Reset the singleton instance (useful for testing)."""
        cls._storage_backend = None
