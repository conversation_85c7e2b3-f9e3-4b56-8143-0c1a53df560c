"""
Storage utility functions that maintain backward compatibility with existing Bolna code.
These functions provide the same API as the original functions in utils.py but use the
storage abstraction layer underneath.
"""

import os
import json
import asyncio
from typing import Optional, Union, Any
from dotenv import load_dotenv
from .storage_interface import StorageFactory
from .logger_config import configure_logger

logger = configure_logger(__name__)
load_dotenv()

# Environment variables for bucket names
BUCKET_NAME_PROMPTS = os.getenv('BUCKET_NAME_PROMPTS')
BUCKET_NAME_RECORDINGS = os.getenv('BUCKET_NAME_RECORDINGS')


async def store_file(bucket_name: str = None, file_key: str = None, file_data: Union[bytes, str, Any] = None,
                    content_type: str = "json", local: bool = False, preprocess_dir: str = None) -> bool:
    """
    Store a file to storage (maintains original API compatibility).

    Args:
        bucket_name: Name of the bucket/container
        file_key: Path/key where to store the file
        file_data: Data to store
        content_type: Type of content (json, wav, mp3, etc.)
        local: If True, force local storage (for backward compatibility)
        preprocess_dir: Directory for local storage (for backward compatibility)

    Returns:
        True if successful, False otherwise
    """
    try:
        if local:
            # Force local storage for backward compatibility
            from .storage_interface import LocalStorageBackend
            storage = LocalStorageBackend(base_dir=preprocess_dir)
        else:
            storage = StorageFactory.get_storage_backend()

        return await storage.store_file(bucket_name, file_key, file_data, content_type) is not None
    except Exception as e:
        logger.error(f"Error storing file {file_key} to {bucket_name}: {e}")
        return False


async def get_raw_audio_bytes(filename: str, agent_name: str = None, audio_format: str = 'mp3',
                             assistant_id: str = None, local: bool = False, is_location: bool = False) -> Optional[bytes]:
    """
    Get raw audio bytes from storage (maintains original API compatibility).

    Args:
        filename: Name of the audio file
        agent_name: Name of the agent (for local storage path)
        audio_format: Format of the audio file
        assistant_id: ID of the assistant (for cloud storage path)
        local: If True, use local storage
        is_location: If True, filename is treated as full path

    Returns:
        Audio data as bytes, or None if not found
    """
    try:
        if local:
            # Local storage logic
            from bolna.constants import PREPROCESS_DIR
            from .storage_interface import LocalStorageBackend

            if not is_location:
                file_key = f"{agent_name}/{audio_format}/{filename}.{audio_format}"
            else:
                # Remove base directory from absolute path to get relative key
                file_key = filename.replace(PREPROCESS_DIR + "/", "") if filename.startswith(PREPROCESS_DIR) else filename

            storage = LocalStorageBackend()
            return await storage.get_file("", file_key)  # bucket_name not used for local storage
        else:
            # Cloud storage logic
            if not is_location:
                file_key = f"{assistant_id}/audio/{filename}.{audio_format}"
            else:
                file_key = filename

            logger.info(f"Reading {file_key}")
            storage = StorageFactory.get_storage_backend()
            return await storage.get_file(BUCKET_NAME_PROMPTS, file_key)
    except Exception as e:
        logger.error(f"Error getting raw audio bytes for {filename}: {e}")
        return None


async def get_prompt_responses(assistant_id: str, local: bool = False) -> Optional[dict]:
    """
    Get prompt responses from storage (maintains original API compatibility).

    Args:
        assistant_id: ID of the assistant
        local: If True, use local storage

    Returns:
        Conversation details as dict, or None if not found
    """
    try:
        if local:
            # Local storage logic
            from bolna.constants import PREPROCESS_DIR
            from .storage_interface import LocalStorageBackend

            file_key = f"{assistant_id}/conversation_details.json"
            storage = LocalStorageBackend()
            file_content = await storage.get_file("", file_key)

            if file_content:
                return json.loads(file_content.decode('utf-8'))
            return None
        else:
            # Cloud storage logic
            file_key = f"{assistant_id}/conversation_details.json"
            logger.info(f"Loading conversation details from storage: {BUCKET_NAME_PROMPTS}/{file_key}")

            storage = StorageFactory.get_storage_backend()
            file_content = await storage.get_file(BUCKET_NAME_PROMPTS, file_key)

            if file_content:
                return json.loads(file_content.decode('utf-8'))
            return None
    except Exception as e:
        logger.error(f"Error getting prompt responses for {assistant_id}: {e}")
        return None


async def save_audio_file_to_s3(conversation_recording: dict, sampling_rate: int = 24000,
                               assistant_id: str = None, run_id: str = None) -> str:
    """
    Save audio file to storage (maintains S3 API compatibility).
    This function processes conversation recordings and saves them as stereo WAV files.

    Args:
        conversation_recording: Dictionary containing input/output audio data
        sampling_rate: Audio sampling rate
        assistant_id: ID of the assistant
        run_id: ID of the conversation run

    Returns:
        URL/path to the saved audio file
    """
    try:
        # Import required libraries for audio processing
        import io
        import torch
        import torchaudio
        from pydub import AudioSegment

        # Process output audio frames
        last_frame_end_time = conversation_recording['output'][0]['start_time']
        logger.info(f"LENGTH OF OUTPUT AUDIO {len(conversation_recording['output'])}")
        initial_gap = (last_frame_end_time - conversation_recording["metadata"]["started"]) * 1000
        logger.info(f"Initial gap {initial_gap}")
        combined_audio = AudioSegment.silent(duration=initial_gap, frame_rate=sampling_rate)

        for i, frame in enumerate(conversation_recording['output']):
            frame_start_time = frame['start_time']
            logger.info(f"Processing frame {i}, frame start time = {last_frame_end_time}, frame start time= {frame_start_time}")
            if last_frame_end_time < frame_start_time:
                gap_duration_samples = frame_start_time - last_frame_end_time
                silence = AudioSegment.silent(duration=gap_duration_samples * 1000, frame_rate=sampling_rate)
                combined_audio += silence
            last_frame_end_time = frame_start_time + frame['duration']
            frame_as = AudioSegment.from_file(io.BytesIO(frame['data']), format="wav")
            combined_audio += frame_as

        # Process input audio
        webm_segment = AudioSegment.from_file(io.BytesIO(conversation_recording['input']["data"]))
        wav_bytes = io.BytesIO()
        webm_segment.export(wav_bytes, format="wav")
        wav_bytes.seek(0)
        waveform, sample_rate = torchaudio.load(wav_bytes)
        resampler = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=sampling_rate)
        downsampled_waveform = resampler(waveform)

        # Convert combined audio to tensor
        audio_segment_bytes = io.BytesIO()
        combined_audio.export(audio_segment_bytes, format="wav")
        audio_segment_bytes.seek(0)
        waveform_audio_segment, sample_rate = torchaudio.load(audio_segment_bytes)

        if waveform_audio_segment.shape[0] > 1:
            waveform_audio_segment = waveform_audio_segment[:1, :]

        # Adjust shapes to be [1, N] if not already
        downsampled_waveform = downsampled_waveform.unsqueeze(0) if downsampled_waveform.dim() == 1 else downsampled_waveform
        waveform_audio_segment = waveform_audio_segment.unsqueeze(0) if waveform_audio_segment.dim() == 1 else waveform_audio_segment

        # Ensure both waveforms have the same length
        max_length = max(downsampled_waveform.size(1), waveform_audio_segment.size(1))
        downsampled_waveform_padded = torch.nn.functional.pad(downsampled_waveform, (0, max_length - downsampled_waveform.size(1)))
        waveform_audio_segment_padded = torch.nn.functional.pad(waveform_audio_segment, (0, max_length - waveform_audio_segment.size(1)))
        stereo_waveform = torch.cat((downsampled_waveform_padded, waveform_audio_segment_padded), 0)

        # Verify the stereo waveform shape is [2, M]
        assert stereo_waveform.shape[0] == 2, "Stereo waveform should have 2 channels."

        # Prepare file key and audio buffer
        file_key = f'{assistant_id + run_id}.wav'
        audio_buffer = io.BytesIO()
        torchaudio.save(audio_buffer, stereo_waveform, 24000, format="wav")
        audio_buffer.seek(0)

        # Store the file using the storage abstraction
        logger.info(f"Storing audio file: {file_key} in {BUCKET_NAME_RECORDINGS}")
        storage = StorageFactory.get_storage_backend()
        file_path = await storage.store_file(BUCKET_NAME_RECORDINGS, file_key, audio_buffer, "wav")

        if file_path is not None:
            return f'{file_path}'
        else:
            logger.error(f"Failed to store audio file {file_key}")
            return ""

    except Exception as e:
        logger.error(f"Error saving audio file to storage: {e}")
        return ""
