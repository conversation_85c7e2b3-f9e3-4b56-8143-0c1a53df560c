version: '3'

services:

  # main bolna service
  bolna-app:
    build:
      context: .
      dockerfile: dockerfiles/bolna_server.Dockerfile
    image: bolna-app:latest  # This will tag the built image
    ports:
      - "5001:5001"
    depends_on:
      - redis
    env_file:
      - .env
    volumes:
      - ../agent_data:/app/agent_data
      - $HOME/.aws/credentials:/root/.aws/credentials:ro
      - $HOME/.aws/config:/root/.aws/config:ro

  # redis service used as a persistent storage
  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  # ngrok for local tunneling
  ngrok:
    image: ngrok/ngrok:latest
    restart: unless-stopped
    command:
      - "start"
      - "--all"
      - "--config"
      - "/etc/ngrok.yml"
    volumes:
      - ./ngrok-config.yml:/etc/ngrok.yml
    ports:
      - 4040:4040

  ### Telephony servers ###
  twilio-app:
    build:
      context: .
      dockerfile: dockerfiles/twilio_server.Dockerfile
    image: twilio-app:latest  # This will tag the built image
    ports:
      - "8001:8001"
    depends_on:
      - redis
      - ngrok
      - bolna-app
    env_file:
      - .env

  plivo-app:
    build:
      context: .
      dockerfile: dockerfiles/plivo_server.Dockerfile
    image: plivo-app:latest  # This will tag the built image
    ports:
      - "8002:8002"
    depends_on:
      - redis
      - ngrok
      - bolna-app
    env_file:
      - .env
